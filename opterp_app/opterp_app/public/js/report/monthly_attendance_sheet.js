frappe.query_reports["Monthly Attendance Sheet"] = {
    filters: [
		{
			fieldname: "month",
			label: __("Month"),
			fieldtype: "Select",
			reqd: 1,
			options: [
				{ value: 1, label: __("Jan") },
				{ value: 2, label: __("Feb") },
				{ value: 3, label: __("Mar") },
				{ value: 4, label: __("Apr") },
				{ value: 5, label: __("May") },
				{ value: 6, label: __("June") },
				{ value: 7, label: __("July") },
				{ value: 8, label: __("Aug") },
				{ value: 9, label: __("Sep") },
				{ value: 10, label: __("Oct") },
				{ value: 11, label: __("Nov") },
				{ value: 12, label: __("Dec") },
			],
			default: frappe.datetime.str_to_obj(frappe.datetime.get_today()).getMonth() + 1,
		},
		{
			fieldname: "year",
			label: __("Year"),
			fieldtype: "Select",
			reqd: 1,
		},
		{
			fieldname: "employee",
			label: __("Employee"),
			fieldtype: "Link",
			options: "Employee",
			get_query: () => {
				var company = frappe.query_report.get_filter_value("company");
				return {
					filters: {
						company: company,
					},
				};
			},
		},
		{
			fieldname: "company",
			label: __("Company"),
			fieldtype: "Link",
			options: "Company",
			default: frappe.defaults.get_user_default("Company"),
			reqd: 1,
		},
		{
			fieldname: "group_by",
			label: __("Group By"),
			fieldtype: "Select",
			options: ["", "Branch", "Grade", "Department", "Designation"],
		},
		{
			fieldname: "include_company_descendants",
			label: __("Include Company Descendants"),
			fieldtype: "Check",
			default: 1,
		},
		{
			fieldname: "summarized_view",
			label: __("Summarized View"),
			fieldtype: "Check",
			default: 0,
		},
	],
    formatter: function (value, row, column, data, default_formatter) {
        value = default_formatter(value, row, column, data);
        const summarized_view = frappe.query_report.get_filter_value("summarized_view");
        const group_by = frappe.query_report.get_filter_value("group_by");

        if (group_by && column.colIndex === 1) {
            value = "<strong>" + value + "</strong>";
        }

        if (!summarized_view) {
            if ((group_by && column.colIndex > 3) || (!group_by && column.colIndex > 2)) {

                if (value === "P" || value === "WFH")
                    value = "<span style='color:green'>" + value + "</span>";
                else if (value === "A")
                    value = "<span style='color:red'>" + value + "</span>";
                else if (value === "HD")
                    value = "<span style='color:orange'>" + value + "</span>";

                else if (value === "CL")
                    value = "<span style='color:purple; background-color:#eaf6ff; padding: 2px 4px; border-radius: 4px;'>" + value + "</span>";
                else if (value === "SL")
                    value = "<span style='color:darkred; background-color:#eaf6ff; padding: 2px 4px; border-radius: 4px;'>" + value + "</span>";
                else if (value === "PL")
                    value = "<span style='color:teal; background-color:#eaf6ff; padding: 2px 4px; border-radius: 4px;'>" + value + "</span>";
                else if (value === "LWP")
                    value = "<span style='color:gray; background-color:#eaf6ff; padding: 2px 4px; border-radius: 4px;'>" + value + "</span>";
                else if (value === "CO")
                    value = "<span style='color:brown; background-color:#eaf6ff; padding: 2px 4px; border-radius: 4px;'>" + value + "</span>";
                else if (value === "L")
                    value = "<span style='color:#318AD8; background-color:#eaf6ff; padding: 2px 4px; border-radius: 4px;'>" + value + "</span>";
            }
        }
        return value;
    },
    onload: function (report) {
		const columns_to_freeze = ["Employee Name"];
		const col_indexes_to_freeze = [];
		const col_sticky_offsets = {};
		
		setTimeout(() => {
			const dtInstance = report.datatable;
			if (!dtInstance) {
				console.warn("DataTable instance not found yet.");
				return;
			}

			const headers = dtInstance.datamanager.getColumns(true);

			let currentLeft = 0;
			headers.forEach((header) => {
				if (columns_to_freeze.includes(header.content)) {
					col_indexes_to_freeze.push(header.colIndex);
					col_sticky_offsets[header.colIndex] = currentLeft;
					currentLeft += header.width;
				}
			});

			const originalCellManager = dtInstance.rowmanager.cellmanager;
			const originalGetCellHTML = originalCellManager.getCellHTML.bind(originalCellManager);

			originalCellManager.getCellHTML = function(cell) {
				let cellHTML = originalGetCellHTML(cell);

				const baseStyle = 'background:#fff; box-sizing:border-box;';
				if (col_indexes_to_freeze.includes(cell.colIndex)) {
					const leftOffset = col_sticky_offsets[cell.colIndex] || 0;
					cellHTML = cellHTML.replace(
						/<div /,
						`<div style="position:sticky; left:${leftOffset}px; width:${cell.width}px; ${baseStyle} z-index:1;" `
					);
				}

				return cellHTML;
			};

			dtInstance.rowmanager.getRowHTML = function(row, props) {
				const dataAttr = Object.keys(props || {})
					.map(key => `data-${key}="${props[key]}"`)
					.join(' ');

				let rowIdentifier = props.rowIndex;

				if (props.isFilter) {
					row = row.map(cell => Object.assign({}, cell, {
						content: this.getFilterInput({
							colIndex: cell.colIndex,
							name: cell.name
						}),
						isFilter: 1,
						isHeader: undefined,
						editable: false
					}));
					rowIdentifier = 'filter';
				}

				if (props.isHeader) {
					rowIdentifier = 'header';
				}

				return `
					<div class="dt-row dt-row-${rowIdentifier}" ${dataAttr}>
						${row.map(cell => originalCellManager.getCellHTML(cell)).join('')}
					</div>
				`;
			};

			dtInstance.refresh();

			col_indexes_to_freeze.forEach((colIndex) => {
				$(`.dt-cell--header-${colIndex}`).css({
					'position': 'sticky',
					'left': `${col_sticky_offsets[colIndex]}px`,
					'z-index': '5',
					'background': '#fff'
				});
			});

			this.addCustomStyles();
        
        this.initializeFrozenColumns(report);
		}, 1000);

		return frappe.call({
			method: "hrms.hr.report.monthly_attendance_sheet.monthly_attendance_sheet.get_attendance_years",
			callback: function (r) {
				var year_filter = frappe.query_report.get_filter("year");
				year_filter.df.options = r.message;
				year_filter.df.default = r.message.split("\n")[0];
				year_filter.refresh();
				year_filter.set_input(year_filter.df.default);
			},
		});
		
	},

	    initializeFrozenColumns: function(report) {
        const maxRetries = 10;
        let retryCount = 0;

        const tryImplementation = () => {
            if (retryCount >= maxRetries) {
                console.warn("Failed to implement frozen columns after maximum retries");
                return;
            }
            
            const dtInstance = report.datatable;
            
            if (!dtInstance || !dtInstance.bodyScrollable || !dtInstance.header) {
                retryCount++;
                setTimeout(tryImplementation, 300);
                return;
            }
            
            this.implementFrozenColumns(report);
        };
        
        setTimeout(tryImplementation, 500);
    },

    implementFrozenColumns: function(report) {
        const dtInstance = report.datatable;
        
        if (!dtInstance || !dtInstance.bodyScrollable) {
            console.warn("DataTable instance not available");
            return;
        }

        const columnsToFreeze = [1,2];
        
        if (dtInstance._frozenScrollHandler) {
            dtInstance.bodyScrollable.removeEventListener('scroll', dtInstance._frozenScrollHandler);
        }
        
        dtInstance._frozenScrollHandler = (e) => {
            if (dtInstance._isUpdatingFrozen) return;
            
            dtInstance._isUpdatingFrozen = true;
            
            requestAnimationFrame(() => {
                const scrollLeft = e.target.scrollLeft;
                
                columnsToFreeze.forEach(colIndex => {
                    const headerCells = dtInstance.header.querySelectorAll(`.dt-cell--col-${colIndex}`);
                    headerCells.forEach(cell => {
                        cell.style.transform = `translateX(${scrollLeft}px)`;
                    });
                    
                    const bodyCells = dtInstance.bodyScrollable.querySelectorAll(`.dt-cell--col-${colIndex}`);
                    bodyCells.forEach(cell => {
                        cell.style.transform = `translateX(${scrollLeft}px)`;
                    });
                });
                
                dtInstance._isUpdatingFrozen = false;
            });
        };

        
        dtInstance.bodyScrollable.addEventListener('scroll', dtInstance._frozenScrollHandler, { passive: true });
        
        this.setupFrozenColumns(dtInstance, columnsToFreeze);
        },

    setupFrozenColumns: function(dtInstance, columnsToFreeze) {
        columnsToFreeze.forEach(colIndex => {
            const headerCells = dtInstance.header.querySelectorAll(`.dt-cell--col-${colIndex}`);
            headerCells.forEach(cell => {
                cell.classList.add('frozen-column', `frozen-col-${colIndex}`);
            });
            
            const bodyCells = dtInstance.bodyScrollable.querySelectorAll(`.dt-cell--col-${colIndex}`);
            bodyCells.forEach(cell => {
                cell.classList.add('frozen-column', `frozen-col-${colIndex}`);
            });
        });
        
        setTimeout(() => this.addFrozenInteractions(dtInstance, columnsToFreeze), 200);
    },

    addFrozenInteractions: function(dtInstance, columnsToFreeze) {
        const addRowHoverEffects = () => {
            const rows = dtInstance.bodyScrollable.querySelectorAll('.dt-row');
            
            rows.forEach(row => {
                row.removeEventListener('mouseenter', row._frozenHoverIn);
                row.removeEventListener('mouseleave', row._frozenHoverOut);
                
                row._frozenHoverIn = () => {
                    columnsToFreeze.forEach(colIndex => {
                        const cell = row.querySelector(`.dt-cell--col-${colIndex}`);
                        if (cell) {
                            cell.classList.add('frozen-hover');
                        }
                    });
                };
                
                row._frozenHoverOut = () => {
                    columnsToFreeze.forEach(colIndex => {
                        const cell = row.querySelector(`.dt-cell--col-${colIndex}`);
                        if (cell) {
                            cell.classList.remove('frozen-hover');
                        }
                    });
                };
                
                row.addEventListener('mouseenter', row._frozenHoverIn);
                row.addEventListener('mouseleave', row._frozenHoverOut);
            });
        };
        
        addRowHoverEffects();
    },

    addCustomStyles: function() {
        // Remove existing styles to prevent conflicts
        $('#attendance-report-frozen-styles').remove();
        
        const frozenStyles = `
            <style id="attendance-report-frozen-styles">
                /* Base table container */
                .dt-scrollable {
                    position: relative !important;
                    overflow-x: auto !important;
                    scroll-behavior: smooth !important;
                }

                /* Header container */
                .dt-header {
                    position: sticky !important;
                    top: 0 !important;
                    z-index: 100 !important;
                    background: #ffffff !important;
                    border-bottom: 2px solid #e9ecef !important;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.08) !important;
                }

                /* Frozen column base styles - simplified approach */
                .frozen-column {
                    position: relative !important;
                    background: #ffffff !important;
                    z-index: 10 !important;
                    box-shadow: 2px 0 4px rgba(0,0,0,0.1) !important;
                    border-right: 2px solid #dee2e6 !important;
                }

                /* Frozen header cells */
                .dt-header .frozen-column {
                    z-index: 110 !important;
                    background: #f8f9fa !important;
                    font-weight: 600 !important;
                    color: #495057 !important;
                }

                /* Frozen body cells */
                .dt-body .frozen-column {
                    z-index: 20 !important;
                    background: #ffffff !important;
                }

                /* Serial number column - let it keep its natural width */
                .frozen-col-0 {
                    text-align: center !important;
                    font-weight: 500 !important;
                    color: #6c757d !important;
                }

                .frozen-col-0 .dt-cell__content {
                    padding: 8px 4px !important;
                    justify-content: center !important;
                }

                /* Employee name column - let it keep its natural width */
                .frozen-col-1 .dt-cell__content {
                    padding: 8px 12px !important;
                    font-weight: 500 !important;
                    color: #212529 !important;
                    white-space: nowrap !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                }

                /* Hover effects */
                .frozen-hover {
                    background: #e3f2fd !important;
                    box-shadow: 3px 0 6px rgba(0,0,0,0.15) !important;
                }

                .dt-header .frozen-hover {
                    background: #e3f2fd !important;
                }

                /* Alternating row colors */
                .dt-row:nth-child(even) .dt-cell {
                    background: #f8f9fa !important;
                }

                .dt-row:nth-child(even) .frozen-column {
                    background: #f8f9fa !important;
                }

                .dt-row:nth-child(even) .frozen-hover {
                    background: #e3f2fd !important;
                }

                /* Status indicators */
                .dt-cell__content span[style*="color:red"] {
                    font-weight: 600 !important;
                    background: rgba(220, 53, 69, 0.1) !important;
                    padding: 2px 6px !important;
                    border-radius: 4px !important;
                    border: 1px solid rgba(220, 53, 69, 0.3) !important;
                }

                /* Scrollbar styling */
                .dt-scrollable::-webkit-scrollbar {
                    height: 8px !important;
                }

                .dt-scrollable::-webkit-scrollbar-track {
                    background: #f1f1f1 !important;
                    border-radius: 4px !important;
                }

                .dt-scrollable::-webkit-scrollbar-thumb {
                    background: #c1c1c1 !important;
                    border-radius: 4px !important;
                }

                .dt-scrollable::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8 !important;
                }

                /* Print styles */
                @media print {
                    .frozen-column {
                        position: static !important;
                        transform: none !important;
                        box-shadow: none !important;
                    }
                }
            </style>
        `;
        
        $('head').append(frozenStyles);
    },

    onDestroy: function() {
        $('#attendance-report-frozen-styles').remove();
        
        if (this.datatable && this.datatable._frozenScrollHandler) {
            this.datatable.bodyScrollable?.removeEventListener('scroll', this.datatable._frozenScrollHandler);
        }
    }
}

    