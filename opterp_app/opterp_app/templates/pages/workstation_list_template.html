{% set filter_fields = filter_fields or [] %}
{% set ignore_filter_fields = ignore_filter_fields or [] %}

{% set filters = filters or {} %}
{% set page_length = page_length or 25 %}

{% set meta = frappe.get_meta(doctype_name) %}
{% set filter_list = filter_list or None %}
{% set include_field = include_field or [] %}

{% if meta.get("filters") %}
    {% for filter in meta.filters %}
        {% set _ = filters.update({filter.fieldname: filter.default}) %}
    {% endfor %}
{% endif %}

{% set instances = frappe.get_list(doctype_name, filters=filter_list, fields=["*"], limit_page_length=page_length) %}
<div class="d-flex justify-content-between align-items-center mb-2 p-2 workstation-list">
    <h3 class="m-0">
        {{ doctype_name }} List

        {% if filter_list %}
        {% set filters = filter_list or {} %}
        <div class="position-relative d-inline-block ms-2 filter-badge-wrapper">
            <span class="badge rounded-pill bg-warning text-dark"
                style="opacity: 0.6; font-size: 10px; padding: 2px 6px;">
            {{ filter_list | length }} Filter{{ "s" if filter_list|length > 1 else "" }} Applied
            </span>

            <div class="filter-hover-box">
            {% for key, value in filters.items() %}
                <div class="filter-line">
                <span class="filter-key">{{ key }}:</span>
                <span class="filter-value">{{ value }}</span>
                </div>
            {% endfor %}
            </div>
        </div>
        {% endif %}

    </h3>
    <div class="d-flex flex-column align-items-end">
        <div class="d flex">
            <button class="btn btn-sm btn-secondary mb-1" style="margin-right: 5px;"
            onclick="window.workstationInstance.loadSection('{{ template }}', '{{ section }}', { doctype: '{{ doctype }}', filters: {{ filters|safe }} })">
            <i class="fa fa-refresh text-small text-muted" ></i>
            </button>
            <button class="btn btn-sm btn-secondary mb-1" 
            onclick="window.location.href='/app/{{ doctype_name | replace(' ', '-') | lower }}';">
                Advance List
            </button>

        </div>
        <div class="text-muted text-small">
            Showing {{ instances | length }} {{ doctype_name }}
        </div>
    </div>
</div>
<div id="list-filter-section" class="filter-flex-container"></div>

{% if instances %}
    <div class="frappe-list border rounded" style="max-height: 450px; overflow-y: auto; overflow-x: auto;">
        <div class="list-row-head text-muted d-flex border-bottom p-2 bg-light sticky-top" style="min-width: 800px; top: 0 ;justify-content: space-between;">
            {% for included_field in include_field%}
            <div class="list-header-subject text-truncate text-small" 
                     style="white-space: nowrap; min-width: 150px; flex: 0 0 150px ">
                    {{included_field | capitalize }}
            </div>
            {%endfor%}
            {% for field in meta.fields if field.in_list_view %}
                <div class="list-header-subject  text-truncate text-small" 
                     style="white-space: nowrap; min-width: 150px;flex: 0 0 150px;">
                    {{ field.label }}
                </div>
            {% endfor %}
        </div>

        <div class="list-row-container" style="min-width: 800px;">
            {% for instance in instances %}
                <div class="list-row d-flex align-items-center p-2 border-bottom"
                     style="cursor: pointer; transition: background 0.2s; white-space: nowrap;justify-content: space-between; "
                     onclick="window.workstationInstance.renderForm('{{ doctype_name }}', '{{ instance.name }}', '#content-section');">
                     {% for included_field in include_field%}
                        <div class="list-row-col  text-truncate" style="min-width: 150px;flex: 0 0 150px;margin-right: 0px;">{{instance[included_field]}}</div>
                    {% endfor %}
                    {% for field in meta.fields if field.in_list_view %}
                        {% set field_value = instance.get(field.fieldname, '') %}
                        <div class="list-row-col  text-truncate" style="min-width: 150px;flex: 0 0 150px; margin-right: 0px;">
                            {% if field.fieldtype == "Link" and field_value %}
                                <a href="javascript:void(0);" 
                                onclick="window.workstationInstacne.renderForm('{{field.options}}', '{{ field_value }}', '{{ field_value }}')" 
                                class="text-dark">
                                    {{ field_value }}
                                </a>
                            {% else %}
                                <span class="text text-muted">{{ field_value or "-" }}</span>
                            {% endif %}

                        </div>
                    {% endfor %}
                </div>
            {% endfor %}
        </div>
    </div>
{% else %}
<div class="card d-flex justify-content-center align-items-center mt-3" style="height: 25vh;">
    <p class="text-muted text-center m-0">No records found.</p>
</div>
{% endif %}

{% set total_instances = frappe.db.count(doctype_name) %}
{% set page_count = (total_instances // 25) + (1 if total_instances % 25 else 0) %}

<div class="d-flex justify-content-center mt-3 flex-column align-items-center" style="margin-left: 8rem; margin-right: 8rem;">
    <div style="max-width: 100%; overflow-x: auto; " class="d-flex flex-row flex-nowrap justify-content-start gap-1 px-2 py-4">
        {% for i in range(1, page_count + 1) %}
        <button class="btn btn-sm btn-outline-secondary gap-1 m-2"
            onclick="window.workstationInstance.loadSection('{{ template }}', '{{ section }}', {'doctype': '{{ doctype }}', 'filters': {{ filters }}, 'page_length': '{{ i * 25 }}'})">
            {{ i * 25 }}
        </button>
        {% endfor %}
    </div>
    <div class="text text-muted p-1">
        Total {{ total_instances }}
    </div>
</div>


<script>
function generateFilters(doctype) {
    const container = document.getElementById("list-filter-section");
    container.innerHTML = "";
    container.classList.add("filter-flex-container");

    const initialFilters = {{ filters | tojson | safe }};
    const filterState = { ...initialFilters };

    function haveFiltersChanged(newFilters, oldFilters) {
        const newKeys = Object.keys(newFilters);
        const oldKeys = Object.keys(oldFilters);
        if (newKeys.length !== oldKeys.length) return true;
        return newKeys.some(key => newFilters[key] !== oldFilters[key]);
    }

    function handleFilterChange(fieldname, control) {
        const value = control.get_value();

        if (value) {
            filterState[fieldname] = value;
        } else {
            delete filterState[fieldname];
        }

        if (haveFiltersChanged(filterState, initialFilters)) {
            window.workstationInstance.loadSection(
                "{{ template }}",
                "{{ section }}",
                {
                    doctype: "{{ doctype }}",
                    filters: filterState
                }
            );
        }
    }

    frappe.model.with_doctype(doctype, () => {
        const meta = frappe.get_meta(doctype);
        const filterable_fields = meta.fields.filter(df =>
            df.in_standard_filter || df.in_list_view
        );

        if (!filterable_fields.some(f => f.fieldname === 'name')) {
            filterable_fields.unshift({
                fieldname: 'name',
                label: 'ID',
                fieldtype: 'Data'
            });
        }

        filterable_fields.forEach(df => {
            const field_wrapper = document.createElement("div");
            field_wrapper.className = "filter-item";

            const control = frappe.ui.form.make_control({
                df: {
                    fieldtype: df.fieldtype,
                    fieldname: df.fieldname,
                    options: df.options || "",
                    label: (df.fieldtype === "Check") ? df.label : "",
                    placeholder: df.label,
                    input_class: "input-sm",
                    change: () => {
                        if (df.fieldtype !== "Data") {
                            handleFilterChange(df.fieldname, control);
                        }
                    }
                },
                parent: field_wrapper,
                render_input: true
            });

            if (["Date", "DateTime", "Time"].includes(df.fieldtype)) {
                setTimeout(() => {
                    control.$input.on("change", () => handleFilterChange(df.fieldname, control));
                });
            }

            if (df.fieldtype === "Data") {
                setTimeout(() => {
                    const wrapper = control.$wrapper;
                    const $flexWrap = $(`<div class="d-flex align-items-center"></div>`);
                    const $input = wrapper.find('input');
                    $flexWrap.append($input);

                    const $btn = $(`
                        <button class="btn btn-sm btn-outline-primary ms-2" type="button" title="Search">
                            <i class="fa fa-search"></i>
                        </button>
                    `);
                    $btn.on("click", () => handleFilterChange(df.fieldname, control));
                    $flexWrap.append($btn);

                    wrapper.empty().append($flexWrap);
                });
            }

            if (initialFilters[df.fieldname]) {
                control.set_value(initialFilters[df.fieldname]);
            }

            container.appendChild(field_wrapper);
        });
    });
}


(function() {
        if (document.querySelector('.workstation-list')) {
            generateFilters('{{ doctype_name }}');
        }
})();

</script>

<style>
.filter-flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1300;
}

.filter-item {
    flex: 1 1 calc(16.66% - 0.75rem);
    min-width: 140px;
    max-width: 240px;
}

@media (max-width: 1200px) {
    .filter-item {
        flex: 1 1 calc(25% - 0.75rem);
    }
}

@media (max-width: 768px) {
    .filter-item {
        flex: 1 1 calc(50% - 0.75rem);
    }
}

@media (max-width: 500px) {
    .filter-item {
        flex: 1 1 100%; 
    }
}

.filter-item .form-control {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
}

  .filter-badge-wrapper {
    position: relative;
    display: inline-block;
  }

  .filter-hover-box {
    display: none;
    position: absolute;
    top: 110%;
    left: 0;
    background: #edf0ead1;
    border: 1px solid #ddd;
    padding: 6px 10px;
    font-size: 11px;
    color: #656464;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: 3000;
    white-space: nowrap;
    min-width: max-content;
  }

  .filter-badge-wrapper:hover .filter-hover-box {
    display: block;
  }

  .filter-line {
    margin-bottom: 4px;
  }

  .filter-key {
    font-weight: 600;
    text-transform: capitalize;
    margin-right: 4px;
  }

  .filter-value {
    color: #555;
  }
</style>
 