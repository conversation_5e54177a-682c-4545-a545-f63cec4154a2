// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Attendance Sheet"] = {
    filters: [
        {
            fieldname: "from_date",
            label: __("From Date"),
            fieldtype: "Date",
            reqd: 1,
            default: frappe.datetime.month_start(),
        },
        {
            fieldname: "to_date",
            label: __("To Date"),
            fieldtype: "Date",
            reqd: 1,
            default: frappe.datetime.month_end(),
        },
        {
            fieldname: "employee",
            label: __("Employee"),
            fieldtype: "<PERSON>",
            options: "Employee",
        },
        {
            fieldname: "shift",
            label: __("Shift Type"),
            fieldtype: "Link",
            options: "Shift Type",
        },
        {
            fieldname: "department",
            label: __("Department"),
            fieldtype: "Link",
            options: "Department",
        },
        {
            fieldname: "company",
            label: __("Company"),
            fieldtype: "Link",
            options: "Company",
            reqd: 1,
            default: frappe.defaults.get_user_default("Company"),
        },
        {
            fieldname: "late_entry",
            label: __("Late Entry"),
            fieldtype: "Check",
        },
        {
            fieldname: "early_exit",
            label: __("Early Exit"),
            fieldtype: "Check",
        },
        {
            fieldname: "consider_grace_period",
            label: __("Consider Grace Period"),
            fieldtype: "Check",
            default: 1,
            hidden: 1,
        },
    ],

    formatter: (value, row, column, data, default_formatter) => {
        value = default_formatter(value, row, column, data);
        if (
            (column.fieldname === "in_time" && data?.late_entry) ||
            (column.fieldname === "out_time" && data?.early_exit)
        ) {
            return `<span style="color:red!important;">${value}</span>`;
        }
        return value;
    },

    onload: function(report) {
        // Add custom CSS immediately
        this.addCustomStyles();
        
        // Initialize frozen columns with retry mechanism
        this.initializeFrozenColumns(report);
    },

    initializeFrozenColumns: function(report) {
        const maxRetries = 10;
        let retryCount = 0;

        // time out used
        
        const tryImplementation = () => {
            if (retryCount >= maxRetries) {
                console.warn("Failed to implement frozen columns after maximum retries");
                return;
            }
            
            const dtInstance = report.datatable;
            
            if (!dtInstance || !dtInstance.bodyScrollable || !dtInstance.header) {
                retryCount++;
                setTimeout(tryImplementation, 300);
                return;
            }
            
            this.implementFrozenColumns(report);
        };
        
        setTimeout(tryImplementation, 500);
    },

    implementFrozenColumns: function(report) {
        const dtInstance = report.datatable;
        
        if (!dtInstance || !dtInstance.bodyScrollable) {
            console.warn("DataTable instance not available");
            return;
        }

        console.log("Implementing frozen columns...");

        const columnsToFreeze = [1,2];
        
        if (dtInstance._frozenScrollHandler) {
            dtInstance.bodyScrollable.removeEventListener('scroll', dtInstance._frozenScrollHandler);
        }
        
        dtInstance._frozenScrollHandler = (e) => {
            if (dtInstance._isUpdatingFrozen) return;
            
            dtInstance._isUpdatingFrozen = true;
            
            requestAnimationFrame(() => {
                const scrollLeft = e.target.scrollLeft;
                
                columnsToFreeze.forEach(colIndex => {
                    const headerCells = dtInstance.header.querySelectorAll(`.dt-cell--col-${colIndex}`);
                    headerCells.forEach(cell => {
                        cell.style.transform = `translateX(${scrollLeft}px)`;
                    });
                    
                    const bodyCells = dtInstance.bodyScrollable.querySelectorAll(`.dt-cell--col-${colIndex}`);
                    bodyCells.forEach(cell => {
                        cell.style.transform = `translateX(${scrollLeft}px)`;
                    });
                });
                
                dtInstance._isUpdatingFrozen = false;
            });
        };

        
        dtInstance.bodyScrollable.addEventListener('scroll', dtInstance._frozenScrollHandler, { passive: true });
        
        this.setupFrozenColumns(dtInstance, columnsToFreeze);
        
        console.log("Frozen columns implemented successfully");
    },

    setupFrozenColumns: function(dtInstance, columnsToFreeze) {
        columnsToFreeze.forEach(colIndex => {
            const headerCells = dtInstance.header.querySelectorAll(`.dt-cell--col-${colIndex}`);
            headerCells.forEach(cell => {
                cell.classList.add('frozen-column', `frozen-col-${colIndex}`);
            });
            
            const bodyCells = dtInstance.bodyScrollable.querySelectorAll(`.dt-cell--col-${colIndex}`);
            bodyCells.forEach(cell => {
                cell.classList.add('frozen-column', `frozen-col-${colIndex}`);
            });
        });
        
        setTimeout(() => this.addFrozenInteractions(dtInstance, columnsToFreeze), 200);
    },

    addFrozenInteractions: function(dtInstance, columnsToFreeze) {
        const addRowHoverEffects = () => {
            const rows = dtInstance.bodyScrollable.querySelectorAll('.dt-row');
            
            rows.forEach(row => {
                row.removeEventListener('mouseenter', row._frozenHoverIn);
                row.removeEventListener('mouseleave', row._frozenHoverOut);
                
                row._frozenHoverIn = () => {
                    columnsToFreeze.forEach(colIndex => {
                        const cell = row.querySelector(`.dt-cell--col-${colIndex}`);
                        if (cell) {
                            cell.classList.add('frozen-hover');
                        }
                    });
                };
                
                row._frozenHoverOut = () => {
                    columnsToFreeze.forEach(colIndex => {
                        const cell = row.querySelector(`.dt-cell--col-${colIndex}`);
                        if (cell) {
                            cell.classList.remove('frozen-hover');
                        }
                    });
                };
                
                row.addEventListener('mouseenter', row._frozenHoverIn);
                row.addEventListener('mouseleave', row._frozenHoverOut);
            });
        };
        
        addRowHoverEffects();
    },

    addCustomStyles: function() {
        // Remove existing styles to prevent conflicts
        $('#attendance-report-frozen-styles').remove();
        
        const frozenStyles = `
            <style id="attendance-report-frozen-styles">
                /* Base table container */
                .dt-scrollable {
                    position: relative !important;
                    overflow-x: auto !important;
                    scroll-behavior: smooth !important;
                }

                /* Header container */
                .dt-header {
                    position: sticky !important;
                    top: 0 !important;
                    z-index: 100 !important;
                    background: #ffffff !important;
                    border-bottom: 2px solid #e9ecef !important;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.08) !important;
                }

                /* Frozen column base styles - simplified approach */
                .frozen-column {
                    position: relative !important;
                    background: #ffffff !important;
                    z-index: 10 !important;
                    box-shadow: 2px 0 4px rgba(0,0,0,0.1) !important;
                    border-right: 2px solid #dee2e6 !important;
                }

                /* Frozen header cells */
                .dt-header .frozen-column {
                    z-index: 110 !important;
                    background: #f8f9fa !important;
                    font-weight: 600 !important;
                    color: #495057 !important;
                }

                /* Frozen body cells */
                .dt-body .frozen-column {
                    z-index: 20 !important;
                    background: #ffffff !important;
                }

                /* Serial number column - let it keep its natural width */
                .frozen-col-0 {
                    text-align: center !important;
                    font-weight: 500 !important;
                    color: #6c757d !important;
                }

                .frozen-col-0 .dt-cell__content {
                    padding: 8px 4px !important;
                    justify-content: center !important;
                }

                /* Employee name column - let it keep its natural width */
                .frozen-col-1 .dt-cell__content {
                    padding: 8px 12px !important;
                    font-weight: 500 !important;
                    color: #212529 !important;
                    white-space: nowrap !important;
                    overflow: hidden !important;
                    text-overflow: ellipsis !important;
                }

                /* Hover effects */
                .frozen-hover {
                    background: #e3f2fd !important;
                    box-shadow: 3px 0 6px rgba(0,0,0,0.15) !important;
                }

                .dt-header .frozen-hover {
                    background: #e3f2fd !important;
                }

                /* Alternating row colors */
                .dt-row:nth-child(even) .dt-cell {
                    background: #f8f9fa !important;
                }

                .dt-row:nth-child(even) .frozen-column {
                    background: #f8f9fa !important;
                }

                .dt-row:nth-child(even) .frozen-hover {
                    background: #e3f2fd !important;
                }

                /* Status indicators */
                .dt-cell__content span[style*="color:red"] {
                    font-weight: 600 !important;
                    background: rgba(220, 53, 69, 0.1) !important;
                    padding: 2px 6px !important;
                    border-radius: 4px !important;
                    border: 1px solid rgba(220, 53, 69, 0.3) !important;
                }

                /* Scrollbar styling */
                .dt-scrollable::-webkit-scrollbar {
                    height: 8px !important;
                }

                .dt-scrollable::-webkit-scrollbar-track {
                    background: #f1f1f1 !important;
                    border-radius: 4px !important;
                }

                .dt-scrollable::-webkit-scrollbar-thumb {
                    background: #c1c1c1 !important;
                    border-radius: 4px !important;
                }

                .dt-scrollable::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8 !important;
                }

                /* Print styles */
                @media print {
                    .frozen-column {
                        position: static !important;
                        transform: none !important;
                        box-shadow: none !important;
                    }
                }
            </style>
        `;
        
        $('head').append(frozenStyles);
    },

    onDestroy: function() {
        $('#attendance-report-frozen-styles').remove();
        
        if (this.datatable && this.datatable._frozenScrollHandler) {
            this.datatable.bodyScrollable?.removeEventListener('scroll', this.datatable._frozenScrollHandler);
        }
    }
};