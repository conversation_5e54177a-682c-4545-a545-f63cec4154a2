import hrms.hr.report.monthly_attendance_sheet.monthly_attendance_sheet as monthly_attendance_report
import frappe
from frappe.utils import cstr
from hrms.hr.report.monthly_attendance_sheet.monthly_attendance_sheet import (
	get_total_days_in_month,
	get_holiday_status,
)
from typing import List

_original_execute = monthly_attendance_report.execute

status_map = {
	"Present": "P",
	"Absent": "A",
	"Half Day": "HD",
	"Work From Home": "WFH",
	"On Leave": "L",
	"Holiday": "H",
	"Weekly Off": "WO",
}

def custom_get_message() -> str:
	message = ""
	colors = ["green", "red", "orange", "green", "#318AD8", "", ""]
	for count, (status, abbr) in enumerate(status_map.items()):
		message += f"""
			<span style='border-left: 2px solid {colors[count]}; padding-right: 12px; padding-left: 5px; margin-right: 3px;'>
				{status} - {abbr}
			</span>
		"""
	return message


def custom_execute(filters=None):
	columns, data, message, chart = _original_execute(filters)

	for row in data:
		for key, val in row.items():
			if val == "L" and key.isdigit():
				employee = row.get("employee")
				month = int(filters["month"])
				year = int(filters["year"])
				day = int(key)

				date_str = f"{year}-{month:02d}-{day:02d}"

				leave_type = frappe.db.get_value(
					"Attendance",
					{
						"employee": employee,
						"attendance_date": date_str,
						"status": "On Leave"
					},
					"leave_type"
				)

				if leave_type:
					initials = "".join([part[0].upper() for part in leave_type.split()])
					row[key] = initials

	return columns, data, message, chart

def custom_get_attendance_status_for_detailed_view(
	employee: str, filters, employee_attendance: dict, holidays: list
) -> List[dict]:
	total_days = get_total_days_in_month(filters)
	attendance_values = []

	for shift, status_dict in employee_attendance.items():
		row = {"shift": shift}

		for day in range(1, total_days + 1):
			status = status_dict.get(day)
			if status is None and holidays:
				status = get_holiday_status(day, holidays)

			abbr = status_map.get(status, "")
			row[cstr(day)] = abbr

		attendance_values.append(row)

	return attendance_values

monthly_attendance_report.execute = custom_execute
monthly_attendance_report.get_message = custom_get_message
monthly_attendance_report.get_attendance_status_for_detailed_view = custom_get_attendance_status_for_detailed_view
