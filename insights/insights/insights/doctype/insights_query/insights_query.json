{"actions": [], "autoname": "format:QRY-{####}", "creation": "2022-01-29 14:40:51.105176", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["tab_break_1", "status", "title", "is_native_query", "is_assisted_query", "is_saved_as_table", "is_script_query", "column_break_3", "data_source", "chart", "is_stored", "section_break_3", "tables", "column_break_4", "columns", "section_break_4", "filters", "section_break_2", "limit", "section_break_cnma", "json", "sql", "section_break_byvw", "script", "script_log", "section_break_qcue", "execution_time", "result_name", "results_row_count", "column_break_dluz", "last_execution", "transform_tab", "section_break_18", "transforms", "variables"], "fields": [{"depends_on": "eval: !doc.is_native_query", "fieldname": "section_break_2", "fieldtype": "Section Break"}, {"depends_on": "eval: !doc.is_assisted_query && !doc.is_native_query && !doc.is_script_query", "fieldname": "columns", "fieldtype": "Table", "label": "Columns", "options": "Insights Query Column"}, {"depends_on": "eval: !doc.is_native_query", "fieldname": "section_break_4", "fieldtype": "Section Break"}, {"default": "{\n\t\"type\": \"LogicalExpression\",\n\t\"operator\": \"&&\",\n\t\"level\": 1,\n\t\"position\": 1,\n\t\"conditions\": []\n}", "depends_on": "eval: !doc.is_assisted_query && !doc.is_native_query && !doc.is_script_query", "fieldname": "filters", "fieldtype": "JSON", "label": "Filters"}, {"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"default": "500", "depends_on": "eval: !doc.is_assisted_query && !doc.is_native_query && !doc.is_script_query", "fieldname": "limit", "fieldtype": "Int", "label": "Limit"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "sql", "fieldtype": "Code", "label": "SQL", "read_only": 1}, {"fieldname": "tab_break_1", "fieldtype": "Tab Break", "label": "Query"}, {"depends_on": "eval: !doc.is_assisted_query && !doc.is_native_query && !doc.is_script_query", "fieldname": "tables", "fieldtype": "Table", "label": "Tables", "options": "Insights Query Table"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.is_assisted_query && !doc.is_native_query", "fieldname": "section_break_3", "fieldtype": "Section Break"}, {"fieldname": "data_source", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Data Source", "options": "Insights Data Source", "search_index": 1}, {"fieldname": "transform_tab", "fieldtype": "Tab Break", "label": "Transform"}, {"fieldname": "section_break_18", "fieldtype": "Section Break"}, {"fieldname": "execution_time", "fieldtype": "Float", "label": "Execution Time (seconds)", "read_only": 1}, {"fieldname": "last_execution", "fieldtype": "Datetime", "label": "Last Executed On", "read_only": 1}, {"default": "Pending Execution", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Pending Execution\nExecution Successful\nExecution Failed", "read_only": 1}, {"default": "0", "fieldname": "is_stored", "fieldtype": "Check", "label": "Stored"}, {"fieldname": "transforms", "fieldtype": "Table", "label": "Transforms", "options": "Insights Query Transform"}, {"default": "0", "fieldname": "is_native_query", "fieldtype": "Check", "label": "Is Native Query"}, {"default": "0", "fieldname": "is_assisted_query", "fieldtype": "Check", "label": "Is Assisted Query"}, {"fieldname": "section_break_cnma", "fieldtype": "Section Break"}, {"default": "{}", "depends_on": "eval: doc.is_assisted_query", "fieldname": "json", "fieldtype": "Code", "label": "JSON", "options": "JSON"}, {"default": "0", "fieldname": "is_saved_as_table", "fieldtype": "Check", "is_virtual": 1, "label": "Is Saved as Table"}, {"default": "0", "fieldname": "is_script_query", "fieldtype": "Check", "label": "<PERSON> Script Query"}, {"depends_on": "eval: doc.is_script_query", "fieldname": "script", "fieldtype": "Code", "label": "<PERSON><PERSON><PERSON>", "options": "Python"}, {"fieldname": "variables", "fieldtype": "Table", "label": "Variables", "no_copy": 1, "options": "Insights Query Variable"}, {"fieldname": "script_log", "fieldtype": "Text", "label": "Script Log", "read_only": 1}, {"fieldname": "chart", "fieldtype": "Link", "label": "Chart", "no_copy": 1, "options": "Insights Chart"}, {"fieldname": "section_break_qcue", "fieldtype": "Section Break"}, {"fieldname": "column_break_dluz", "fieldtype": "Column Break"}, {"fieldname": "section_break_byvw", "fieldtype": "Section Break"}, {"fieldname": "result_name", "fieldtype": "Data", "is_virtual": 1, "label": "Result", "read_only": 1}, {"fieldname": "results_row_count", "fieldtype": "Int", "is_virtual": 1, "label": "Result Row Count", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-20 14:05:43.181668", "modified_by": "Administrator", "module": "Insights", "name": "Insights Query", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights User", "share": 1, "write": 1}], "search_fields": "status", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1, "track_views": 1}