{"actions": [], "allow_rename": 1, "autoname": "field:team_name", "beta": 1, "creation": "2022-12-22 20:18:03.581245", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["team_name", "section_break_4", "team_members", "team_permissions"], "fields": [{"fieldname": "team_name", "fieldtype": "Data", "in_list_view": 1, "label": "Team Name", "reqd": 1, "unique": 1}, {"fieldname": "team_members", "fieldtype": "Table", "label": "Team Members", "options": "Insights Team Member"}, {"fieldname": "team_permissions", "fieldtype": "Table", "label": "Team Permissions", "options": "Insights Resource Permission"}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-11-02 12:13:37.959366", "modified_by": "Administrator", "module": "Insights", "name": "Insights Team", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights Admin", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights User", "share": 1}], "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "team_name", "track_changes": 1}