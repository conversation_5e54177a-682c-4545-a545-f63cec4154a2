{"actions": [], "autoname": "autoincrement", "creation": "2024-05-28 11:49:18.335351", "doctype": "DocType", "engine": "InnoDB", "field_order": ["title", "data_backup"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"fieldname": "data_backup", "fieldtype": "JSON", "hidden": 1, "label": "Data Backup", "read_only": 1}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Insights Query v3", "link_fieldname": "workbook"}, {"link_doctype": "Insights Chart v3", "link_fieldname": "workbook"}, {"link_doctype": "Insights Dashboard v3", "link_fieldname": "workbook"}], "modified": "2025-04-11 17:26:45.109680", "modified_by": "Administrator", "module": "Insights", "name": "Insights Workbook", "naming_rule": "Autoincrement", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights Admin", "share": 1, "write": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}