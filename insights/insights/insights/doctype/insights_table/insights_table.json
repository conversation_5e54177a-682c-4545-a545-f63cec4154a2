{"actions": [], "creation": "2022-05-16 06:02:19.601702", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["table", "label", "is_query_based", "column_break_3", "data_source", "hidden", "section_break_6", "columns", "links_section", "table_links"], "fields": [{"fieldname": "data_source", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Data Source", "options": "Insights Data Source", "reqd": 1, "search_index": 1}, {"fieldname": "table", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Table", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "label", "fieldtype": "Data", "label": "Label", "reqd": 1}, {"fieldname": "links_section", "fieldtype": "Section Break"}, {"fieldname": "table_links", "fieldtype": "Table", "label": "Links", "options": "Insights Table Link"}, {"default": "0", "fieldname": "hidden", "fieldtype": "Check", "label": "Hidden"}, {"fieldname": "columns", "fieldtype": "Table", "label": "Columns", "options": "Insights Table Column"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "is_query_based", "fieldtype": "Check", "label": "Is Query Based"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-10-09 20:00:47.546059", "modified_by": "Administrator", "module": "Insights", "name": "Insights Table", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights User", "share": 1, "write": 1}], "search_fields": "label, data_source", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "label"}