{"actions": [], "allow_rename": 1, "creation": "2022-06-30 17:59:52.181464", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["onboarding_section", "enable_permissions", "enable_data_store", "apply_user_permissions", "allowed_origins", "max_records_to_sync", "max_memory_usage", "max_execution_time", "integrations_section", "telegram_api_token", "query_section", "fiscal_year_start", "week_starts_on", "tab_break_tvwi", "setup_complete", "onboarding_complete", "allow_subquery", "auto_execute_query", "query_result_expiry", "query_result_limit"], "fields": [{"default": "0", "fieldname": "setup_complete", "fieldtype": "Check", "label": "Setup Complete"}, {"fieldname": "onboarding_section", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "onboarding_complete", "fieldtype": "Check", "label": "Onboarding Complete"}, {"default": "0", "fieldname": "auto_execute_query", "fieldtype": "Check", "label": "Auto Execute Query"}, {"default": "10", "fieldname": "query_result_expiry", "fieldtype": "Int", "label": "<PERSON><PERSON> Query Results For (Minutes)"}, {"default": "0", "fieldname": "allow_subquery", "fieldtype": "Check", "label": "Allow Subquery"}, {"default": "1000", "fieldname": "query_result_limit", "fieldtype": "Int", "label": "Query Result Limit"}, {"default": "0", "fieldname": "enable_permissions", "fieldtype": "Check", "label": "Enable Permissions"}, {"fieldname": "integrations_section", "fieldtype": "Section Break", "label": "Integrations"}, {"fieldname": "telegram_api_token", "fieldtype": "Password", "label": "Telegram API Token"}, {"fieldname": "query_section", "fieldtype": "Section Break", "label": "Query"}, {"fieldname": "fiscal_year_start", "fieldtype": "Date", "label": "Fiscal Year Start"}, {"description": "Comma seperated URLs to whitelist for embedding charts & dashboards", "fieldname": "allowed_origins", "fieldtype": "Data", "label": "Allowed Origins"}, {"fieldname": "max_records_to_sync", "fieldtype": "Int", "label": "Max Records To Sync", "non_negative": 1}, {"default": "Monday", "fieldname": "week_starts_on", "fieldtype": "Select", "label": "Week Starts On", "options": "Monday\nTuesday\nWednesday\nThursday\nFriday\nSaturday\nSunday"}, {"fieldname": "tab_break_tvwi", "fieldtype": "Tab Break", "label": "Legacy"}, {"fieldname": "max_memory_usage", "fieldtype": "Int", "label": "Max Memory Usage", "non_negative": 1}, {"default": "0", "fieldname": "enable_data_store", "fieldtype": "Check", "label": "Enable Data Store"}, {"default": "0", "fieldname": "apply_user_permissions", "fieldtype": "Check", "label": "Apply User Permissions"}, {"fieldname": "max_execution_time", "fieldtype": "Int", "label": "Max Execution Time (Seconds)"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-01-14 13:17:25.798141", "modified_by": "Administrator", "module": "Insights", "name": "Insights Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Insights Admin", "share": 1, "write": 1}, {"email": 1, "print": 1, "read": 1, "role": "Insights User", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}