{"actions": [], "autoname": "field:title", "creation": "2022-02-23 06:14:40.960055", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "status", "database_type", "is_site_db", "host", "port", "use_ssl", "allow_imports", "column_break_5", "database_name", "username", "password", "section_break_j5ez", "connection_string"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1, "unique": 1}, {"depends_on": "eval: !doc.is_site_db", "fieldname": "port", "fieldtype": "Data", "label": "Port"}, {"depends_on": "eval: !doc.is_site_db", "fieldname": "database_name", "fieldtype": "Data", "label": "Database Name"}, {"depends_on": "eval: !doc.is_site_db", "fieldname": "username", "fieldtype": "Data", "label": "Username"}, {"depends_on": "eval: !doc.is_site_db", "fieldname": "password", "fieldtype": "Password", "label": "Password"}, {"default": "0", "depends_on": "eval: !doc.is_site_db", "fieldname": "use_ssl", "fieldtype": "Check", "label": "Use SSL"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.is_site_db", "fieldname": "host", "fieldtype": "Data", "label": "Host"}, {"fieldname": "status", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "label": "Status", "options": "Inactive\nActive", "read_only": 1}, {"default": "MariaDB", "depends_on": "eval: !doc.is_site_db", "fieldname": "database_type", "fieldtype": "Select", "in_list_view": 1, "label": "Database Type", "options": "MariaDB\nPostgreSQL\nSQLite"}, {"default": "0", "depends_on": "is_site_db", "fieldname": "is_site_db", "fieldtype": "Check", "label": "Site Database", "read_only": 1}, {"default": "0", "fieldname": "allow_imports", "fieldtype": "Check", "label": "Allow Table Import"}, {"fieldname": "section_break_j5ez", "fieldtype": "Section Break"}, {"fieldname": "connection_string", "fieldtype": "Small Text", "label": "Connection String"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-05-29 14:25:59.190544", "modified_by": "Administrator", "module": "Insights", "name": "Insights Data Source", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights User", "share": 1, "write": 1}], "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}