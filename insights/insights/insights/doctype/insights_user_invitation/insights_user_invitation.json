{"actions": [], "autoname": "autoincrement", "creation": "2024-08-31 19:36:59.759731", "doctype": "DocType", "engine": "InnoDB", "field_order": ["email", "key", "invited_by", "status", "column_break_tlag", "email_sent_at", "accepted_at"], "fields": [{"fieldname": "email", "fieldtype": "Data", "in_list_view": 1, "label": "Email", "reqd": 1}, {"fieldname": "key", "fieldtype": "Data", "label": "Key"}, {"fieldname": "invited_by", "fieldtype": "Link", "label": "Invited By", "options": "User"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Pending\nAccepted\nExpired"}, {"fieldname": "column_break_tlag", "fieldtype": "Column Break"}, {"fieldname": "email_sent_at", "fieldtype": "Datetime", "label": "<PERSON><PERSON>"}, {"fieldname": "accepted_at", "fieldtype": "Datetime", "label": "Accepted At"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-11-05 13:59:33.681447", "modified_by": "Administrator", "module": "Insights", "name": "Insights User Invitation", "naming_rule": "Autoincrement", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights User", "share": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Insights Admin", "share": 1, "write": 1}], "show_title_field_in_link": 1, "sort_field": "creation", "sort_order": "DESC", "states": [{"color": "Green", "title": "Accepted"}, {"color": "Red", "title": "Expired"}, {"color": "Orange", "title": "Pending"}], "title_field": "email"}