[pre_model_sync]
insights.patches.rename_doctypes
insights.patches.convert_duration_to_float
execute:frappe.delete_doc('DocType', 'Query Filter', ignore_missing=True)
execute:frappe.delete_doc('DocType', 'Query Field', ignore_missing=True)
execute:frappe.delete_doc('DocType', 'Query Chart', ignore_missing=True)
insights.patches.add_roles
insights.insights.doctype.insights_query.patches.make_query_variable_value_password_field
insights.patches.normalize_workbook #6

[post_model_sync]
insights.patches.add_position_key_to_filter
insights.patches.add_last_execution_field
insights.patches.rename_like_to_contains
insights.patches.reset_query_filters
insights.patches.refresh_tables #v5
insights.patches.modify_dashboard_layout
insights.patches.fix_select_options_after_rename
insights.patches.rename_column_type #v2
insights.patches.rename_visualization
insights.patches.store_queries #v3
insights.patches.rename_count_column_name #v4
insights.patches.add_column_row_to_result
insights.patches.rename_data_to_config
insights.patches.migrate_dashboard_charts
insights.patches.rename_target_column_field
execute:frappe.db.sql("update `tabInsights Table Import` set `data_source`='Site DB' where `data_source` is null")
insights.patches.replace_demo_data_source
execute:frappe.db.sql("update `tabInsights Query` set `status`='Pending Execution'")
execute:frappe.db.sql("update `tabInsights Query Column` set `format_option`='{}' where `type` not in ('Date', 'Datetime', 'Time')")
execute:frappe.db.sql("""update `tabInsights Query Table` set `join`=replace(`join`, '"Right"', '"Inner"') where `join` like '%Right%'""")
execute:frappe.db.sql("""update `tabInsights Query Table` set `join`=replace(`join`, '"right"', '"inner"') where `join` like '%right%'""")
insights.patches.modify_join_condition
insights.patches.make_filter_links #v3
insights.patches.replace_pivot_column_with_label #2
insights.patches.refactor_dashboard_filter
insights.patches.refactor_dashboard_item #17
insights.patches.make_query_tables
insights.patches.show_support_login_message
insights.insights.doctype.insights_table.patches.delete_duplicate_records
insights.insights.doctype.insights_query.patches.rename_untitled_query_to_query_name
insights.insights.doctype.insights_table.patches.delete_unused_query_based_tables
insights.insights.doctype.insights_query.patches.flatten_columns_in_query_json
# insights.insights.doctype.insights_query.patches.migrate_old_query_to_new_query_structure
insights.insights.doctype.insights_table.patches.sync_table_links
insights.insights.doctype.insights_notebook_page.patches.replace_query_builder_with_editor
insights.insights.doctype.insights_query.patches.set_chart_name
insights.insights.doctype.insights_chart.patches.convert_bar_to_row_chart
insights.insights.doctype.insights_data_source_v3.patches.copy_data_sources #v4
insights.insights.doctype.insights_table_v3.patches.force_sync_tables #5
insights.patches.enable_data_store
