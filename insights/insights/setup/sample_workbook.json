{"version": "1.0", "timestamp": "2025-05-21 20:00:35.426564", "type": "Workbook", "name": 10, "doc": {"name": 10, "title": "Order Analysis"}, "dependencies": {"queries": {"sj4rgk0g8v": {"name": "sj4rgk0g8v", "title": "Undelivered Orders", "workbook": "10", "use_live_connection": 1, "is_script_query": 0, "is_builder_query": 0, "is_native_query": 0, "operations": [{"table": {"data_source": "demo_data", "table_name": "orders", "type": "table"}, "type": "source"}, {"join_condition": {"left_column": {"column_name": "order_id", "type": "column"}, "right_column": {"column_name": "order_id", "type": "column"}}, "join_type": "left", "table": {"data_source": "demo_data", "table_name": "orderitems", "type": "table"}, "type": "join"}, {"column": {"column_name": "order_purchase_timestamp", "type": "column"}, "data_type": "Datetime", "type": "cast"}, {"filters": [{"column": {"column_name": "order_status", "type": "column"}, "operator": "not_in", "value": ["delivered"]}, {"column": {"column_name": "order_id", "type": "column"}, "operator": "is_set"}, {"column": {"column_name": "customer_id", "type": "column"}, "operator": "is_set"}, {"expression": {"expression": "(order_purchase_timestamp >= '2016-10-01') & (order_purchase_timestamp <= '2018-08-31')", "type": "expression"}}], "logical_operator": "And", "type": "filter_group"}, {"data_type": "Decimal", "expression": {"expression": "price * freight_value", "type": "expression"}, "new_name": "order_value", "type": "mutate"}, {"join_condition": {"left_column": {"column_name": "product_id", "type": "column"}, "right_column": {"column_name": "product_id", "type": "column"}}, "join_type": "left", "select_columns": [{"column_name": "product_category_name", "type": "column"}], "table": {"data_source": "demo_data", "table_name": "products", "type": "table"}, "type": "join"}]}}, "charts": {"sj4r5sn1ro": {"name": "sj4r5sn1ro", "title": "Order per Month", "workbook": "10", "query": "sj4rgk0g8v", "chart_type": "Bar", "config": {"filters": {"filters": [], "logical_operator": "And"}, "grouping": "stacked", "limit": "100", "normalize": false, "order_by": [{"column": {"column_name": "order_purchase_timestamp", "type": "column"}, "direction": "asc"}], "show_data_labels": false, "split_by": {"column_name": "", "data_type": "String", "dimension_name": ""}, "stack": false, "swap_axes": false, "x_axis": {"dimension": {"column_name": "order_purchase_timestamp", "data_type": "Datetime", "dimension_name": "order_purchase_timestamp", "granularity": "month", "label": "order_purchase_timestamp", "value": "order_purchase_timestamp"}}, "y2_axis": null, "y2_axis_type": "line", "y_axis": {"normalize": false, "series": [{"measure": {"aggregation": "sum", "column_name": "price", "data_type": "Decimal", "label": "sum_of_price", "measure_name": "sum_of_price", "value": "sum_of_price"}}], "show_axis_label": false, "show_data_labels": false, "stack": false}}}, "sj4rdmcvpa": {"name": "sj4rdmcvpa", "title": "Order per Status", "workbook": "10", "query": "sj4rgk0g8v", "chart_type": "Donut", "config": {"filters": {"filters": [], "logical_operator": "And"}, "label_column": {"column_name": "order_status", "data_type": "String", "dimension_name": "order_status", "label": "order_status", "value": "order_status"}, "limit": 100, "order_by": [], "value_column": {"aggregation": "sum", "column_name": "price", "data_type": "Decimal", "measure_name": "sum_of_price"}}}, "sj4rjuvvpu": {"name": "sj4rjuvvpu", "title": "Top Level Metrics", "workbook": "10", "query": "sj4rgk0g8v", "chart_type": "Number", "config": {"comparison": true, "date_column": {"column_name": "order_purchase_timestamp", "data_type": "Datetime", "dimension_name": "Date", "granularity": "month", "label": "order_purchase_timestamp", "value": "order_purchase_timestamp"}, "decimal": "2", "filters": {"filters": [], "logical_operator": "And"}, "limit": 100, "negative_is_better": false, "number_column": ["price", "freight_value"], "number_column_options": [], "number_columns": [{"aggregation": "count", "column_name": "order_id", "data_type": "String", "label": "count(*)", "measure_name": "Orders", "value": "count(*)"}, {"aggregation": "sum", "column_name": "order_item_id", "data_type": "Integer", "label": "sum(order_item_id)", "measure_name": "Items", "value": "sum(order_item_id)"}, {"aggregation": "sum", "column_name": "price", "data_type": "Decimal", "label": "sum(price)", "measure_name": "Net Total", "value": "sum(price)"}, {"aggregation": "sum", "column_name": "freight_value", "data_type": "Decimal", "label": "sum(freight_value)", "measure_name": "Freight", "value": "sum(freight_value)"}, {"aggregation": "sum", "column_name": "order_value", "data_type": "Decimal", "label": "sum(order_value)", "measure_name": "Grand Total", "value": "sum(order_value)"}], "order_by": [{"column": {"column_name": "Date", "type": "column"}, "direction": "desc"}], "shorten_numbers": true, "sparkline": true}}, "sj4rdo50er": {"name": "sj4rdo50er", "title": "Order Value per Month per Status", "workbook": "10", "query": "sj4rgk0g8v", "chart_type": "Table", "config": {"columns": [{"column_name": "order_purchase_timestamp", "data_type": "Datetime", "dimension_name": "order_purchase_timestamp", "granularity": "month", "label": "order_purchase_timestamp", "value": "order_purchase_timestamp"}], "filters": {"filters": [{"column": {"column_name": "order_status", "type": "column"}, "operator": "not_in", "value": ["unavailable"]}], "logical_operator": "And"}, "limit": 100, "order_by": [{"column": {"column_name": "order_purchase_timestamp", "type": "column"}, "direction": "asc"}, {"column": {"column_name": "order_status", "type": "column"}, "direction": "desc"}], "rows": [{"column_name": "order_status", "data_type": "String", "dimension_name": "order_status", "label": "order_status", "value": "order_status"}], "show_column_totals": false, "show_filter_row": true, "show_row_totals": false, "values": [{"0": {"aggregation": "count", "column_name": "count", "data_type": "Integer", "label": "count_of_rows", "measure_name": "count_of_rows", "value": "count_of_rows"}, "aggregation": "sum", "column_name": "order_value", "data_type": "Decimal", "label": "sum_of_order_value", "measure_name": "sum_of_order_value", "value": "sum_of_order_value"}]}}}, "dashboards": {"sj4rie1jfp": {"name": "sj4rie1jfp", "title": "Order Analysis", "workbook": "10", "items": [{"chart": "sj4rjuvvpu", "layout": {"h": 3, "i": "1geec587", "moved": false, "w": 20, "x": 0, "y": 0}, "type": "chart"}, {"chart": "sj4r5sn1ro", "layout": {"h": 8, "i": "1kpzltkq", "moved": false, "w": 10, "x": 0, "y": 3}, "type": "chart"}, {"chart": "sj4rdmcvpa", "layout": {"h": 8, "i": "2mkbxvt3", "moved": false, "w": 10, "x": 10, "y": 3}, "type": "chart"}, {"chart": "sj4rdo50er", "layout": {"h": 11, "i": "b6n952s4", "moved": false, "w": 20, "x": 0, "y": 11}, "type": "chart"}]}}}}