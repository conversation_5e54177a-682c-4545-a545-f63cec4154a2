export const slideDownTransition = {
	enterActiveClass: 'transition duration-200 ease-out',
	leaveActiveClass: 'transition duration-150 ease-in',
	enterFromClass: 'translate-y-1 opacity-0',
	enterToClass: 'translate-y-0 opacity-100',
	leaveFromClass: 'translate-y-0 opacity-100',
	leaveToClass: 'translate-y-1 opacity-0',
}

export const slideRightTransition = {
	enterActiveClass: 'transition duration-200 ease-out',
	leaveActiveClass: 'transition duration-150 ease-in',
	enterFromClass: 'translate-x-1 opacity-0',
	enterToClass: 'translate-x-0 opacity-100',
	leaveFromClass: 'translate-x-0 opacity-100',
	leaveToClass: 'translate-x-1 opacity-0',
}
