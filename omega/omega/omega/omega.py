def calculate_future_value(investments, rate):
    n = len(investments)
    future_value = 0
    for i in range(n):
        fv = investments[i] * ((1 + rate) ** (n - i))

        future_value += fv
        print(f"year {i+1}: {future_value:,.2f}...........{fv}")
    return future_value


# Define cash flows over 15 years
investments = [125378, 125378] + [50378] * 13
interest_rate = 0.10

fv = calculate_future_value(investments, interest_rate)
print(f"Future Value at the end of 15 years: NPR {fv:,.2f}")
