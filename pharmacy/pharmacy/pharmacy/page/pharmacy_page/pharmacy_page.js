class Pharmacy extends BaseWorkStation {
    constructor(wrapper, templatesPath, templates, layout_html, page_name) {
        super(wrapper, templatesPath, templates, page_name);
        this.layout_html = layout_html;
        this.profiler = false;
        this.page_name = page_name;
        this.selectedPatient = "";
        this.checkCounterStatus();
    }
    checkCounterStatus() {
        frappe.call({
            method: "opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.check_opening_entry",
            args: {
                cashier: frappe.session.user,
            },
            callback: (r) => {
                if (r.message && r.message.length) {
                    this.initializeWorkstation();
                } else {
                    this.showOpeningDialog();
                }
            },
        });
    }

    async showOpeningDialog() {
        const me = this;
        const pharmacy_company = await frappe.db.get_single_value("Pharmacy Settings", "pharmacy_company");
        const table_fields = [
            {
                fieldname: "mode_of_payment",
                fieldtype: "Link",
                in_list_view: 1,
                label: __("Mode of Payment"),
                options: "Mode of Payment",
                reqd: 1,
            },
            {
                fieldname: "opening_amount",
                fieldtype: "Currency",
                in_list_view: 1,
                label: __("Opening Amount"),
                options: "company:company_currency",
                onchange: function () {
                    dialog.fields_dict.balance_details.df.data.some((d) => {
                        if (d.idx == this.doc.idx) {
                            d.opening_amount = this.value;
                            dialog.fields_dict.balance_details.grid.refresh();
                            return true;
                        }
                    });
                },
            },
        ];
        const fetch_pos_payment_methods = () => {
            const pos_profile = dialog.fields_dict.pos_profile.get_value();
            if (!pos_profile) return;
            frappe.db.get_doc("POS Profile", pos_profile).then(({ payments }) => {
                dialog.fields_dict.balance_details.df.data = [];
                payments.forEach((pay) => {
                    const { mode_of_payment } = pay;
                    dialog.fields_dict.balance_details.df.data.push({
                        mode_of_payment,
                        opening_amount: "0",
                    });
                });
                dialog.fields_dict.balance_details.grid.refresh();
            });
        };
        const pos_profile_query = () => {
            return {
                query: "erpnext.accounts.doctype.pos_profile.pos_profile.pos_profile_query",
                filters: { company: dialog.fields_dict.company.get_value() },
            };
        };
        const dialog = new frappe.ui.Dialog({
            title: __("Create Counter Opening Entry"),
            static: true,
            fields: [
                {
                    fieldtype: "Link",
                    label: __("Company"),
                    default: pharmacy_company,
                    options: "Company",
                    fieldname: "company",
                    reqd: 1,
                },
                {
                    fieldtype: "Link",
                    label: __("POS Profile"),
                    options: "POS Profile",
                    fieldname: "pos_profile",
                    reqd: 1,
                    get_query: () => pos_profile_query(),
                    onchange: () => fetch_pos_payment_methods(),
                },
                {
                    fieldname: "balance_details",
                    fieldtype: "Table",
                    label: __("Opening Balance Details"),
                    cannot_add_rows: false,
                    in_place_edit: true,
                    reqd: 1,
                    data: [],
                    fields: table_fields,
                },
            ],
            primary_action: async function ({pos_profile, company, balance_details }) {
                if (!balance_details.length) {
                    frappe.show_alert({
                        message: __("Please add Mode of payments and opening balance details."),
                        indicator: "red",
                    });
                    return frappe.utils.play_sound("error");
                }

                // Filter balance details for empty rows
                balance_details = balance_details.filter((d) => d.mode_of_payment);

                frappe.call({
                    method: "opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.create_opening_entry",
                    args: { pos_profile, company, balance_details },
                    callback: (r) => {
                        if (r.message) {
                            frappe.call({
                                method: "frappe.client.submit",
                                args: {
                                    doc: r.message,
                                },
                                callback: (r) => {
                                    if (r.message) {
                                        frappe.show_alert({
                                            message: __("Counter opened successfully"),
                                            indicator: "green",
                                        });

                                        // Initialize the workstation after counter is opened
                                        me.initializeWorkstation();
                                    }
                                },
                            });
                        }
                    },
                });
                dialog.hide();
            },
            primary_action_label: __("Submit"),
        });

        dialog.show();

        // Don't allow closing this dialog
        dialog.$wrapper.find(".modal-header .btn-modal-close").hide();
    }
    
    async initializeWorkstation() {
        this.actions = {
            "#show-new-patient": () => this.renderNewForm("Patient", "#content-section"),
            "#book-appointment": () =>
                this.renderNewForm("Patient Appointment", "#content-section"),
            "#quick-patient-appointment": () =>
                this.renderNewForm("Quick Patient Appointment", "#content-section"),
            "#general-doctor": () =>
                this.renderNewForm("Quick Patient Appointment", "#content-section", {
                    doctor_type: "general",
                }),
            "#specialist-doctor": () =>
                this.renderNewForm("Quick Patient Appointment", "#content-section", {
                    doctor_type: "specialist",
                }),
            "#show-appointment": () =>
                this.loadSection("appointment_list", "#content-section", {
                    doctype: "Patient Appointment",
                    filters: { patient: this.selectedPatient },
                }),
            "#new-emergency-ipd": () => {
                this.renderNewForm("Emergency Admission", "#content-section");
            },
            "#new-billing": () => {
                this.renderNewForm("Sales Invoice", "#content-section");
            },
            "#new-return": () => {
                console.log("here");
				this.renderNewForm("Sales Invoice", "#content-section", 
                    {doc_vals: {is_return: 1, patient: this.selectedPatient}}
                );
			},
            "#show-invoices": () =>
                this.loadSection("invoice_list", "#content-section", {
                    doctype: "Sales Invoice",
                    filters: { patient: this.selectedPatient },
                }),
            "#show-payments": () =>
                this.loadSection("payment_list", "#content-section", {
                    doctype: "Payment Entry",
                    filters: {},
                }),
            "#show-patients": () =>
                this.loadSection("patient_list", "#content-section", {
                    doctype: "Patient",
                    filters: {},
                }),
            // "#item-lists": () =>
            //     this.loadSection("item_list", "#content-section", { doctype: "Item", filters: {} }),
            // "#medication-lists": () =>
            //     this.loadSection("medication_list", "#content-section", {
            //         doctype: "Medication",
            //         filters: {},
            //     }),
        };

        this.set_up_key_bindings();
        this.initPage("Counter Work Station");
        this.default_company = await frappe.db.get_single_value("Pharmacy Settings", "pharmacy_company");
        this.initSelectedPatient();
        this.initPatientField();
    
        this.renderNewForm("Sales Invoice", "#content-section", {company: 
            await frappe.db.get_single_value("Pharmacy Settings", "pharmacy_company")
        });
        // this.renderNewForm("Sales Invoice", "#content-section");
        // this.loadSection("default","#content-section")
        this.attachGlobalEventHandlers();
    }

    renderLayout() {
        super.renderLayout(this.layout_html);
        this.loadSection("button_container", "#button-section");
    }

    initSelectedPatient() {
        if (!$("#selectedPatientValue").length) {
            $("<div>", {
                id: "selectedPatientValue",
                text: "No patient selected",
                "data-value": "",
                style: "display: none;",
            }).appendTo("body");
        }
    }

    initPatientField() {
        const formSection = document.getElementById("form-section");
        if (!formSection) return console.error("Error: form-section not found");

        this.patientField = frappe.ui.form.make_control({
            parent: formSection,
            df: {
                fieldtype: "Link",
                options: "Patient",
                label: "Select Patient",
                fieldname: "patient",
                change: () => this.setSelectedPatient(this.patientField.get_value()),
            },
            render_input: true,
        });
        this.patientField.refresh();
    }

    setSelectedPatient(patient) {
        if (this.selectedPatient === patient) return;

        this.selectedPatient = patient;
        $("#selectedPatientValue").text(patient).attr("data-value", patient);

        // this would solve the reverse field fill up but would again rerender form so dont use it just yet
        // if (this.patientField && this.patientField.get_value() !== patient) {
        //     this.patientField.set_value(patient);
        // }

        this.updateSectionsAfterSelection();
    }

    enableProfiler() {
        this.profiler = true;
    }

    updateSectionsAfterSelection() {
        let balance = 0;
        const me = this;

        frappe.call({
            method: "sb_health.methods.patient.patient_balance",
            args: { patient_id: this.selectedPatient, company: this.default_company },
            callback: function (response) {
                if (response.message) {
                    balance = response.message;
                }
                me.loadSection("patient_preview", "#patient-preview-section", {
                    selectedPatient: me.selectedPatient,
                    balance: balance,
                });
                if (me.lastRenderingFunction) {
                    me.lastRenderingFunction();
                }
            },
        });
    }

    // async renderNewForm(doctype, section, context = {}) {
    //     $(section).empty();
        
    //     const $loading =
    //         $(`<div class="form-loading-message" style="text-align: center; padding: 1rem;">
    //         <i class="fa fa-spinner fa-spin fa-2x"></i>
    //         <p>Loading form, please wait...</p>
    //     </div>`);
        
    //     $(section).append($loading);

    //     if (this.currentForm) {
    //         this.nullifyForm(this.currentForm);
    //     }

    //     await frappe.model.with_doctype(doctype);
    //     let newDoc = frappe.model.get_new_doc(doctype);
    //     if (["Sales Invoice"].includes(doctype)) {
    //         newDoc.company = await frappe.db.get_single_value("Pharmacy Settings", "pharmacy_company");
    //         newDoc.is_pos = 1;

    //         if (this.selectedPatient){
    //             newDoc.patient = this.selectedPatient;
    //         }
    //     }

    //     if (doctype === "Payment Entry" && this.selectedPatient) {
    //         try {
    //             let patient_doc = await frappe.db.get_doc("Patient", this.selectedPatient);
    //             if (!patient_doc || !patient_doc.customer)
    //                 throw new Error("Patient does not have a linked customer.");

    //             let customer = await frappe.db.get_doc("Customer", patient_doc.customer);
    //             let default_company = await frappe.db.get_single_value(
    //                 "Pharmacy Settings",
    //                 "pharmacy_company"
    //             );
    //             let company = await frappe.db.get_doc("Company", default_company);
    //             console.log("Company:", company);

    //             Object.assign(newDoc, {
    //                 party: patient_doc.customer,
    //                 party_type: "Customer",
    //                 party_name: customer.customer_name,
    //                 mode_of_payment: "Cash",
    //                 paid_from: company.default_receivable_account || "",
    //             });
    //         } catch (err) {
    //             console.error("Error setting default values for Payment Entry:", err);
    //         }
    //     }

    //     if (doctype === "Payment Entry" && context.sales_invoice) {
    //         newDoc.references = [
    //             {
    //                 reference_doctype: "Sales Invoice",
    //                 reference_name: context.sales_invoice.name,
    //                 total_amount: context.sales_invoice.grand_total,
    //                 allocated_amount: context.sales_invoice.outstanding_amount,
    //             },
    //         ];
    //         newDoc.mode_of_payment = "Cash";
    //         newDoc.paid_amount = context.sales_invoice.outstanding_amount;
    //     }

    //     $loading.remove();

    //     const targetPages = ["pharmacy-page"];

    //     if (window.workstationInstance && targetPages.includes(window.workstationInstance.page_name)) {
        
    //         this.currentForm = new frappe.ui.form.Form(doctype, $(section));

    //         frappe.ui.form.on(doctype, {
    //             refresh: function (frm) {
    //                 const currentRoute = frappe.get_route()[0];
    //                 if (targetPages.includes(currentRoute)) {
    //                     $(".sidebar-toggle-btn").remove();
    //                     $(".form-footer").remove();
    //                     frm.page.sidebar?.remove?.();
    //                 }

    //                 setTimeout(() => {
    //                     frm.remove_custom_button("Timesheet", "Get Items From");
    //                     frm.remove_custom_button("Healthcare Services", "Get Items From");
    //                     frm.remove_custom_button("Prescriptions", "Get Items From");
    //                     frm.remove_custom_button("Sales Order", "Get Items From");
    //                     frm.remove_custom_button("Delivery Note", "Get Items From");
    //                     frm.remove_custom_button("Quotation", "Get Items From");
    //                 }, 1);
    //             },
    //         });

    //         await this.currentForm.refresh(newDoc.name);

    //         if (this.profiler || frappe.boot.developer_mode === 1) {
    //             const timings = [];
    //             const wrapWithProfiler = (eventName, handler) => {
    //                 return async function (...args) {
    //                     const start = performance.now();
    //                     // console.log(`[CALL] ${eventName} started`);
    //                     let result;
    //                     try {
    //                         result = await handler.apply(this, args);
    //                     } catch (e) {
    //                         // console.error(`[ERROR] in ${eventName}:`, e);
    //                         throw e;
    //                     } finally {
    //                         const end = performance.now();
    //                         const timeTaken = (end - start).toFixed(2);
    //                         timings.push({ event: eventName, time: timeTaken });
    //                         // console.log(`[DONE] ${eventName} took ${timeTaken}ms`);
    //                     }
    //                     return result;
    //                 };
    //             };

    //             window.viewFormEventTimings = () => console.table(timings);

    //             const lifecycleEvents = [
    //                 "before_load",
    //                 "onload",
    //                 "refresh",
    //                 "validate",
    //                 "before_save",
    //                 "after_save",
    //                 "before_submit",
    //                 "on_submit",
    //                 "before_cancel",
    //                 "on_cancel",
    //                 "before_naming",
    //                 "autoname",
    //             ];

    //             for (const eventName of lifecycleEvents) {
    //                 const originalHandler = this.currentForm[eventName];
    //                 if (typeof originalHandler === "function") {
    //                     this.currentForm[eventName] = wrapWithProfiler(eventName, originalHandler);
    //                 }
    //             }

    //             const formMetaHandlers = frappe.ui.form.handlers?.[this.currentForm.doctype];
    //             if (formMetaHandlers && typeof formMetaHandlers === "object") {
    //                 for (const [key, handlers] of Object.entries(formMetaHandlers)) {
    //                     if (Array.isArray(handlers)) {
    //                         for (let i = 0; i < handlers.length; i++) {
    //                             const handler = handlers[i];
    //                             if (typeof handler === "function") {
    //                                 handlers[i] = wrapWithProfiler(`${key}`, handler);
    //                             }
    //                         }
    //                     }
    //                 }
    //             }

    //             if (this.currentForm.script_manager && this.currentForm.script_manager.events) {
    //                 for (const fieldname in this.currentForm.fields_dict) {
    //                     const field = this.currentForm.fields_dict[fieldname];
    //                     const df = field.df;
    //                     if (df && df.fieldname) {
    //                         const fieldHandler =
    //                             this.currentForm.script_manager.events[df.fieldname];
    //                         if (typeof fieldHandler === "function") {
    //                             this.currentForm.script_manager.events[df.fieldname] =
    //                                 wrapWithProfiler(`field:${df.fieldname}`, fieldHandler);
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }

    nullifyForm(form) {
        Object.entries(form.events).forEach(([event, handler]) => {
            frappe.ui.form.off(form.doctype, event);
        });

        if (form.beforeUnloadListener) {
            removeEventListener("beforeunload", form.beforeUnloadListener, { capture: true });
        }

        if (form && form.doctype && frappe.ui.form) {
            ["refresh", "onload", "setup"].forEach((event) => {
                frappe.ui.form.off(form.doctype, event);
            });
        }

        form.docname = null;
        form.$wrapper = null;
        form.currentForm = null;
    }
}


const page_name = "pharmacy-page";
frappe.pages[page_name].on_page_load = function (wrapper) {
    $("#navbar-search").hide();
    $(".search-icon").hide();
    const templates = {
        "default": "health_counter.html",
        "button_container": "button_container.html",
        "patient_list": "patient_list.html",
        "appointment_list": "appointment_list.html",
        "invoice_list": "invoice_list.html",
        "payment_list": "payment_list.html",
        "patient_preview": "patient_preview.html",
		"item_list":"item_list.html",
        "medication_list":"medication_list.html"
    };

    const templatesPath = "pharmacy/templates/pages/pharmacy_page/";
    const layout_html = (`
        <div class="container-fluid">
            <div class="row">
            <div class="col-12 col-md-2" id="button-section-container">
                    <div 
                        id="button-section" 
                        class="d-flex flex-column justify-content-start align-items-start p-1"
                        style="
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(12px);
                            border-left: 1px solid rgba(255, 255, 255, 0.2);
                            height: auto;
                        "
                    >
                    </div>
                </div>
                
                <div class="col-12 col-md-7">
                    <div class="card p-3">
                        <div class="health-sections" id="content-section"></div>
                    </div>
                </div>

                 <div class="col-12 col-md-3">
                    <div class="col">
                        <div class="section-buttons" id="form-section"></div>
                        <div class="section-buttons" id="patient-preview-section"></div>
                    </div>
                </div>
    
            </div>
        </div>
    `);
    
    window.workstationInstance = new Pharmacy(
        wrapper, 
        templatesPath, 
        templates,
        layout_html,
        page_name);
        
    window.workstationInstance.beforeRouteChange = function(...route) {
        window.workstationInstance.removePageStyles();
    };
};

frappe.pages[page_name].on_page_show = function(wrapper){
    window.workstationInstance.renderLayout();
}

const originalSetRoute = frappe.set_route;

frappe.set_route = function (...args) {
	try {
		if (typeof window.workstationInstance?.beforeRouteChange === "function") {
			// window.workstationInstance.beforeRouteChange(...args);

				const [, raw_doctype, docname] = args[0].split("/").filter(Boolean); 
				const doctype = raw_doctype
					.split("-")
					.map(part => part.charAt(0).toUpperCase() + part.slice(1))
					.join(" ");

				window.workstationInstance.renderForm(doctype, decodeURIComponent(docname), "#content-section");
				return;
		}
	} catch (e) {
		console.warn("beforeRouteChange threw an error", e);
	}

	return originalSetRoute.apply(this, args);
};

