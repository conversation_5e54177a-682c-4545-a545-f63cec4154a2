<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Main content -->
    <div class="w-full max-w-md p-8">
      <!-- Login Card -->
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Header with logo -->
        <div class="px-8 pt-8 pb-6 text-center">
          <!-- Logo -->
          <div class="flex justify-center mb-6">
            <img 
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'%3E%3Cdefs%3E%3ClinearGradient id='grad1' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' style='stop-color:%236366f1;stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:%238b5cf6;stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath d='M50 150 C50 90, 90 50, 150 50 C180 50, 200 60, 220 80 C240 100, 250 130, 250 150 C250 210, 210 250, 150 250 C90 250, 50 210, 50 150 Z' fill='none' stroke='url(%23grad1)' stroke-width='8'/%3E%3Cpath d='M160 80 C180 80, 200 100, 200 120 C200 140, 180 160, 160 160 C140 160, 120 140, 120 120 C120 100, 140 80, 160 80 Z' fill='url(%23grad1)'/%3E%3Cpath d='M160 160 L160 200 L140 220 L180 220 Z' fill='url(%23grad1)'/%3E%3Cpolygon points='100,70 105,85 120,85 110,95 115,110 100,100 85,110 90,95 80,85 95,85' fill='%23fbbf24'/%3E%3Cpolygon points='280,90 284,100 295,100 287,107 291,118 280,112 269,118 273,107 265,100 276,100' fill='%23fbbf24'/%3E%3Cpolygon points='320,140 323,148 332,148 326,153 329,162 320,158 311,162 314,153 308,148 317,148' fill='%23a78bfa'/%3E%3Ctext x='150' y='280' font-family='Arial, sans-serif' font-size='36' font-weight='bold' text-anchor='middle' fill='url(%23grad1)'%3EMoonStar%3C/text%3E%3Ctext x='300' y='260' font-family='Arial, sans-serif' font-size='14' text-anchor='middle' fill='%236b7280'%3E%3C/text%3E%3C/svg%3E" 
              alt="MoonStar Logo" 
              class="w-24 h-24"
            />
          </div>
          
          <h1 class="text-2xl font-bold text-gray-800 mb-2">
            Welcome Back
          </h1>
          <p class="text-gray-600 text-sm">
            Sign in to MoonStar
          </p>
        </div>

        <!-- Form content -->
        <div class="px-8 pb-8">
          <form class="space-y-6" @submit.prevent="submit">
            <!-- Email Input -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                User ID
              </label>
              <div class="relative">
                <input
                  required
                  name="email"
                  type="text"
                  placeholder="<EMAIL>"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- Password Input -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div class="relative">
                <input
                  required
                  name="password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="••••••"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                  <button
                    type="button"
                    @click="showPassword = !showPassword"
                    class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200"
                  >
                    <!-- Eye Icon (Show Password) -->
                    <svg v-if="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                    <!-- Eye Slash Icon (Hide Password) -->
                    <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Login Button -->
            <button
              type="submit"
              :disabled="session.login.loading"
              class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:scale-100 shadow-md hover:shadow-lg disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              <span v-if="!session.login.loading">Sign In</span>
              <span v-else class="flex items-center space-x-2">
                <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Signing in...</span>
              </span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { session } from '../data/session'

const showPassword = ref(false)

function submit(e) {
  let formData = new FormData(e.target)
  session.login.submit({
    email: formData.get('email'),
    password: formData.get('password'),
  })
}
</script>

<style scoped>
/* Clean and simple - no extra animations needed */
</style>