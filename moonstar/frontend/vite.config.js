import path from 'path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import frappeui from 'frappe-ui/vite'
import { VitePWA } from 'vite-plugin-pwa'

export default defineConfig({
  plugins: [
    vue(),
    frappeui(),
    VitePWA({
      registerType: 'autoUpdate',
      strategies: 'injectManifest',
      injectRegister: null,
      devOptions: {
        enabled: true,
      },
      manifest: {
        display: 'standalone',
        name: 'OptERP Sales',
        short_name: 'OptERP Sales',
        start_url: '/ui',
        description: 'Everyday Sales operations at your fingertips',
        theme_color: '#ffffff',
        icons: [
          {
            src: '/assets/opterp_app/images/opterp-logo.svg',
            sizes: '192x192',
            type: 'image/svg',
            purpose: 'any',
          },
          {
            src: '/assets/opterp_app/images/opterp-logo.svg',
            sizes: '192x192',
            type: 'image/svg',
            purpose: 'maskable',
          },
          {
            src: '/assets/opterp_app/images/opterp-logo.svg',
            sizes: '512x512',
            type: 'image/svg',
            purpose: 'any',
          },
          {
            src: '/assets/opterp_app/images/opterp-logo.svg',
            sizes: '512x512',
            type: 'image/svg',
            purpose: 'maskable',
          },
        ],
      },
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: `../${path.basename(path.resolve('..'))}/public/frontend`,
    emptyOutDir: true,
    target: 'es2015',
  },
  optimizeDeps: {
    include: ['frappe-ui > feather-icons', 'showdown', 'engine.io-client'],
  },
})
