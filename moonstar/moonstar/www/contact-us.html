{% extends "templates/web.html" %}

{% block page_content %}
<div class="container">
  <div class="heading">Contact Us</div>

  <div class="contact-section" style="display: flex; flex-wrap: wrap; gap: 2rem;">
    <!-- Contact Form -->
    <div class="form-container" style="flex: 1; min-width: 300px;">
      <form onsubmit="return submitContactForm(event)">
        <input type="text" name="name" placeholder="Your Name" required />
        <input type="email" name="email_address" placeholder="Your Email" required />
        <textarea name="message" placeholder="Your Message" rows="5" required></textarea>
        <button type="submit">SEND</button>
      </form>
    </div>

    <!-- Right-side Info Cards -->
    <div class="info-column" style="flex: 1; min-width: 300px;">
      <div class="info-card">
        <h3>Email Us</h3>
        <p>Email address:</p>
        <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>

      <div class="info-card">
        <h3>Call Us</h3>
        <p>Contact number:</p>
        <p>056-536504</p>
      </div>
    </div>
  </div>

    <!-- Map Embed -->
    <div class="map-container">
      <iframe 
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3530.197121805978!2d84.**************!3d27.68192492673961!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3994fb7395b8f331%3A0x6453226aa615cfe3!2sCoko%20ice-cream%2C%20Bharatpur%2044200!5e0!3m2!1sen!2snp!4v1714800000000!5m2!1sen!2snp"
        allowfullscreen="" 
        loading="lazy" 
        referrerpolicy="no-referrer-when-downgrade">
      </iframe>
    </div>
</div>

<script>
  async function submitContactForm(event) {
    event.preventDefault();
    const form = event.target;

    frappe.call({
      method: 'moonstar.api.contact_us.get_contact_us_data',
      args: {
        name: form.name.value,
        email_address: form.email_address.value,
        message: form.message.value
      },
      callback: function (response) {
        if (response.message && response.message.status === 'success') {
          alert("Thank you! Your message has been sent.");
          form.reset();
        } else {
          alert(response.message || "There was an error. Please try again.");
        }
      },
      error: function (err) {
        console.error(err);
        alert("Server error. Please try again later.");
      }
    });
  }
</script>
{% endblock %}
