import frappe
from frappe import _


@frappe.whitelist(allow_guest=True)
def get_item_variants_and_routes(item_code):
    item = frappe.get_value(
        "Item", {"item_code": item_code}, ["name", "has_variants"], as_dict=True
    )
    if not item or not item.has_variants:
        return {"variants": [], "routes": {}}

    variants = frappe.get_all(
        "Item", filters={"variant_of": item_code}, fields=["name", "item_name", "image"]
    )
    variant_names = [v["name"] for v in variants]

    routes = frappe.get_all(
        "Website Item",
        filters={"item_code": ["in", variant_names]},
        fields=["item_code", "route"],
    )

    route_map = {r["item_code"]: r["route"] for r in routes}
    return {"variants": variants, "routes": route_map}


@frappe.whitelist(allow_guest=True)
def get_item_groups_for_special_variant(variant_of_code):
    """
    Get item groups for variants of a specific item (e.g., CK01IC02005)
    Returns item groups for card display
    """
    if variant_of_code != "CK01IC02005":
        return {"item_groups": []}
    
    # Get all variants of the specified item
    variants = frappe.get_all(
        "Item", 
        filters={"variant_of": variant_of_code}, 
        fields=["name", "item_name", "item_group", "image"]
    )
    
    if not variants:
        return {"item_groups": []}
    
    # Group variants by item_group
    item_groups = {}
    for variant in variants:
        group = variant.get("item_group")
        if group:
            if group not in item_groups:
                item_groups[group] = {
                    "item_group": group,
                    "variants": []
                }
            item_groups[group]["variants"].append(variant)
    
    # Convert to list
    result_groups = []
    for group_data in item_groups.values():
        result_groups.append({
            "item_group": group_data["item_group"],
            "sample_image": group_data["variants"][0].get("image") or ""
        })
    
    return {"item_groups": result_groups}


@frappe.whitelist(allow_guest=True)
def get_variants_by_item_group(variant_of_code, item_group):
    """
    Get all variants of a specific item group for the specified variant_of code
    """
    if variant_of_code != "CK01IC02005":
        return {"variants": [], "routes": {}}
    
    # Get all variants of the specified item that belong to the item_group
    variants = frappe.get_all(
        "Item", 
        filters={
            "variant_of": variant_of_code,
            "item_group": item_group
        }, 
        fields=["name", "item_name", "image"]
    )
    
    if not variants:
        return {"variants": [], "routes": {}}
    
    variant_names = [v["name"] for v in variants]
    
    # Get routes for these variants
    routes = frappe.get_all(
        "Website Item",
        filters={"item_code": ["in", variant_names]},
        fields=["item_code", "route"],
    )
    
    route_map = {r["item_code"]: r["route"] for r in routes}
    return {"variants": variants, "routes": route_map}