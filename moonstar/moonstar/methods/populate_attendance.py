import frappe
import random
from datetime import datetime, timed<PERSON><PERSON>

def create_checkins_for_khagendra():
    employee = "HR-EMP-00001"  # <PERSON><PERSON><PERSON>'s employee ID
    start_date = datetime(2025, 6, 1)
    end_date = datetime(2025, 6, 30)
    
    print(f"Creating check-ins for {employee} from {start_date.date()} to {end_date.date()}")
    
    # Get all days in the range
    all_days = [(start_date + timedelta(days=i)) for i in range((end_date - start_date).days + 1)]
    
    # Filter out Saturdays and Sundays for working days (only Monday-Friday)
    working_days = [d for d in all_days if d.weekday() < 5]  # 0=Monday, 6=Sunday
    
    print(f"Total working days (Mon-Fri): {len(working_days)}")
    
    # Select random days for overtime (about 40% of working days)
    overtime_count = max(6, int(len(working_days) * 0.4))  # At least 6 days or 40% of working days
    overtime_days = random.sample(working_days, min(overtime_count, len(working_days)))
    
    print(f"Days with overtime: {len(overtime_days)}")
    
    created_checkins = 0
    
    for current_date in working_days:
        # Standard check-in time: 10:00 AM (with slight variations)
        check_in_minutes = random.randint(0, 15)  # 10:00 to 10:15
        in_time = datetime.combine(current_date, datetime.strptime("10:00:00", "%H:%M:%S").time()) + timedelta(minutes=check_in_minutes)
        
        # Standard check-out time: 17:00 (5:00 PM)
        base_out_time = datetime.combine(current_date, datetime.strptime("17:00:00", "%H:%M:%S").time())
        
        # Determine check-out time based on overtime
        if current_date in overtime_days:
            # Add 1-3 extra hours for overtime (17:00 becomes 18:00, 19:00, or 20:00)
            extra_hours = random.choice([1, 2, 3])
            out_time = base_out_time + timedelta(hours=extra_hours)
            print(f"  {current_date.date()}: Overtime day - Out at {out_time.strftime('%H:%M')} (+{extra_hours}h)")
        else:
            # Regular day - out at 17:00 (with slight variations up to 15 minutes)
            extra_minutes = random.randint(0, 15)
            out_time = base_out_time + timedelta(minutes=extra_minutes)
            print(f"  {current_date.date()}: Regular day - Out at {out_time.strftime('%H:%M')}")
        
        try:
            # Create IN checkin
            checkin_in = frappe.new_doc("Employee Checkin")
            checkin_in.employee = employee
            checkin_in.time = in_time
            checkin_in.log_type = "IN"
            checkin_in.insert(ignore_permissions=True)
            
            # Fetch shift information
            try:
                checkin_in.fetch_shift()
            except:
                pass  # Continue even if fetch_shift fails
            
            checkin_in.save(ignore_permissions=True)
            created_checkins += 1
            
            # Create OUT checkin
            checkin_out = frappe.new_doc("Employee Checkin")
            checkin_out.employee = employee
            checkin_out.time = out_time
            checkin_out.log_type = "OUT"
            checkin_out.insert(ignore_permissions=True)
            
            # Fetch shift information
            try:
                checkin_out.fetch_shift()
            except:
                pass  # Continue even if fetch_shift fails
            
            checkin_out.save(ignore_permissions=True)
            created_checkins += 1
            
        except Exception as e:
            print(f"  Error creating checkin for {current_date.date()}: {str(e)}")
            continue
    
    # Commit all changes
    frappe.db.commit()
    
    # Summary
    total_hours = 0
    regular_days = len(working_days) - len(overtime_days)
    overtime_hours = len(overtime_days) * 1.5  # Average 1.5 extra hours per overtime day
    regular_hours = regular_days * 7  # 7 hours per regular day
    total_hours = regular_hours + (len(overtime_days) * 7) + overtime_hours
    
    summary = f"""
✅ Check-ins created successfully for Khagendra Jora!

📊 SUMMARY:
- Total working days: {len(working_days)}
- Regular days: {regular_days}
- Overtime days: {len(overtime_days)}
- Total check-ins created: {created_checkins}
- Estimated total hours: {total_hours:.1f}

⏰ SCHEDULE:
- Check-in time: 10:00 AM (with variations)
- Regular check-out: 17:00 PM (5:00 PM)
- Overtime check-out: 18:00-20:00 PM (6:00-8:00 PM)

📅 PERIOD: {start_date.date()} to {end_date.date()}
"""
    
    print(summary)
    return summary

# Alternative function to run for specific date range
def create_checkins_custom_range(employee_id, start_date_str, end_date_str):
    """
    Create check-ins for custom date range
    Usage: create_checkins_custom_range("HR-EMP-00001", "2025-07-01", "2025-07-30")
    """
    employee = employee_id
    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")
    
    # Same logic as above but with custom parameters
    all_days = [(start_date + timedelta(days=i)) for i in range((end_date - start_date).days + 1)]
    working_days = [d for d in all_days if d.weekday() < 5]
    
    overtime_count = max(3, int(len(working_days) * 0.3))
    overtime_days = random.sample(working_days, min(overtime_count, len(working_days)))
    
    for current_date in working_days:
        check_in_minutes = random.randint(0, 10)
        in_time = datetime.combine(current_date, datetime.strptime("10:00:00", "%H:%M:%S").time()) + timedelta(minutes=check_in_minutes)
        
        base_out_time = datetime.combine(current_date, datetime.strptime("17:00:00", "%H:%M:%S").time())
        
        if current_date in overtime_days:
            extra_hours = random.choice([1, 2, 3])
            out_time = base_out_time + timedelta(hours=extra_hours)
        else:
            out_time = base_out_time + timedelta(minutes=random.randint(0, 10))
        
        # Create check-ins (same logic as main function)
        try:
            checkin_in = frappe.new_doc("Employee Checkin")
            checkin_in.employee = employee
            checkin_in.time = in_time
            checkin_in.log_type = "IN"
            checkin_in.insert(ignore_permissions=True)
            checkin_in.save(ignore_permissions=True)
            
            checkin_out = frappe.new_doc("Employee Checkin")
            checkin_out.employee = employee
            checkin_out.time = out_time
            checkin_out.log_type = "OUT"
            checkin_out.insert(ignore_permissions=True)
            checkin_out.save(ignore_permissions=True)
        except Exception as e:
            print(f"Error for {current_date.date()}: {str(e)}")
    
    frappe.db.commit()
    return f"✅ Custom check-ins created for {employee} from {start_date_str} to {end_date_str}"


def create_checkins_full_attendance(employee_id="HR-EMP-00001"):
    import frappe
    import random
    from datetime import datetime, timedelta

    employee = employee_id
    start_date = datetime(2025, 9, 1)
    end_date = datetime(2025, 9, 30)

    print(f"Creating check-ins for {employee} from {start_date.date()} to {end_date.date()} (including weekends)")

    # All days in the month (including weekends)
    all_days = [(start_date + timedelta(days=i)) for i in range((end_date - start_date).days + 1)]

    # Randomly select 40% of days for overtime
    overtime_count = max(10, int(len(all_days) * 0.4))  # Ensure at least 10 days of overtime
    overtime_days = random.sample(all_days, min(overtime_count, len(all_days)))

    print(f"Total days in August: {len(all_days)}")
    print(f"Overtime days selected: {len(overtime_days)}")

    created_checkins = 0

    for current_date in all_days:
        # IN: Between 10:00 to 10:15 AM
        check_in_minutes = random.randint(0, 15)
        in_time = datetime.combine(current_date, datetime.strptime("10:00:00", "%H:%M:%S").time()) + timedelta(minutes=check_in_minutes)

        # OUT base: 17:00 (5:00 PM)
        base_out_time = datetime.combine(current_date, datetime.strptime("17:00:00", "%H:%M:%S").time())

        # Apply overtime if applicable
        if current_date in overtime_days:
            extra_hours = random.choice([1, 2, 3])
            out_time = base_out_time + timedelta(hours=extra_hours)
            print(f"  {current_date.date()}: Overtime day - Out at {out_time.strftime('%H:%M')} (+{extra_hours}h)")
        else:
            # Slight variation for normal days
            extra_minutes = random.randint(0, 15)
            out_time = base_out_time + timedelta(minutes=extra_minutes)
            print(f"  {current_date.date()}: Regular day - Out at {out_time.strftime('%H:%M')}")

        try:
            # IN Checkin
            checkin_in = frappe.new_doc("Employee Checkin")
            checkin_in.employee = employee
            checkin_in.time = in_time
            checkin_in.log_type = "IN"
            checkin_in.insert(ignore_permissions=True)
            checkin_in.save(ignore_permissions=True)

            # OUT Checkin
            checkin_out = frappe.new_doc("Employee Checkin")
            checkin_out.employee = employee
            checkin_out.time = out_time
            checkin_out.log_type = "OUT"
            checkin_out.insert(ignore_permissions=True)
            checkin_out.save(ignore_permissions=True)

            created_checkins += 2

        except Exception as e:
            print(f"❌ Error on {current_date.date()}: {str(e)}")
            continue

    frappe.db.commit()

    summary = f"""
✅ Check-ins created successfully for {employee}!

📅 PERIOD: August 1, 2025 – August 31, 2025
📆 Total days: {len(all_days)}
⏰ Overtime days: {len(overtime_days)}
🧾 Total check-ins created: {created_checkins}
"""
    print(summary)
    return summary
