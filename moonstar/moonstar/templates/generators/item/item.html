{% extends "templates/web.html" %}
{% from "webshop/templates/includes/macros.html" import recommended_item_row %}

{% block style %}
  <link rel="stylesheet" href="/assets/moonstar/css/item.css">
  <style>
    #item-groups-display .row{
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
    }
    .filter-item-group-title{
    font-size: 30px;
    font-weight: bold;
    color: #040969;
    text-shadow: 2px 2px 4px #93b1c9;
    }

    .card-body {
        display: flex;
        flex-wrap: wrap;
        gap: 0.3rem;
        justify-content: center;
        padding: 0.6rem;
    }
    .filter-card-title {
        display: inline-block;
         background-color: #3a71bd;
        color: #fff;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        cursor: pointer;
        transition: background 0.2s, transform 0.2s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin: 0.25rem;
        font-size: 1rem;
        white-space: nowrap;
        min-width: 165px;
        text-align: center;
    }

    .filter-card-title:hover {
        background-color: #5b84bd;
        transform: translateY(-1px);
    }

    .filter-card-title.active {
        background-color: #040969;
        font-weight: bold;
    }
     @media (min-width: 850px) and (max-width: 1033px) {

        .card-body {
            gap: 0; 
            padding: 0.3rem !important;
        }
    }
      @media (min-width: 576px) and (max-width: 849px) {
        .card-body {
            gap: 0; 
            padding: 0.25rem !important;
        }
    }
    @media (max-width: 576px){
         #item-groups-display .row{
        display: flex;
      flex-direction: column;
        align-items: left;
        justify-content:  left;
    }
    .card-body {
            gap: 0; 
            padding: 0.25rem !important;
        }
    }
  </style>

    
{% endblock %}


{% block breadcrumbs %}
<div class="item-breadcrumbs small text-muted">
    {% include "templates/includes/breadcrumbs.html" %}
</div>
{% endblock %}

{% block page_content %}

<div class="product-container item-main">
    {% from "webshop/templates/includes/macros.html" import product_image %}
    <div class="item-content">
        <div class="product-page-content" itemscope itemtype="http://schema.org/Product">
            <div class="row mb-5">
                {% include "templates/generators/item/item_image.html" %}
                {% include "templates/generators/item/item_details.html" %}
            </div>
        </div>
    </div>
</div>

<!-- Additional Info/Reviews, Recommendations -->
<div class="d-flex flex-wrap">
    {% set show_recommended_items = recommended_items and shopping_cart.cart_settings.enable_recommendations %}
    {% set info_col = 'col-lg-9 col-12' if show_recommended_items else 'col-12' %}
    {% set padding_top = 'pt-0' if (show_tabs and tabs) else '' %}

    <div class="product-container mt-4 {{ padding_top }} {{ info_col }}">
        <div class="item-content {{ 'mt-minus-2' if (show_tabs and tabs) else '' }}">
            <div class="product-page-content" itemscope itemtype="http://schema.org/Product">
                {% if show_tabs and tabs %}
                    <div class="category-tabs">
                        {{ web_block("Section with Tabs", values=tabs, add_container=0, add_top_padding=0, add_bottom_padding=0) }}
                    </div>
                {% elif website_specifications %}
                    {% include "templates/generators/item/item_specifications.html" %}
                {% endif %}

                {{ doc.website_content or '' }}

                {% if shopping_cart.cart_settings.enable_reviews and not doc.has_variants %}
                    {% include "templates/generators/item/item_reviews.html" %}
                {% endif %}
            </div>
        </div>

        <!-- Item Groups Display for CK01IC02005 -->
        <div id="item-groups-display" class="mt-4" >
            <div class="row" id="item-groups-container"></div>
        </div>

        <!-- Variants List in Card Format -->
        <div id="variants-debug" class="mt-4">
            <div class="row" id="variants-container"></div>
        </div>
    </div>

    {% if show_recommended_items %}
        <div class="mt-4 col-lg-3 col-12 recommended-item-section">
            <span class="recommendation-header">{{ _("Recommended") }}</span>
            <div class="product-container mt-2 recommendation-container">
                {% for item in recommended_items %}
                    {{ recommended_item_row(item) }}
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block base_scripts %}
<script type="text/javascript" src="/assets/frappe/js/lib/jquery/jquery.min.js"></script>
{{ include_script("frappe-web.bundle.js") }}
{{ include_script("controls.bundle.js") }}
{{ include_script("dialog.bundle.js") }}

<script>
    frappe.ready(function () {
        const itemCode = "{{ doc.item_code }}";
        console.log("Item Code Used in Variant Lookup:", itemCode);

        // Check if this item should show item groups instead of variants
        if (itemCode === "CK01IC02005") {
            loadItemGroups(itemCode);
        } else {
            loadVariants(itemCode);
        }
    });

    let currentView = 'groups';
    let currentItemCode = '';
    let currentItemGroup = '';

    function loadItemGroups(itemCode) {
        currentItemCode = itemCode;
        currentView = 'groups';
        
        frappe.call({
            method: "moonstar.api.item_variants.get_item_groups_for_special_variant",
            args: { variant_of_code: itemCode },
            callback: function (response) {
                if (response && response.message && response.message.item_groups) {
                    const itemGroups = response.message.item_groups;
                    if (itemGroups.length > 0) {
                        displayItemGroups(itemGroups);
                    }
                }
            }
        });
    }

    function loadVariantsByGroup(itemCode, itemGroup) {
        currentItemCode = itemCode;
        currentItemGroup = itemGroup;
        currentView = 'variants';
        
        frappe.call({
            method: "moonstar.api.item_variants.get_variants_by_item_group",
            args: { 
                variant_of_code: itemCode,
                item_group: itemGroup
            },
            callback: function (response) {
                if (response && response.message) {
                    const variants = response.message.variants || [];
                    const routeMap = response.message.routes || {};
                    if (variants.length > 0) {
                        displayVariantsCard(variants, routeMap, itemGroup);
                    }
                }
            }
        });
    }

    function loadVariants(itemCode) {
        frappe.call({
            method: "moonstar.api.item_variants.get_item_variants_and_routes",
            args: { item_code: itemCode },
            callback: function (response) {
                if (response && response.message) {
                    const variants = response.message.variants || [];
                    const routeMap = response.message.routes || {};
                    if (variants.length > 0) {
                        displayVariantsCard(variants, routeMap);
                    }
                }
            }
        });
    }

    function displayItemGroups(itemGroups) {
        const itemGroupsContainer = document.getElementById("item-groups-container");
        if (!itemGroupsContainer) return;
        itemGroupsContainer.innerHTML = '';

        if (!itemGroups || itemGroups.length === 0) {
            itemGroupsContainer.innerHTML = "<p>No item groups found.</p>";
            return;
        }

        // Heading wrapper
        const headingWrapper = document.createElement("div");
        headingWrapper.className = "w-100 mb-4";
        headingWrapper.innerHTML = `<h2 class="filter-item-group-title text-center">Explore the Flavour Family</h2>`;
        itemGroupsContainer.appendChild(headingWrapper);

        // Cards row wrapper
        const rowWrapper = document.createElement("div");
        rowWrapper.className = "row";
        const default_item_image = "{{ frappe.db.get_single_value('Moonstar Settings', 'default_item_image') }}";

        itemGroups.forEach(function(group) {
           
            
            const groupCard = document.createElement("div");
            groupCard.className = "";
            groupCard.innerHTML = `
                <div class="filter-item-group-card">
                    <div class="card-body" style="display: flex; flex-direction: column; align-items: center;">
                        <h5 
                            class="filter-card-title" 
                            style="cursor: pointer;" 
                            onclick="handleGroupClick(this, '${currentItemCode}', '${group.item_group}')"
                        >
                            ${group.item_group}
                        </h5>
                    </div>
                </div>
            `;

            rowWrapper.appendChild(groupCard);
        });

        itemGroupsContainer.appendChild(rowWrapper);
    }

    function displayVariantsCard(variants, routeMap, itemGroupName = null) {
        const variantsContainer = document.getElementById("variants-container");
        if (!variantsContainer) return;
        variantsContainer.innerHTML = '';

        if (!variants || variants.length === 0) {
            variantsContainer.innerHTML = "<p>No variants found for this item.</p>";
            return;
        }

        // Back button and heading wrapper
        const headingWrapper = document.createElement("div");
        headingWrapper.className = "w-100 mb-4";
        
        let headingContent = '';
        if (itemGroupName && currentView === 'variants') {
            headingContent = `
                <h2 class="item-variant-title text-center">${itemGroupName}</h2>
            `;
        } else {
            headingContent = `<h2 class="item-variant-title text-center">Explore the Flavour Family</h2>`;
        }
        
        headingWrapper.innerHTML = headingContent;
        variantsContainer.appendChild(headingWrapper);

        // Cards row wrapper
        const rowWrapper = document.createElement("div");
        rowWrapper.className = "row";
        const default_item_image = "{{ frappe.db.get_single_value('Moonstar Settings', 'default_item_image') }}";

        variants.forEach(function(variant) {
            const route = routeMap[variant.name] || "#";
            const imageSrc = variant.image && variant.image.trim() !== "" ? variant.image : default_item_image;

            const variantCard = document.createElement("div");
            variantCard.className = "col-lg-4 col-md-6 col-12 mb-4";
            variantCard.innerHTML = `
                <div class="item-variant-card h-100 shadow-sm">
                    <img src="${imageSrc}"
                        class="card-img-top justify-content-center align-items-center text-center" 
                        alt="${variant.item_name}">
                    
                    <hr>
                    
                    <div class="card-body d-flex flex-column justify-content-center align-items-center text-center">
                        <h6 class="card-title">${variant.item_name}</h6>
                        <a href="/${route}" class="btn btn-primary mt-auto">View Details</a>
                    </div>
                </div>
            `;
            rowWrapper.appendChild(variantCard);
        });

        variantsContainer.appendChild(rowWrapper);
    }

// highlight filtered-item-flavour-category when clicked
    function handleGroupClick(el, itemCode, groupName) {
    document.querySelectorAll('.filter-card-title').forEach(el => el.classList.remove('active'));
    el.classList.add('active');
    loadVariantsByGroup(itemCode, groupName);
}

</script>
{% endblock %}