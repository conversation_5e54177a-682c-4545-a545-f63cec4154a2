### App Versions
```
{
	"doctor_sharing": "0.0.1",
	"erpnext": "15.55.4",
	"frappe": "15.63.0",
	"healthcare": "15.1.0",
	"hrms": "15.42.2",
	"moonstar": "0.0.1",
	"opterp_app": "0.0.1",
	"opterp_health": "0.0.1",
	"pharmacy": "0.0.1",
	"sb_health": "0.0.1"
}
```
### Route
```
sales-invoice-ui
```
### Traceback
```
Traceback (most recent call last):
  File "apps/frappe/frappe/app.py", line 115, in application
    response = frappe.api.handle(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/__init__.py", line 49, in handle
    data = endpoint(**arguments)
           ^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/api/v1.py", line 36, in handle_rpc_call
    return frappe.handler.handle()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 51, in handle
    data = execute_cmd(cmd)
           ^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 84, in execute_cmd
    return frappe.call(method, **frappe.form_dict)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/__init__.py", line 1736, in call
    return fn(*args, **newargs)
           ^^^^^^^^^^^^^^^^^^^^
  File "apps/frappe/frappe/handler.py", line 326, in run_doc_method
    method_obj = getattr(doc, method)
                 ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'HealthSalesInvoice' object has no attribute 'reset_mode_of_payments'

```
### Request Data
```
{
	"type": "POST",
	"args": {
		"docs": "{\"name\":\"ACC-SINV-2025-00250\",\"owner\":\"<EMAIL>\",\"creation\":\"2025-06-10 14:33:42.997474\",\"modified\":\"2025-06-10 14:33:42.997474\",\"modified_by\":\"<EMAIL>\",\"docstatus\":0,\"idx\":0,\"title\":\"Avash  Gyawali\",\"naming_series\":\"ACC-SINV-.YYYY.-\",\"patient\":null,\"patient_name\":null,\"customer\":\"Avash  Gyawali\",\"ref_practitioner\":null,\"customer_name\":\"Avash  Gyawali\",\"service_unit\":null,\"tax_id\":null,\"company\":\"Geofinity\",\"company_tax_id\":null,\"posting_date\":\"2025-06-10\",\"posting_time\":\"14:33:43.130023\",\"set_posting_time\":0,\"due_date\":\"2025-06-10\",\"is_pos\":1,\"pos_profile\":\"Counter\",\"is_consolidated\":0,\"is_return\":0,\"return_against\":null,\"update_outstanding_for_self\":1,\"update_billed_amount_in_sales_order\":0,\"update_billed_amount_in_delivery_note\":1,\"is_debit_note\":0,\"amended_from\":null,\"is_damaged\":0,\"cost_center\":null,\"project\":null,\"currency\":\"NPR\",\"conversion_rate\":1,\"selling_price_list\":\"Standard Selling\",\"price_list_currency\":\"NPR\",\"plc_conversion_rate\":1,\"ignore_pricing_rule\":0,\"scan_barcode\":null,\"update_stock\":1,\"set_warehouse\":\"Stores - G\",\"set_target_warehouse\":null,\"total_qty\":1,\"total_net_weight\":0,\"base_total\":1000,\"base_net_total\":1000,\"total\":1000,\"net_total\":1000,\"tax_category\":\"\",\"taxes_and_charges\":null,\"shipping_rule\":null,\"incoterm\":null,\"named_place\":null,\"base_total_taxes_and_charges\":0,\"total_taxes_and_charges\":0,\"base_grand_total\":1000,\"base_rounding_adjustment\":0,\"base_rounded_total\":1000,\"base_in_words\":\"NPR One Thousand only.\",\"grand_total\":1000,\"rounding_adjustment\":0,\"use_company_roundoff_cost_center\":0,\"rounded_total\":1000,\"in_words\":\"NPR One Thousand only.\",\"total_advance\":0,\"outstanding_amount\":0,\"customer_signature\":null,\"disable_rounded_total\":0,\"apply_discount_on\":\"Grand Total\",\"base_discount_amount\":0,\"is_cash_or_non_trade_discount\":0,\"additional_discount_account\":null,\"additional_discount_percentage\":0,\"discount_amount\":0,\"other_charges_calculation\":null,\"total_billing_hours\":0,\"total_billing_amount\":0,\"cash_bank_account\":null,\"base_paid_amount\":1000,\"paid_amount\":1000,\"base_change_amount\":0,\"change_amount\":0,\"account_for_change_amount\":\"Cash - G\",\"allocate_advances_automatically\":0,\"only_include_allocated_payments\":0,\"write_off_amount\":0,\"base_write_off_amount\":0,\"write_off_outstanding_amount_automatically\":0,\"write_off_account\":\"Write Off - G\",\"write_off_cost_center\":\"Main - G\",\"redeem_loyalty_points\":0,\"loyalty_points\":0,\"loyalty_amount\":0,\"loyalty_program\":null,\"loyalty_redemption_account\":null,\"loyalty_redemption_cost_center\":null,\"customer_address\":null,\"address_display\":null,\"contact_person\":\"Avash Gyawali-Avash  Gyawali\",\"contact_display\":\"Avash Gyawali\",\"contact_mobile\":\"\",\"contact_email\":\"<EMAIL>\",\"territory\":\"All Territories\",\"shipping_address_name\":null,\"shipping_address\":null,\"dispatch_address_name\":null,\"dispatch_address\":null,\"company_address\":null,\"company_address_display\":null,\"company_contact_person\":null,\"ignore_default_payment_terms_template\":0,\"payment_terms_template\":\"\",\"tc_name\":null,\"terms\":null,\"po_no\":\"\",\"po_date\":null,\"debit_to\":\"Debtors - G\",\"party_account_currency\":\"NPR\",\"is_opening\":\"No\",\"unrealized_profit_loss_account\":null,\"against_income_account\":\"Sales - G\",\"sales_partner\":null,\"amount_eligible_for_commission\":1000,\"commission_rate\":0,\"total_commission\":0,\"letter_head\":null,\"group_same_items\":0,\"select_print_heading\":null,\"language\":\"en\",\"subscription\":null,\"from_date\":null,\"auto_repeat\":null,\"to_date\":null,\"status\":\"Draft\",\"inter_company_invoice_reference\":null,\"campaign\":null,\"represents_company\":null,\"source\":null,\"customer_group\":\"All Customer Groups\",\"is_internal_customer\":0,\"is_discounted\":0,\"remarks\":null,\"doctype\":\"Sales Invoice\",\"taxes\":[],\"payment_schedule\":[],\"payments\":[{\"name\":\"rhee79l6ga\",\"owner\":\"<EMAIL>\",\"creation\":\"2025-06-10 14:33:42.997474\",\"modified\":\"2025-06-10 14:33:42.997474\",\"modified_by\":\"<EMAIL>\",\"docstatus\":0,\"idx\":1,\"default\":1,\"mode_of_payment\":\"Cash\",\"amount\":1000,\"reference_no\":null,\"account\":\"Cash - G\",\"type\":\"Cash\",\"base_amount\":1000,\"clearance_date\":null,\"parent\":\"ACC-SINV-2025-00250\",\"parentfield\":\"payments\",\"parenttype\":\"Sales Invoice\",\"doctype\":\"Sales Invoice Payment\"},{\"name\":\"rheu7fbac5\",\"owner\":\"<EMAIL>\",\"creation\":\"2025-06-10 14:33:42.997474\",\"modified\":\"2025-06-10 14:33:42.997474\",\"modified_by\":\"<EMAIL>\",\"docstatus\":0,\"idx\":2,\"default\":0,\"mode_of_payment\":\"Cheque\",\"amount\":0,\"reference_no\":\"\",\"account\":\"Bank Account - G\",\"type\":\"Bank\",\"base_amount\":0,\"clearance_date\":\"2025-06-10\",\"parent\":\"ACC-SINV-2025-00250\",\"parentfield\":\"payments\",\"parenttype\":\"Sales Invoice\",\"doctype\":\"Sales Invoice Payment\"},{\"name\":\"rhe5c0lsfp\",\"owner\":\"<EMAIL>\",\"creation\":\"2025-06-10 14:33:42.997474\",\"modified\":\"2025-06-10 14:33:42.997474\",\"modified_by\":\"<EMAIL>\",\"docstatus\":0,\"idx\":3,\"default\":0,\"mode_of_payment\":\"Online Payment\",\"amount\":0,\"reference_no\":\"\",\"account\":\"Bank Account - G\",\"type\":\"Bank\",\"base_amount\":0,\"clearance_date\":\"2025-06-10\",\"parent\":\"ACC-SINV-2025-00250\",\"parentfield\":\"payments\",\"parenttype\":\"Sales Invoice\",\"doctype\":\"Sales Invoice Payment\"}],\"items\":[{\"name\":\"rhe1g0qtgm\",\"owner\":\"<EMAIL>\",\"creation\":\"2025-06-10 14:33:42.997474\",\"modified\":\"2025-06-10 14:33:42.997474\",\"modified_by\":\"<EMAIL>\",\"docstatus\":0,\"idx\":1,\"barcode\":null,\"has_item_scanned\":0,\"item_code\":\"0124\",\"custom_commission\":0,\"custom_commission_amount\":0,\"item_name\":\"Paracetamol\",\"customer_item_code\":null,\"custom_commission_rate\":0,\"description\":\"Paracetamol\",\"item_group\":\"Consumable\",\"brand\":null,\"image\":\"\",\"qty\":1,\"stock_uom\":\"Nos\",\"uom\":\"Nos\",\"conversion_factor\":1,\"stock_qty\":1,\"price_list_rate\":1000,\"base_price_list_rate\":1000,\"margin_type\":\"\",\"margin_rate_or_amount\":0,\"rate_with_margin\":0,\"discount_percentage\":0,\"discount_amount\":0,\"base_rate_with_margin\":0,\"rate\":1000,\"amount\":1000,\"item_tax_template\":null,\"base_rate\":1000,\"base_amount\":1000,\"pricing_rules\":\"\",\"stock_uom_rate\":1000,\"is_free_item\":0,\"grant_commission\":1,\"net_rate\":1000,\"net_amount\":1000,\"base_net_rate\":1000,\"base_net_amount\":1000,\"delivered_by_supplier\":0,\"income_account\":\"Sales - G\",\"is_fixed_asset\":0,\"asset\":null,\"finance_book\":null,\"expense_account\":\"Cost of Goods Sold - G\",\"discount_account\":null,\"deferred_revenue_account\":null,\"service_stop_date\":null,\"enable_deferred_revenue\":0,\"service_start_date\":null,\"service_end_date\":null,\"weight_per_unit\":0,\"total_weight\":0,\"weight_uom\":null,\"warehouse\":\"Stores - G\",\"target_warehouse\":null,\"quality_inspection\":null,\"serial_and_batch_bundle\":null,\"use_serial_batch_fields\":1,\"allow_zero_valuation_rate\":0,\"incoming_rate\":5000,\"item_tax_rate\":\"{}\",\"actual_batch_qty\":0,\"serial_no\":null,\"batch_no\":\"\",\"actual_qty\":2,\"company_total_stock\":0,\"reference_dt\":null,\"reference_dn\":null,\"practitioner\":null,\"sales_order\":null,\"so_detail\":null,\"sales_invoice_item\":null,\"delivery_note\":null,\"dn_detail\":null,\"delivered_qty\":0,\"medical_department\":null,\"service_unit\":null,\"pos_invoice\":null,\"pos_invoice_item\":null,\"purchase_order\":null,\"purchase_order_item\":null,\"cost_center\":\"Main - G\",\"project\":null,\"page_break\":0,\"parent\":\"ACC-SINV-2025-00250\",\"parentfield\":\"items\",\"parenttype\":\"Sales Invoice\",\"doctype\":\"Sales Invoice Item\"}],\"pricing_rules\":[],\"timesheets\":[],\"advances\":[],\"packed_items\":[],\"sales_team\":[],\"__last_sync_on\":\"2025-06-11T04:57:42.921Z\"}",
		"method": "reset_mode_of_payments"
	},
	"headers": {},
	"error_handlers": {},
	"url": "/api/method/run_doc_method",
	"request_id": null
}
```
### Response Data
```
{
	"exception": "AttributeError: 'HealthSalesInvoice' object has no attribute 'reset_mode_of_payments'",
	"exc_type": "AttributeError"
}
```