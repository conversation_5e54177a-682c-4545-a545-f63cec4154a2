erpnext.SalesInvoiceUI.PastOrderList = class {
	constructor({ wrapper, events }) {
		this.wrapper = wrapper;
		this.events = events;
		this.current_filters = { search_term: "", status: "Draft" };
		this.pagination = {
			current_page: 1,
			items_per_page: 7,
			total_pages: 1,
			total_items: 0
		};
		this.all_invoices = [];
		this.init_component();
	}

	init_component() {
		this.prepare_dom();
		this.setup_responsive_layout();
		this.make_filter_section();
		this.bind_events();
		
	}

	prepare_dom() {
		this.wrapper.append(
			`<div class="past-order-list">
				<div class="filter-section">
					<div class="label">${__("Recent Orders")}</div>
					<div class="search-field"></div>
					<div class="status-field"></div>
				</div>
				<div class="invoices-container"></div>
				<div class="pagination-section"></div>
			</div>`
		);

		this.$component = this.wrapper.find(".past-order-list");
		this.$invoices_container = this.$component.find(".invoices-container");
		this.$pagination_section = this.$component.find(".pagination-section");
	}

	setup_responsive_layout() {
		// Remove existing styles first to avoid duplicates
		const existingStyle = document.getElementById('pos-responsive-styles');
		if (existingStyle) {
			existingStyle.remove();
		}

		const style = document.createElement('style');
		style.id = 'pos-responsive-styles';
		style.textContent = `
			/* Pagination Styles - with high specificity */
			.past-order-list .pagination-section {
				flex-shrink: 0 !important;
				padding: 1rem !important;
				background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
				border-top: 1px solid #d1d8dd !important;
				border-radius: 0 0 8px 8px !important;
			}

			.past-order-list .pagination-container {
				display: flex !important;
				justify-content: space-between !important;
				align-items: center !important;
				gap: 1rem !important;
			}

			.past-order-list .pagination-info {
				color: #6c757d !important;
				font-size: 0.875rem !important;
				font-weight: 500 !important;
			}

			.past-order-list .pagination-controls {
				display: flex !important;
				gap: 0.5rem !important;
				align-items: center !important;
			}

			/* Critical pagination button styles with maximum specificity */
			.past-order-list .pagination-section .pagination-btn {
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				width: 36px !important;
				height: 36px !important;
				border: 1px solid #d1d8dd !important;
				background: white !important;
				color: #6c757d !important;
				border-radius: 6px !important;
				cursor: pointer !important;
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
				font-size: 0.875rem !important;
				font-weight: 500 !important;
				position: relative !important;
				overflow: hidden !important;
				box-sizing: border-box !important;
			}

			.past-order-list .pagination-section .pagination-btn::before {
				content: '' !important;
				position: absolute !important;
				top: 0 !important;
				left: -100% !important;
				width: 100% !important;
				height: 100% !important;
				background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent) !important;
				transition: left 0.5s !important;
			}

			.past-order-list .pagination-section .pagination-btn:hover::before {
				left: 100% !important;
			}

			.past-order-list .pagination-section .pagination-btn:hover {
				border-color: #667eea !important;
				color: #667eea !important;
				transform: translateY(-2px) !important;
				box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2) !important;
			}

			.past-order-list .pagination-section .pagination-btn:active {
				transform: translateY(0) !important;
			}

			.past-order-list .pagination-section .pagination-btn.active {
				background: linear-gradient(135deg,rgb(102, 234, 120),rgb(38, 173, 72)) !important;
				color: white !important;
				border-color:rgb(66, 245, 245) !important;
				box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
			}

			.past-order-list .pagination-section .pagination-btn:disabled {
				opacity: 0.4 !important;
				cursor: not-allowed !important;
				transform: none !important;
				box-shadow: none !important;
			}

			.past-order-list .pagination-section .pagination-btn:disabled:hover {
				border-color: #d1d8dd !important;
				color: #6c757d !important;
				transform: none !important;
			}

			.past-order-list .pagination-section .pagination-btn:disabled::before {
				display: none !important;
			}

			.past-order-list .pagination-numbers {
				display: flex !important;
				gap: 0.25rem !important;
			}

			/* Loading, empty, and error states */
			.past-order-list .loading-state,
			.past-order-list .empty-state,
			.past-order-list .error-state {
				padding: 3rem 2rem !important;
				text-align: center !important;
				color: #8d99a6 !important;
				display: flex !important;
				flex-direction: column !important;
				align-items: center !important;
				justify-content: center !important;
				min-height: 200px !important;
			}

			.past-order-list .loading-state {
				position: relative !important;
			}

			.past-order-list .loading-state::before {
				content: '' !important;
				width: 40px !important;
				height: 40px !important;
				border: 3px solid #f3f3f3 !important;
				border-top: 3px solid #667eea !important;
				border-radius: 50% !important;
				animation: spin 1s linear infinite !important;
				margin-bottom: 1rem !important;
			}

			@keyframes spin {
				0% { transform: rotate(0deg) !important; }
				100% { transform: rotate(360deg) !important; }
			}

			.past-order-list .error-state {
				color: #e74c3c !important;
			}

			.past-order-list .empty-state {
				background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
				border-radius: 8px !important;
				margin: 1rem !important;
			}

			/* Page transition effects */
			.past-order-list .page-transition-enter {
				opacity: 0 !important;
				transform: translateX(30px) !important;
			}

			.past-order-list .page-transition-enter-active {
				opacity: 1 !important;
				transform: translateX(0) !important;
				transition: all 0.3s ease !important;
			}

			.past-order-list .page-transition-exit {
				opacity: 1 !important;
				transform: translateX(0) !important;
			}

			.past-order-list .page-transition-exit-active {
				opacity: 0 !important;
				transform: translateX(-30px) !important;
				transition: all 0.3s ease !important;
			}

			/* Mobile styles */
			@media (max-width: 768px) {
				.past-order-list .pagination-container {
					flex-direction: column !important;
					gap: 0.75rem !important;
				}

				.past-order-list .pagination-info {
					order: 2 !important;
					font-size: 0.8rem !important;
				}

				.past-order-list .pagination-controls {
					order: 1 !important;
					justify-content: center !important;
				}

				.past-order-list .invoice-wrapper {
					flex-direction: column !important;
					align-items: flex-start !important;
					gap: 0.75rem !important;
					padding: 1rem !important;
				}

				.past-order-list .invoice-total-status {
					align-self: flex-end !important;
					text-align: right !important;
					margin-left: 0 !important;
				}

				.past-order-list .filter-section,
				.past-order-list .pagination-section {
					padding: 0.75rem !important;
				}
			}

			/* Extra small screens */
			@media (max-width: 480px) {
				.past-order-list .pagination-section .pagination-btn {
					width: 32px !important;
					height: 32px !important;
					font-size: 0.8rem !important;
				}

				.past-order-list .pagination-controls {
					gap: 0.25rem !important;
				}

				.past-order-list .invoice-wrapper {
					padding: 0.75rem !important;
				}

				.past-order-list .filter-section,
				.past-order-list .pagination-section {
					padding: 0.5rem !important;
				}
			}

			/* Smooth scrolling and transitions */
			.past-order-list .invoices-container {
				scroll-behavior: smooth !important;
			}

			.past-order-list .invoice-wrapper {
				will-change: transform, opacity !important;
			}

			/* Focus styles for accessibility */
			.past-order-list .pagination-section .pagination-btn:focus {
				outline: 2px solid #667eea !important;
				outline-offset: 2px !important;
			}
		`;
		document.head.appendChild(style);
		
		// Force style recalculation
		setTimeout(() => {
			if (this.$pagination_section && this.$pagination_section.length) {
				this.$pagination_section[0].offsetHeight; // Trigger reflow
			}
		}, 0);
	}

	refresh_pagination_styles() {
		// Force style reapplication
		this.$pagination_section.find('.pagination-btn').each(function() {
			$(this).removeClass('pagination-btn').addClass('pagination-btn');
		});
		
		// Trigger reflow
		if (this.$pagination_section[0]) {
			this.$pagination_section[0].offsetHeight;
		}
	}

	bind_events() {
		this.search_field.$input.on("input", (e) => {
			clearTimeout(this.last_search);
			this.last_search = setTimeout(() => {
				const search_term = e.target.value;
				this.current_filters.search_term = search_term;
				this.pagination.current_page = 1; // Reset to first page
				this.refresh_list(search_term, this.status_field.get_value());
			}, 300);
		});
		
		const me = this;
		this.$invoices_container.on("click", ".invoice-wrapper", function () {
			const invoice_name = unescape($(this).attr("data-invoice-name"));
			me.events.open_invoice_data(invoice_name);
		});

		// Handle window resize for responsive behavior
		$(window).on('resize', this.debounce(() => {
			this.handle_resize();
		}, 250));
	}

	debounce(func, wait) {
		let timeout;
		return function executedFunction(...args) {
			const later = () => {
				clearTimeout(timeout);
				func(...args);
			};
			clearTimeout(timeout);
			timeout = setTimeout(later, wait);
		};
	}

	handle_resize() {
		// Update items per page based on screen size
		if (window.innerWidth <= 480) {
			this.pagination.items_per_page = 5;
		} else if (window.innerWidth <= 768) {
			this.pagination.items_per_page = 6;
		} else {
			this.pagination.items_per_page = 7;
		}
		
		// Recalculate pagination and refresh current page
		this.calculate_pagination();
		this.render_current_page();
		this.render_pagination();
	}

	make_filter_section() {
		const me = this;
		
		this.search_field = frappe.ui.form.make_control({
			df: {
				label: __("Search"),
				fieldtype: "Data",
				placeholder: __("Search by invoice id or customer name"),
			},
			parent: this.$component.find(".search-field"),
			render_input: true,
		});

		this.status_field = frappe.ui.form.make_control({
			df: {
				label: __("Invoice Status"),
				fieldtype: "Select",
				options: `Draft\nPaid\nConsolidated\nReturn\nPartly Paid`,
				placeholder: __("Filter by invoice status"),
				onchange: function () {
					if (me.$component.is(":visible")) {
						me.current_filters.status = this.get_value();
						me.pagination.current_page = 1; // Reset to first page
						me.refresh_list();
					}
				},
			},
			parent: this.$component.find(".status-field"),
			render_input: true,
		});

		this.search_field.toggle_label(false);
		this.status_field.toggle_label(false);
		this.status_field.set_value("Draft");
		this.current_filters.status = "Draft";

		this.settlement_button = $(`
			<button class="btn btn-primary btn-sm" style="margin-left: 10px;">
				<i class="fa fa-money"></i> ${__('Make Settlement')}
			</button>
		`);
		
		this.$component.find(".filter-section").append(this.settlement_button);
		
		this.settlement_button.on('click', () => {
			this.show_settlement_dialog();
		});
	}

	show_settlement_dialog() {
		let dialog = new frappe.ui.Dialog({
			title: __('Make Settlement'),
			fields: [
				{
					label: __('Customer'),
					fieldname: 'customer',
					fieldtype: 'Link',
					options: 'Customer',
					reqd: 1,
					get_query: function() {
						return {
							filters: {
								'disabled': 0
							}
						};
					}
				}
			],
			primary_action_label: __('Settle'),
			primary_action: (values) => {
				if (!values.customer) {
					frappe.msgprint(__('Please select customer'));
					return;
				}
				
				this.create_payment_entry(values.customer);
				dialog.hide();
			}
		});
		
		dialog.show();
	}

	create_payment_entry(customer) {
		frappe.call({
			method: 'erpnext.accounts.utils.get_balance_on',
			args: {
				party_type: 'Customer',
				party: customer,
				company: frappe.defaults.get_user_default('Company')
			},
			callback: (r) => {
				if (typeof r.message === 'number') {
					let outstanding_amount = Math.abs(r.message);

					frappe.new_doc('Payment Entry');
					setTimeout(() => {
						if (cur_frm && cur_frm.doc.doctype === 'Payment Entry') {
							cur_frm.set_value('party_type', 'Customer');
							cur_frm.set_value('party', customer);
							cur_frm.set_value('payment_type', 'Receive');
						}
					}, 500);
				} else {
					frappe.msgprint(__('Could not fetch outstanding amount for customer {0}', [customer]));
				}
			}
		});
	}

	set_filter_and_refresh(status, search_term = "") {
		console.log(`Setting filter: status=${status}, search_term=${search_term}`);
		
		this.clear_container();
		
		this.current_filters.status = status;
		this.current_filters.search_term = search_term;
		this.pagination.current_page = 1; // Reset pagination
		
		this.status_field.set_value(status);
		if (search_term) {
			this.search_field.set_value(search_term);
		} else {
			this.search_field.set_value("");
		}
		
		this.refresh_list(search_term, status);
	}

	clear_container() {
		this.$invoices_container.html(`
			<div class="loading-state">
				<div>${__("Loading orders...")}</div>
			</div>
		`);
		this.$pagination_section.html("");
	}

	refresh_list(search_term = null, status = null) {
		const final_search_term = search_term !== null ? search_term : this.current_filters.search_term;
		const final_status = status !== null ? status : this.current_filters.status;

		console.log(`Refreshing list with: search_term="${final_search_term}", status="${final_status}"`);

		this.current_filters.search_term = final_search_term;
		this.current_filters.status = final_status;

		if (this.$invoices_container.find('.loading-state').length === 0) {
			this.clear_container();
		}

		this.events.reset_summary();

		return frappe.call({
			method: "moonstar.moonstar.page.sales_invoice_ui.sales_invoice_ui.get_past_order_list",
			freeze: true,
			args: { 
				search_term: final_search_term, 
				status: final_status 
			},
			callback: (response) => {
				this.$invoices_container.html("");
				
				if (response.message && response.message.length > 0) {
					this.all_invoices = response.message;
					this.calculate_pagination();
					this.render_current_page();
					this.render_pagination();
					
					this.update_filter_label(final_status, response.message.length);
				} else {
					this.all_invoices = [];
					this.pagination.total_pages = 1;
					this.pagination.total_items = 0;
					
					this.$invoices_container.append(
						`<div class="empty-state">
							<div style="font-size: 1.2rem; margin-bottom: 0.5rem;">📋</div>
							<div>${__("No orders found for the selected criteria")}</div>
							<div style="margin-top: 0.5rem; font-size: 0.9em; opacity: 0.8;">${__("Status")}: ${final_status}</div>
						</div>`
					);
					this.$pagination_section.html("");
				}
			},
			error: (error) => {
				console.error("Error fetching past orders:", error);
				this.$invoices_container.html(`
					<div class="error-state">
						<div style="font-size: 1.2rem; margin-bottom: 0.5rem;">⚠️</div>
						<div>${__("Error loading past orders")}</div>
						<div style="margin-top: 0.5rem; font-size: 0.9em;">${__("Please try again")}</div>
					</div>
				`);
				this.$pagination_section.html("");
				frappe.msgprint(__("Error loading past orders. Please try again."));
			}
		});
	}

	calculate_pagination() {
		this.pagination.total_items = this.all_invoices.length;
		this.pagination.total_pages = Math.ceil(this.pagination.total_items / this.pagination.items_per_page);
		
		// Ensure current page is valid
		if (this.pagination.current_page > this.pagination.total_pages) {
			this.pagination.current_page = Math.max(1, this.pagination.total_pages);
		}
	}

	render_current_page() {
		const start_index = (this.pagination.current_page - 1) * this.pagination.items_per_page;
		const end_index = start_index + this.pagination.items_per_page;
		const current_page_invoices = this.all_invoices.slice(start_index, end_index);

		this.$invoices_container.html("");

		if (current_page_invoices.length > 0) {
			current_page_invoices.forEach((invoice, index) => {
				const invoice_html = this.get_invoice_html(invoice);
				const $invoice = $(invoice_html);
				
				// Add staggered animation delay
				$invoice.css('animation-delay', `${0.1 + (index * 0.05)}s`);
				
				this.$invoices_container.append($invoice);
			});
		}
	}

	render_pagination() {
		if (this.pagination.total_pages <= 1) {
			this.$pagination_section.html("");
			return;
		}

		const start_item = (this.pagination.current_page - 1) * this.pagination.items_per_page + 1;
		const end_item = Math.min(start_item + this.pagination.items_per_page - 1, this.pagination.total_items);

		let pagination_html = `
			<div class="pagination-container">
				<div class="pagination-info">
					${__("Showing")} ${start_item}-${end_item} ${__("of")} ${this.pagination.total_items} ${__("orders")}
				</div>
				<div class="pagination-controls">
					<button class="pagination-btn" data-action="prev" ${this.pagination.current_page === 1 ? 'disabled' : ''}>
						<i class="fa fa-angle-left"></i>
					</button>
					<div class="pagination-numbers">
		`;

		// Generate page numbers
		const max_visible_pages = 5;
		let start_page = Math.max(1, this.pagination.current_page - Math.floor(max_visible_pages / 2));
		let end_page = Math.min(this.pagination.total_pages, start_page + max_visible_pages - 1);

		// Adjust start_page if we're near the end
		if (end_page - start_page + 1 < max_visible_pages) {
			start_page = Math.max(1, end_page - max_visible_pages + 1);
		}

		for (let i = start_page; i <= end_page; i++) {
			pagination_html += `
				<button class="pagination-btn ${i === this.pagination.current_page ? 'active' : ''}" data-action="page" data-page="${i}">
					${i}
				</button>
			`;
		}

		pagination_html += `
					</div>
					<button class="pagination-btn" data-action="next" ${this.pagination.current_page === this.pagination.total_pages ? 'disabled' : ''}>
						<i class="fa fa-angle-right"></i>
					</button>
				</div>
			</div>
		`;

		this.$pagination_section.html(pagination_html);

		setTimeout(() => {
			this.refresh_pagination_styles();
		}, 100);

		// Bind pagination events
		this.$pagination_section.find('.pagination-btn').on('click', (e) => {
			const $btn = $(e.currentTarget);
			if ($btn.prop('disabled')) return;

			const action = $btn.data('action');
			let new_page = this.pagination.current_page;

			switch (action) {
				case 'first':
					new_page = 1;
					break;
				case 'prev':
					new_page = Math.max(1, this.pagination.current_page - 1);
					break;
				case 'next':
					new_page = Math.min(this.pagination.total_pages, this.pagination.current_page + 1);
					break;
				case 'last':
					new_page = this.pagination.total_pages;
					break;
				case 'page':
					new_page = parseInt($btn.data('page'));
					break;
			}

			if (new_page !== this.pagination.current_page) {
				this.goto_page(new_page);
			}
		});
	}

	goto_page(page) {
		if (page < 1 || page > this.pagination.total_pages || page === this.pagination.current_page) {
			return;
		}

		// Add smooth transition effect
		this.$invoices_container.addClass('page-transition-exit');
		
		setTimeout(() => {
			this.pagination.current_page = page;
			this.render_current_page();
			this.render_pagination();
			
			this.$invoices_container.removeClass('page-transition-exit');
			this.$invoices_container.addClass('page-transition-enter');
			
			setTimeout(() => {
				this.$invoices_container.removeClass('page-transition-enter');
			}, 300);
		}, 150);
	}

	update_filter_label(status, count) {
		const label = this.$component.find(".label");
		label.html(`${__("Recent Orders")} - ${status} (${count})`);
	}

	get_invoice_html(invoice) {
		const posting_datetime = frappe.datetime.str_to_user(
			invoice.posting_date + " " + invoice.posting_time
		);
		
		return `<div class="invoice-wrapper" data-invoice-name="${escape(invoice.name)}">
				<div class="invoice-name-date">
					<div class="invoice-name">${invoice.name}</div>
					<div class="invoice-customer">
						<svg class="customer-icon" width="12" height="12" viewBox="0 0 24 24" stroke="currentColor" fill="none">
							<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
							<circle cx="12" cy="7" r="4"></circle>
						</svg>
						${frappe.ellipsis(invoice.customer, 20)}
					</div>
				</div>
				<div class="invoice-total-status">
					<div class="invoice-total">${format_currency(invoice.grand_total, invoice.currency) || 0}</div>
					<div class="invoice-datetime">${posting_datetime}</div>
				</div>
			</div>`;
	}

	toggle_component(show) {
		if (show) {
			this.$component.css("display", "flex");
			if (!this.current_filters.status) {
				this.current_filters.status = "Draft";
				this.status_field.set_value("Draft");
			}
			this.pagination.current_page = 1;
			this.refresh_list();
			this.handle_resize();
		} else {
			this.$component.css("display", "none");
			this.clear_container();
		}
	}

	reset_filters() {
		this.current_filters = { search_term: "", status: "Draft" };
		this.pagination.current_page = 1;
		this.search_field.set_value("");
		this.status_field.set_value("Draft");
		this.$component.find(".label").html(__("Recent Orders"));
	}

	scroll_to_invoice(invoice_name) {
		// Find which page contains this invoice
		const invoice_index = this.all_invoices.findIndex(inv => inv.name === invoice_name);
		if (invoice_index !== -1) {
			const target_page = Math.ceil((invoice_index + 1) / this.pagination.items_per_page);
			
			if (target_page !== this.pagination.current_page) {
				this.goto_page(target_page);
			}
			
			// Highlight the invoice after page load
			setTimeout(() => {
				const invoice_element = this.$invoices_container.find(`[data-invoice-name="${escape(invoice_name)}"]`);
				if (invoice_element.length) {
					// Add temporary highlight effect
					invoice_element.css({
						'background': 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
						'transform': 'scale(1.02)',
						'box-shadow': '0 4px 20px rgba(102, 126, 234, 0.2)'
					});
					
					// Remove highlight after 2 seconds
					setTimeout(() => {
						invoice_element.css({
							'background': '',
							'transform': '',
							'box-shadow': ''
						});
					}, 2000);
				}
			}, 400);
		}
	}

	get_scroll_position() {
		return {
			page: this.pagination.current_page,
			scroll_top: this.$invoices_container.scrollTop()
		};
	}

	set_scroll_position(position) {
		if (position.page && position.page !== this.pagination.current_page) {
			this.goto_page(position.page);
		}
		if (position.scroll_top) {
			setTimeout(() => {
				this.$invoices_container.scrollTop(position.scroll_top);
			}, 100);
		}
	}

	// Additional utility methods for better UX
	get_current_page_info() {
		return {
			current_page: this.pagination.current_page,
			total_pages: this.pagination.total_pages,
			items_per_page: this.pagination.items_per_page,
			total_items: this.pagination.total_items,
			start_item: (this.pagination.current_page - 1) * this.pagination.items_per_page + 1,
			end_item: Math.min(this.pagination.current_page * this.pagination.items_per_page, this.pagination.total_items)
		};
	}

	refresh_pagination_only() {
		this.calculate_pagination();
		this.render_pagination();
	}

	// Keyboard navigation support
	setup_keyboard_navigation() {
		$(document).on('keydown.pagination', (e) => {
			if (!this.$component.is(':visible')) return;
			
			// Only handle if no input is focused
			if ($(e.target).is('input, textarea, select')) return;
			
			switch(e.key) {
				case 'ArrowLeft':
					if (this.pagination.current_page > 1) {
						e.preventDefault();
						this.goto_page(this.pagination.current_page - 1);
					}
					break;
				case 'ArrowRight':
					if (this.pagination.current_page < this.pagination.total_pages) {
						e.preventDefault();
						this.goto_page(this.pagination.current_page + 1);
					}
					break;
				case 'Home':
					if (this.pagination.current_page !== 1) {
						e.preventDefault();
						this.goto_page(1);
					}
					break;
				case 'End':
					if (this.pagination.current_page !== this.pagination.total_pages) {
						e.preventDefault();
						this.goto_page(this.pagination.total_pages);
					}
					break;
			}
		});
	}

	destroy() {
		// Clean up event listeners
		$(document).off('keydown.pagination');
		$(window).off('resize');
		
		// Clear timers
		if (this.last_search) {
			clearTimeout(this.last_search);
		}
	}
};