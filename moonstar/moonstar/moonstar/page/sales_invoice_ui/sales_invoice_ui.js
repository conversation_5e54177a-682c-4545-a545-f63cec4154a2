frappe.provide("erpnext.SalesInvoiceUI");

frappe.pages["sales-invoice-ui"].on_page_load = function (wrapper) {
	frappe.ui.make_app_page({
		parent: wrapper,
		title: __("Daily Sales"),
		single_column: true,
	});

	frappe.require("sales_invoice_ui.bundle.js", function () {
		wrapper.sales_invoice_ui = new erpnext.SalesInvoiceUI.Controller(wrapper);
		window.cur_sales_invoice_ui = wrapper.sales_invoice_ui;
	});
};


frappe.pages["sales-invoice-ui"].refresh = function (wrapper) {
	if (document.scannerDetectionData) {
		onScan.detachFrom(document);
		wrapper.sales_invoice_ui.wrapper.html("");
		wrapper.sales_invoice_ui.initialize();
	}
};