erpnext.SalesInvoiceUI = erpnext.SalesInvoiceUI || {};

erpnext.SalesInvoiceUI.Controller = class {
	constructor(wrapper) {
		this.wrapper = $(wrapper).find(".layout-main-section");
		this.page = wrapper.page;
		this.check_opening_entry();
	}


	fetch_opening_entry() {
		return frappe.call("erpnext.selling.page.point_of_sale.point_of_sale.check_opening_entry", {
			user: frappe.session.user,
		});
	}

	check_opening_entry() {
		this.fetch_opening_entry().then((r) => {
			if (r.message.length) {
				this.prepare_app_defaults(r.message[0]);
			} else {
				this.create_opening_voucher();
			}
		});
	}

	async create_opening_voucher() {
	const me = this;
	
	const check_existing_opening = async () => {
		try {
			const result = await frappe.call({
				method: "frappe.client.get_list",
				args: {
					doctype: "POS Opening Entry",
					filters: {
						user: frappe.session.user,
						docstatus: 1,
						status: "Open"
					},
					fields: ["name", "pos_profile", "company"],
					limit: 1,
					order_by: "creation desc"
				}
			});
			
			return result.message && result.message.length > 0 ? result.message[0] : null;
		} catch (error) {
			console.error("Error checking existing POS Opening Entry:", error);
			return null;
		}
	};

	const existing_opening = await check_existing_opening();
	
	if (existing_opening) {
		frappe.show_alert({
			message: __("Using existing POS Opening Entry: {0}", [existing_opening.name]),
			indicator: "green"
		});
		
		me.prepare_app_defaults(existing_opening);
		return;
	}

	const table_fields = [
		{
			fieldname: "mode_of_payment",
			fieldtype: "Link",
			in_list_view: 1,
			label: __("Mode of Payment"),
			options: "Mode of Payment",
			reqd: 1,
		},
		{
			fieldname: "opening_amount",
			fieldtype: "Currency",
			in_list_view: 1,
			label: __("Opening Amount"),
			options: "company:company_currency",
			onchange: function () {
				dialog.fields_dict.balance_details.df.data.some((d) => {
					if (d.idx == this.doc.idx) {
						d.opening_amount = this.value;
						dialog.fields_dict.balance_details.grid.refresh();
						return true;
					}
				});
			},
		},
	];



	const fetch_pos_payment_methods = () => {
		const pos_profile = dialog.fields_dict.pos_profile.get_value();
		if (!pos_profile) return;
		frappe.db.get_doc("POS Profile", pos_profile).then(({ payments }) => {
			dialog.fields_dict.balance_details.df.data = [];
			payments.forEach((pay) => {
				const { mode_of_payment } = pay;
				dialog.fields_dict.balance_details.df.data.push({ mode_of_payment, opening_amount: "0" });
			});
			dialog.fields_dict.balance_details.grid.refresh();
		});
	};

	const dialog = new frappe.ui.Dialog({
		title: __("Create POS Opening Entry"),
		static: true,
		fields: [
			{
				fieldtype: "Link",
				label: __("Company"),
				default: frappe.defaults.get_default("company"),
				options: "Company",
				fieldname: "company",
				reqd: 1,
			},
			{
				fieldtype: "Link",
				label: __("POS Profile"),
				options: "POS Profile",
				fieldname: "pos_profile",
				reqd: 1,
				get_query: () => pos_profile_query(),
				onchange: () => fetch_pos_payment_methods(),
			},
			{
				fieldname: "balance_details",
				fieldtype: "Table",
				label: __("Opening Balance Details"),
				cannot_add_rows: false,
				in_place_edit: true,
				reqd: 1,
				data: [],
				fields: table_fields,
			},
		],
		primary_action: async function ({ company, pos_profile, balance_details }) {
			if (!balance_details.length) {
				frappe.show_alert({
					message: __("Please add Mode of payments and opening balance details."),
					indicator: "red",
				});
				return frappe.utils.play_sound("error");
			}

			balance_details = balance_details.filter((d) => d.mode_of_payment);

			const method = "erpnext.selling.page.point_of_sale.point_of_sale.create_opening_voucher";
			const res = await frappe.call({
				method,
				args: { pos_profile, company, balance_details },
				freeze: true,
			});
			!res.exc && me.prepare_app_defaults(res.message);
			dialog.hide();
		},
		primary_action_label: __("Submit"),
	});

	dialog.show();

	const pos_profile_query = () => {
		return {
			query: "erpnext.accounts.doctype.pos_profile.pos_profile.pos_profile_query",
			filters: { company: dialog.fields_dict.company.get_value() },
		};
	};
}

	async prepare_app_defaults(data) {
		this.pos_opening = data.name;
		this.company = data.company;
		this.pos_profile = data.pos_profile;
		this.pos_opening_time = data.period_start_date;
		this.item_stock_map = {};
		this.settings = {};

		frappe.db.get_value("Stock Settings", undefined, "allow_negative_stock").then(({ message }) => {
			this.allow_negative_stock = flt(message.allow_negative_stock) || false;
		});

		frappe.call({
			method: "erpnext.selling.page.point_of_sale.point_of_sale.get_pos_profile_data",
			args: { pos_profile: this.pos_profile },
			callback: (res) => {
				const profile = res.message;
				Object.assign(this.settings, profile);
				this.settings.customer_groups = profile.customer_groups.map((group) => group.name);
				this.make_app();
			},
		});
	}

	set_opening_entry_status() {
		this.page.set_title_sub(
			`<span class="indicator orange">
				<a class="text-muted" href="#Form/POS%20Opening%20Entry/${this.pos_opening}">
					Opened at ${frappe.datetime.str_to_user(this.pos_opening_time)}
				</a>
			</span>`
		);
	}

	make_app() {
		this.prepare_dom();
		this.prepare_components();
		this.prepare_menu();
		this.prepare_fullscreen_btn();
		this.make_new_invoice();
	}

	prepare_dom() {
		this.wrapper.append(`<div class="point-of-sale-app"></div>`);
		this.$components_wrapper = this.wrapper.find(".point-of-sale-app");
	}

	prepare_components() {
		this.init_item_selector();
		this.init_item_details();
		this.init_item_cart();
		this.init_payments();
		this.init_recent_order_list();
		this.init_order_summary();
	}

	prepare_menu() {
		this.page.clear_menu();

		this.page.add_menu_item(__("Open Form View"), this.open_form_view.bind(this), false, "Ctrl+F");

		this.page.add_menu_item(
			__("Toggle Recent Orders"),
			this.toggle_recent_order.bind(this),
			false,
			"Ctrl+O"
		);

		this.page.add_menu_item(__("Save as Draft"), this.save_draft_invoice.bind(this), false, "Ctrl+S");

		this.page.add_menu_item(__("Close the POS"), this.close_pos.bind(this), false, "Shift+Ctrl+C");
	}

	prepare_fullscreen_btn() {
		this.page.page_actions.find(".custom-actions").empty();
		this.create_custom_button_container();
		this.create_top_bar_buttons();

		this.page.add_button(__("Full Screen"), null, {
			btn_class: "btn-default fullscreen-btn"
		});

		this.bind_fullscreen_events();
	}

	create_top_bar_buttons() {
		this.page.wrapper.find('.pos-top-buttons').remove();
		
		const topButtonsHtml = `
			<div class="pos-top-buttons" style="display: inline-flex; gap: 8px; margin-left: 10px;">
				<button class="btn btn-default btn-sm stock-ledger-btn" title="Stock Ledger">
					<i class="fa fa-list"></i> Stock Ledger
				</button>
				<button class="btn btn-default btn-sm add-location-btn" title="Add Location">
					<i class="fa fa-plus"></i> Add Location
				</button>
				<button class="btn btn-default btn-sm return-sales-btn" title="Add Sales">
					Return Sales
				</button>
			</div>
		`;
		
		const refreshBtn = this.page.wrapper.find('.new-order-btn');
		if (refreshBtn.length) {
			refreshBtn.after(topButtonsHtml);
		} else {
			this.page.page_actions.append(topButtonsHtml);
		}

		$(".stock-ledger-btn").on("click", () => {
			this.store_navigation_state();
			window.location.href = "/app/query-report/Stock Ledger";

			//we are not using frappe.set_route here because it messes with page on load
			// Contact Ayush for more clarification on this.
			// frappe.set_route("query-report", "Stock Ledger");
		});

		$(".add-location-btn").on("click", () => {
			this.store_navigation_state();

			window.location.href = "/app/location/new-location";
			// frappe.new_doc("Location");
		});

		$(".return-sales-btn").on("click", () => {
			this.store_navigation_state();

			window.location.href = "/app/sales-invoice/new-sales-invoice";
			// frappe.new_doc("Location");
		});
		
		this.add_top_button_styles();
	}

	store_navigation_state() {
		sessionStorage.setItem('pos_navigated_away', 'true');
		sessionStorage.setItem('pos_return_time', Date.now().toString());
	}

	add_top_button_styles() {
		const topStyles = `
			<style>
				.pos-top-buttons {
					display: inline-flex;
					gap: 8px;
					margin-left: 10px;
					vertical-align: middle;
				}

				.pos-top-buttons .btn {
					padding: 4px 8px;
					font-size: 12px;
					height: 30px;
					display: flex;
					align-items: center;
					gap: 4px;
					border-radius: 4px;
					background-color: #f8f9fa;
					border: 1px solid #dee2e6;
					color: #495057;
					transition: all 0.2s ease;
					white-space: nowrap;
				}

				.pos-top-buttons .btn:hover {
					background-color: #e9ecef;
					transform: translateY(-1px);
				}

				.pos-top-buttons .btn i {
					font-size: 12px;
				}

				/* Responsive adjustments */
				@media (max-width: 768px) {
					.pos-top-buttons .btn {
						padding: 3px 6px;
						font-size: 11px;
						height: 28px;
					}
					
					.pos-top-buttons .btn i {
						font-size: 11px;
					}
				}

				@media (max-width: 480px) {
					.pos-top-buttons {
						gap: 4px;
						margin-left: 5px;
					}
					
					.pos-top-buttons .btn {
						padding: 2px 4px;
						font-size: 10px;
						height: 26px;
					}
					
					.pos-top-buttons .btn i {
						font-size: 10px;
					}
				}
			</style>
		`;
		
		$('#pos-top-button-styles').remove();
		
		$('head').append(`<div id="pos-top-button-styles">${topStyles}</div>`);
	}

	create_custom_button_container() {
	this.page.wrapper.find('.pos-custom-buttons').remove();
	
	const buttonContainerHtml = `
		<div class="pos-custom-buttons" style="
			position: relative;
			top: 10%;
			left: 50%;
			transform: translateX(-50%);
			z-index: 1000;
			display: flex;
			gap: 20px;
			justify-content: center;
			align-items: center;
			background: rgba(255, 255, 255, 0.95);
		">	

			<div>
				<button class="fa fa-refresh btn-secondary new-order-btn">
				</button>
			</div>

			<div class="pos-filter-buttons" style="display: flex; gap: 20px;">
				<button class="btn btn-default partly-paid-btn pos-filter-btn" data-filter="Partly Paid">
					${__("Outstanding")}
				</button>
				<button class="btn btn-default paid-btn pos-filter-btn" data-filter="Paid">
					${__("Paid")}
				</button>
			</div>


		</div>
	`;
	
	this.page.wrapper.prepend(buttonContainerHtml);

	$(".new-order-btn").on("click", function () {
		sessionStorage.removeItem('pos_navigated_away');
		sessionStorage.removeItem('pos_return_time');
		window.location.reload();
	});

	
	this.add_responsive_styles();
	
	this.active_filter_btn = null;
	
	this.page.wrapper.on('click', '.pos-filter-btn', (e) => {
		const button = e.target;
		const status = $(button).data('filter');
		this.handle_filter_button_click(button, status);
	});

	}

	add_responsive_styles() {
	// Add responsive CSS styles
	const styles = `
		<style>
			.new-order-btn {
				position: fixed;
				left:10px;
				top:13%;
				z-index: 1000;
				background-color: #ccc;
				color: #333;
				padding: 7px 10px;
				font-size: 13px;
				border: none;
				border-radius: 4px;
				font-weight: 500;
				white-space: nowrap;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 4px;
				transition: background-color 0.2s ease;
			}

			.new-order-btn:hover {
				background-color: #bbb;
			}
			/* Base styles for desktop */
			.pos-custom-buttons {
				position: fixed;
				top: 70px;
				left: 50%;
				transform: translateX(-50%);
				z-index: 1000;
				display: flex;
				gap: 25px;
				justify-content: center;
				align-items: center;
				background: rgba(255, 255, 255, 0.95);
				padding: 12px 20px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
				backdrop-filter: blur(10px);
				border: 1px solid rgba(0, 0, 0, 0.1);
				max-width: auto;
			}		

			.pos-custom-buttons .btn {
				padding: 8px 16px;
				font-size: 14px;
				min-width: 100px;
				white-space: nowrap;
				border-radius: 6px;
				font-weight: 500;
			}

			/* Ensure main content doesn't overlap */
			.layout-main-section {
				margin-top: 5px !important;
			}

			/* Large tablets and small laptops (1024px and below) */
			@media (max-width: 1024px) {
				.pos-custom-buttons {
					top: 65px;
					gap: 14px;
					padding: 10px 18px;
				}
				
				.pos-custom-buttons .btn {
					padding: 7px 14px;
					font-size: 13px;
					min-width: 90px;
				}
			}

			/* Tablets (768px and below) */
			@media (max-width: 768px) {
				.pos-custom-buttons {
					top: 80px;
					gap: 12px;
					padding: 8px 16px;
					max-width: 95%;
				}

				.pos-custom-buttons .btn {
					padding: 6px 12px;
					font-size: 12px;
					min-width: 80px;
				}

				.layout-main-section {
					margin-top: 5px !important;
				}
			}

			/* Small tablets and large phones (640px and below) */
			@media (max-width: 640px) {
				.pos-custom-buttons {
					top: 65px;
					gap: 10px;
					padding: 6px 12px;
				}

				.pos-custom-buttons .btn {
					padding: 5px 10px;
					font-size: 11px;
					min-width: 70px;
				}

				.layout-main-section {
					margin-top: 5px !important;
				}
			}

			/* Mobile phones (480px and below) */
			@media (max-width: 480px) {
				.pos-custom-buttons {
					top: 15%;
					gap: 8px;
					padding: 6px 10px;
					flex-wrap: nowrap; /* Keep buttons in one row */
				}

				.pos-custom-buttons .btn {
					padding: 4px 8px;
					font-size: 10px;
					min-width: 60px;
					flex: 1; /* Equal width distribution */
				}

				.layout-main-section {
					margin-top: 5px !important;
				}
			}

			/* Small mobile phones (400px and below) */
			@media (max-width: 400px) {
				.pos-custom-buttons {
					top: 15%;
					gap: 6px;
					padding: 4px 8px;
				}

				.pos-custom-buttons .btn {
					padding: 3px 6px;
					font-size: 9px;
					min-width: 50px;
				}

				.layout-main-section {
					margin-top: 5px !important;
				}
			}

			/* Very small screens (360px and below) */
			@media (max-width: 360px) {
				.pos-custom-buttons {
					position: relative;
					top: auto;
					left: auto;
					transform: none;
					margin: 8px auto;
					max-width: 100%;
					gap: 4px;
					padding: 4px 6px;
				}

				.pos-custom-buttons .btn {
					padding: 3px 5px;
					font-size: 8px;
					min-width: 45px;
				}

				.layout-main-section {
					margin-top: 5px !important;
				}
			}

			/* Extra small screens (320px and below) */
			@media (max-width: 320px) {
				.pos-custom-buttons {
					flex-wrap: wrap;
					gap: 3px;
					padding: 3px 5px;
				}

				.pos-custom-buttons .btn {
					padding: 2px 4px;
					font-size: 8px;
					min-width: 40px;
					margin: 1px;
				}

				.layout-main-section {
					margin-top: 25px !important;
				}
			}

			/* Landscape orientation adjustments for mobile */
			@media (max-height: 500px) and (orientation: landscape) {
				.pos-custom-buttons {
					position: relative;
					top: auto;
					margin: 5px auto;
					padding: 3px 8px;
				}

				.pos-custom-buttons .btn {
					padding: 2px 6px;
					font-size: 9px;
				}

				.layout-main-section {
					margin-top: 10px !important;
				}
			}

			/* Hover effects for larger screens */
			@media (min-width: 769px) {
				.pos-custom-buttons .btn:hover {
					transform: translateY(-1px);
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
					transition: all 0.2s ease;
				}
			}

			/* Active button styling */
			.pos-custom-buttons .btn-primary {
				background-color: #007bff;
				border-color: #007bff;
				color: white;
			}

			.pos-custom-buttons .btn-default {
				background-color: #f8f9fa;
				border-color: #dee2e6;
				color: #495057;
			}
		</style>
	`;

	
	// Remove existing styles if any
	$('#pos-custom-styles').remove();
	
	// Add styles to head
	$('head').append(`<div id="pos-custom-styles">${styles}</div>`);
	}


	bind_fullscreen_events() {
		this.$fullscreen_btn = this.page.page_actions.find(".fullscreen-btn");

		this.$fullscreen_btn.on("click", function () {
			if (!document.fullscreenElement) {
				document.documentElement.requestFullscreen();
			} else if (document.exitFullscreen) {
				document.exitFullscreen();
			}
		});

		$(document).on("fullscreenchange", this.handle_fullscreen_change_event.bind(this));
	}

	handle_fullscreen_change_event() {
		let enable_fullscreen_label = __("Full Screen");
		let exit_fullscreen_label = __("Exit Full Screen");

		if (document.fullscreenElement) {
			this.$fullscreen_btn[0].innerText = exit_fullscreen_label;
		} else {
			this.$fullscreen_btn[0].innerText = enable_fullscreen_label;
		}
	}

	open_form_view() {
		frappe.model.sync(this.frm.doc);
		frappe.set_route("Form", this.frm.doc.doctype, this.frm.doc.name);
	}

	handle_filter_button_click(button, status) {
		console.log(`Filter button clicked: ${status}`);
		
		// Update button states
		this.update_button_states(button);
		
		// If clicking the same button twice, toggle off
		if (this.active_filter_btn === button) {
			this.toggle_recent_order_list(false);
			this.active_filter_btn = null;
			this.remove_button_active_states();
			return;
		}
		
		// Set the new active button
		this.active_filter_btn = button;
		
		// Show the recent order list with the filter
		this.toggle_recent_order_with_filter(status);
	}


	update_button_states(activeButton) {
		// Remove active state from all filter buttons
		this.page.wrapper.find(".pos-filter-btn")
			.removeClass("btn-primary")
			.addClass("btn-default");
		
		// Add active state to clicked button
		$(activeButton).removeClass("btn-default").addClass("btn-primary");
	}


	toggle_recent_order_with_filter(status) {
		console.log(`Toggling recent order with filter: ${status}`);
		
		// Ensure components are properly initialized
		if (!this.recent_order_list) {
			console.error("Recent order list not initialized");
			return;
		}
		
		// First, ensure other components are hidden
		this.toggle_components(false);
		
		// Show the recent order list
		this.recent_order_list.toggle_component(true);
		this.order_summary.toggle_component(true);
		
		// Wait for component to be fully visible before setting filter
		setTimeout(() => {
			if (this.recent_order_list.$component.is(":visible")) {
				this.recent_order_list.set_filter_and_refresh(status);
			} else {
				console.error("Recent order list component not visible after toggle");
			}
		}, 100);
	}

	toggle_recent_order() {
		const show = this.recent_order_list.$component.is(":hidden");
		
		if (!show) {
			// If hiding, also reset button states
			this.active_filter_btn = null;
			this.remove_button_active_states();
			// Reset filters when hiding
			this.recent_order_list.reset_filters();
		}
		
		this.toggle_recent_order_list(show);
	}

	save_draft_invoice() {
		if (!this.$components_wrapper.is(":visible")) return;

		if (this.frm.doc.items.length == 0) {
			frappe.show_alert({
				message: __("You must add atleast one item to save it as draft."),
				indicator: "red",
			});
			frappe.utils.play_sound("error");
			return;
		}

		this.frm
			.save(undefined, undefined, undefined, () => {
				frappe.show_alert({
					message: __("There was an error saving the document."),
					indicator: "red",
				});
				frappe.utils.play_sound("error");
			})
			.then(() => {
				frappe.run_serially([
					() => frappe.dom.freeze(),
					() => this.make_new_invoice(),
					() => frappe.dom.unfreeze(),
				]);
			});
	}

	close_pos() {
		if (!this.$components_wrapper.is(":visible")) return;

		let voucher = frappe.model.get_new_doc("POS Closing Entry");
		voucher.pos_profile = this.frm.doc.pos_profile;
		voucher.user = frappe.session.user;
		voucher.company = this.frm.doc.company;
		voucher.pos_opening_entry = this.pos_opening;
		voucher.period_end_date = frappe.datetime.now_datetime();
		voucher.posting_date = frappe.datetime.now_date();
		voucher.posting_time = frappe.datetime.now_time();
		frappe.set_route("Form", "POS Closing Entry", voucher.name);
	}

	init_item_selector() {
		// Ensure ItemSelector class exists
		if (!erpnext.SalesInvoiceUI.ItemSelector) {
			console.error("ItemSelector class not found");
			return;
		}

		this.item_selector = new erpnext.SalesInvoiceUI.ItemSelector({
			wrapper: this.$components_wrapper,
			pos_profile: this.pos_profile,
			settings: this.settings,
			events: {
				item_selected: (args) => this.on_cart_update(args),
				get_frm: () => this.frm || {},
			},
		});
	}

	init_item_cart() {
		// Ensure ItemCart class exists
		if (!erpnext.SalesInvoiceUI.ItemCart) {
			console.error("ItemCart class not found");
			return;
		}

		this.cart = new erpnext.SalesInvoiceUI.ItemCart({
			wrapper: this.$components_wrapper,
			settings: this.settings,
			events: {
				get_frm: () => this.frm,

				cart_item_clicked: (item) => {
					const item_row = this.get_item_from_frm(item);
					this.item_details.toggle_item_details_section(item_row);
				},

				numpad_event: (value, action) => this.update_item_field(value, action),

				checkout: () => this.save_and_checkout(),

				edit_cart: () => this.payment.edit_cart(),

				customer_details_updated: (details) => {
					this.item_selector.load_items_data();
					this.customer_details = details;
					this.payment.render_loyalty_points_payment_mode();
				},
			},
		});
	}

	init_item_details() {
		if (!erpnext.SalesInvoiceUI.ItemDetails) {
			console.error("ItemDetails class not found");
			return;
		}

		this.item_details = new erpnext.SalesInvoiceUI.ItemDetails({
			wrapper: this.$components_wrapper,
			settings: this.settings,
			events: {
				get_frm: () => this.frm,

				toggle_item_selector: (minimize) => {
					this.item_selector.toggle_component(!minimize);
					this.cart.toggle_numpad(minimize);
				},

				form_updated: (item, field, value) => {
					const item_row = frappe.model.get_doc(item.doctype, item.name);
					if (item_row && item_row[field] != value) {
						const args = {
							field,
							value,
							item: this.item_details.current_item,
						};
						return this.on_cart_update(args);
					}

					return Promise.resolve();
				},

				highlight_cart_item: (item) => {
					const cart_item = this.cart.get_cart_item(item);
					this.cart.toggle_item_highlight(cart_item);
				},

				item_field_focused: (fieldname) => {
					this.cart.toggle_numpad_field_edit(fieldname);
				},
				set_value_in_current_cart_item: (selector, value) => {
					this.cart.update_selector_value_in_cart_item(
						selector,
						value,
						this.item_details.current_item
					);
				},
				clone_new_batch_item_in_frm: (batch_serial_map, item) => {
					// called if serial nos are 'auto_selected' and if those serial nos belongs to multiple batches
					// for each unique batch new item row is added in the form & cart
					Object.keys(batch_serial_map).forEach((batch) => {
						const item_to_clone = this.frm.doc.items.find((i) => i.name == item.name);
						const new_row = this.frm.add_child("items", { ...item_to_clone });
						// update new serialno and batch
						new_row.batch_no = batch;
						new_row.serial_no = batch_serial_map[batch].join(`\n`);
						new_row.qty = batch_serial_map[batch].length;
						this.frm.doc.items.forEach((row) => {
							if (item.item_code === row.item_code) {
								this.update_cart_html(row);
							}
						});
					});
				},
				remove_item_from_cart: () => this.remove_item_from_cart(),
				get_item_stock_map: () => this.item_stock_map,
				close_item_details: () => {
					this.item_details.toggle_item_details_section(null);
					this.cart.prev_action = null;
					this.cart.toggle_item_highlight();
					// Show the item selector when item details are closed
					this.item_selector.toggle_component(true);
				},
				get_available_stock: (item_code, warehouse) => this.get_available_stock(item_code, warehouse),
			},
		});
	}

	init_payments() {
		// Ensure Payment class exists
		if (!erpnext.SalesInvoiceUI.Payment) {
			console.error("Payment class not found");
			return;
		}

		this.payment = new erpnext.SalesInvoiceUI.Payment({
			wrapper: this.$components_wrapper,
			events: {
				get_frm: () => this.frm || {},

				get_customer_details: () => this.customer_details || {},

				toggle_other_sections: (show) => {
					if (show) {
						this.item_details.$component.is(":visible")
							? this.item_details.$component.css("display", "none")
							: "";
						this.item_selector.toggle_component(false);
					} else {
						this.item_selector.toggle_component(true);
					}
				},

				submit_invoice: () => {
					this.frm.savesubmit().then((r) => {
						this.toggle_components(false);
						this.order_summary.toggle_component(true);
						this.order_summary.load_summary_of(this.frm.doc, true);
						frappe.show_alert({
							indicator: "green",
							message: __("Sales Invoice {0} created successfully", [r.doc.name]),
						});
					});
				},
			},
		});
	}

	init_recent_order_list() {
		console.log("comp wrapper:",this.$components_wrapper);
		console.log("wrapper:",this.wrapper);

		
		if (!erpnext.SalesInvoiceUI.PastOrderList) {
			console.error("PastOrderList class not found");
			return;
		}

		this.recent_order_list = new erpnext.SalesInvoiceUI.PastOrderList({
			wrapper: this.$components_wrapper,
			events: {
				open_invoice_data: (name) => {
					frappe.db.get_doc("Sales Invoice", name).then((doc) => {
						this.order_summary.load_summary_of(doc);
					});
				},
				reset_summary: () => this.order_summary.toggle_summary_placeholder(true),
			},
		});
	}

	init_order_summary() {
		if (!erpnext.SalesInvoiceUI.PastOrderSummary) {
			console.error("PastOrderSummary class not found");
			return;
		}

		this.order_summary = new erpnext.SalesInvoiceUI.PastOrderSummary({
			wrapper: this.$components_wrapper,
			settings: this.settings,
			events: {
				get_frm: () => this.frm,

				process_return: (name) => {
					this.recent_order_list.toggle_component(false);
					frappe.db.get_doc("Sales Invoice", name).then((doc) => {
						frappe.run_serially([
							() => this.make_return_invoice(doc),
							() => this.cart.load_invoice(),
							() => this.item_selector.toggle_component(true),
						]);
					});
				},
				edit_order: (name) => {
					this.recent_order_list.toggle_component(false);
					frappe.run_serially([
						() => this.frm.refresh(name),
						() => this.cart.load_invoice(),
						() => this.item_selector.toggle_component(true),
					]);
				},
				delete_order: (name) => {
					frappe.model.delete_doc(this.frm.doc.doctype, name, () => {
						this.recent_order_list.refresh_list();
					});
				},
				new_order: () => {
					frappe.run_serially([
						() => frappe.dom.freeze(),
						() => this.make_new_invoice(),
						() => this.item_selector.toggle_component(true),
						() => frappe.dom.unfreeze(),
					]);
				},
			},
		});
	}

	remove_button_active_states() {
	this.page.wrapper.find(".pos-filter-btn")
		.removeClass("btn-primary")
		.addClass("btn-default");
	}

	toggle_custom_buttons(show = true) {
	if (show) {
		this.page.wrapper.find('.pos-custom-buttons').show();
	} else {
		this.page.wrapper.find('.pos-custom-buttons').hide();
	}
	}
	
	toggle_recent_order_list(show) {
		this.toggle_components(!show);
		
		if (show) {
			this.recent_order_list.toggle_component(true);
			this.order_summary.toggle_component(true);
		} else {
			this.recent_order_list.toggle_component(false);
			this.order_summary.toggle_component(false);
			// Reset button states when hiding
			this.active_filter_btn = null;
			this.remove_button_active_states();
		}
	}

	toggle_components(show) {
		this.cart.toggle_component(show);
		this.item_selector.toggle_component(show);

		// do not show item details or payment if recent order is toggled off
		!show ? this.item_details.toggle_component(false) || this.payment.toggle_component(false) : "";
	}

	close_recent_orders() {
	this.toggle_recent_order_list(false);
	this.active_filter_btn = null;
	this.remove_button_active_states();
	if (this.recent_order_list) {
		this.recent_order_list.reset_filters();
	}
	}

	make_new_invoice() {
		return frappe.run_serially([
			() => frappe.dom.freeze(),
			() => this.make_sales_invoice_frm(),
			() => this.set_pos_profile_data(),
			() => this.set_pos_profile_status(),
			() => this.cart.load_invoice(),
			() => frappe.dom.unfreeze(),
		]);
	}
	

	make_sales_invoice_frm() {
		const doctype = "Sales Invoice";
		return new Promise((resolve) => {
			if (this.frm) {
				this.frm = this.get_new_frm(this.frm);
				this.frm.doc.items = [];
				this.frm.doc.is_pos = 1;
				resolve();
			} else {
				frappe.model.with_doctype(doctype, () => {
					this.frm = this.get_new_frm();
					this.frm.doc.items = [];
					this.frm.doc.is_pos = 1;
					resolve();
				});
			}
		});
	}

	get_new_frm(_frm) {
		const doctype = "Sales Invoice";
		const page = $("<div>");
		const frm = _frm || new frappe.ui.form.Form(doctype, page, false);
		const name = frappe.model.make_new_doc_and_get_name(doctype, true);
		frm.refresh(name);

		return frm;
	}

	async make_return_invoice(doc) {
		frappe.dom.freeze();
		this.frm = this.get_new_frm(this.frm);
		this.frm.doc.items = [];
		return frappe.call({
			method: "erpnext.accounts.doctype.sales_invoice.sales_invoice.make_sales_return",
			args: {
				source_name: doc.name,
				target_doc: this.frm.doc,
			},
			callback: (r) => {
				frappe.model.sync(r.message);
				frappe.get_doc(r.message.doctype, r.message.name).__run_link_triggers = false;
				this.set_pos_profile_data().then(() => {
					frappe.dom.unfreeze();
				});
			},
		});
	}

	set_pos_profile_data() {
		if (this.company && !this.frm.doc.company) this.frm.doc.company = this.company;
		if (
			(this.pos_profile && !this.frm.doc.pos_profile) |
			(this.frm.doc.is_return && this.pos_profile != this.frm.doc.pos_profile)
		) {
			this.frm.doc.pos_profile = this.pos_profile;
		}

		if (!this.frm.doc.company) return;

		return this.frm.trigger("set_pos_data");
	}

	set_pos_profile_status() {
		this.page.set_indicator(this.pos_profile, "blue");
	}

	async on_cart_update(args) {
		frappe.dom.freeze();
		if (this.frm.doc.set_warehouse != this.settings.warehouse)
			this.frm.doc.set_warehouse = this.settings.warehouse;
		let item_row = undefined;
		try {
			let { field, value, item } = args;
			item_row = this.get_item_from_frm(item);
			const item_row_exists = !$.isEmptyObject(item_row);

			const from_selector = field === "qty" && value === "+1";
			if (from_selector) value = flt(item_row.qty) + flt(value);

			if (item_row_exists) {
				if (field === "qty") value = flt(value);

				if (["qty", "conversion_factor"].includes(field) && value > 0 && !this.allow_negative_stock) {
					const qty_needed =
						field === "qty" ? value * item_row.conversion_factor : item_row.qty * value;
					await this.check_stock_availability(item_row, qty_needed, this.frm.doc.set_warehouse);
				}

				if (this.is_current_item_being_edited(item_row) || from_selector) {
					await frappe.model.set_value(item_row.doctype, item_row.name, field, value);
					if (item.serial_no && from_selector) {
						await frappe.model.set_value(
							item_row.doctype,
							item_row.name,
							"serial_no",
							item_row.serial_no + `\n${item.serial_no}`
						);
					}
					this.update_cart_html(item_row);
				}
			} else {
				if (!this.frm.doc.customer) return this.raise_customer_selection_alert();

				const { item_code, batch_no, serial_no, rate, uom, stock_uom } = item;

				if (!item_code) return;

				if (rate == undefined || rate == 0) {
					frappe.show_alert({
						message: __("Price is not set for the item."),
						indicator: "orange",
					});
					frappe.utils.play_sound("error");
					return;
				}
				const new_item = { item_code, batch_no, rate, uom, [field]: value, stock_uom };

				if (serial_no) {
					await this.check_serial_no_availablilty(item_code, this.frm.doc.set_warehouse, serial_no);
					new_item["serial_no"] = serial_no;
				}

				new_item["use_serial_batch_fields"] = 1;
				if (field === "serial_no") new_item["qty"] = value.split(`\n`).length || 0;

				item_row = this.frm.add_child("items", new_item);

				if (field === "qty" && value !== 0 && !this.allow_negative_stock) {
					const qty_needed = value * item_row.conversion_factor;
					await this.check_stock_availability(item_row, qty_needed, this.frm.doc.set_warehouse);
				}

				await this.trigger_new_item_events(item_row);

				this.update_cart_html(item_row);

				if (this.item_details.$component.is(":visible")) this.edit_item_details_of(item_row);

				if (
					this.check_serial_batch_selection_needed(item_row) &&
					!this.item_details.$component.is(":visible")
				)
					this.edit_item_details_of(item_row);
			}
		} catch (error) {
			console.log(error);
		} finally {
			frappe.dom.unfreeze();
			return item_row; // eslint-disable-line no-unsafe-finally
		}
	}

	raise_customer_selection_alert() {
		frappe.dom.unfreeze();
		frappe.show_alert({
			message: __("You must select a customer before adding an item."),
			indicator: "orange",
		});
		frappe.utils.play_sound("error");
	}

	get_item_from_frm({ name, item_code, batch_no, uom, rate }) {
		let item_row = null;
		if (name) {
			item_row = this.frm.doc.items.find((i) => i.name == name);
		} else {
			// if item is clicked twice from item selector
			// then "item_code, batch_no, uom, rate" will help in getting the exact item
			// to increase the qty by one
			const has_batch_no = batch_no !== "null" && batch_no !== null;
			item_row = this.frm.doc.items.find(
				(i) =>
					i.item_code === item_code &&
					(!has_batch_no || (has_batch_no && i.batch_no === batch_no)) &&
					i.uom === uom &&
					i.price_list_rate === flt(rate)
			);
		}

		return item_row || {};
	}

	edit_item_details_of(item_row) {
		this.item_details.toggle_item_details_section(item_row);
	}

	is_current_item_being_edited(item_row) {
		return item_row.name == this.item_details.current_item.name;
	}

	update_cart_html(item_row, remove_item) {
		this.cart.update_item_html(item_row, remove_item);
		this.cart.update_totals_section(this.frm);
	}

	check_serial_batch_selection_needed(item_row) {
		// right now item details is shown for every type of item.
		// if item details is not shown for every item then this fn will be needed
		const serialized = item_row.has_serial_no;
		const batched = item_row.has_batch_no;
		const no_serial_selected = !item_row.serial_no;
		const no_batch_selected = !item_row.batch_no;

		if (
			(serialized && no_serial_selected) ||
			(batched && no_batch_selected) ||
			(serialized && batched && (no_batch_selected || no_serial_selected))
		) {
			return true;
		}
		return false;
	}

	async trigger_new_item_events(item_row) {
		await this.frm.script_manager.trigger("item_code", item_row.doctype, item_row.name);
		await this.frm.script_manager.trigger("qty", item_row.doctype, item_row.name);
	}

	async check_stock_availability(item_row, qty_needed, warehouse) {
		const resp = (await this.get_available_stock(item_row.item_code, warehouse)).message;
		const available_qty = resp[0];
		const is_stock_item = resp[1];

		frappe.dom.unfreeze();
		const bold_uom = item_row.stock_uom.bold();
		const bold_item_code = item_row.item_code.bold();
		const bold_warehouse = warehouse.bold();
		const bold_available_qty = available_qty.toString().bold();
		if (!(available_qty > 0)) {
			if (is_stock_item) {
				frappe.model.clear_doc(item_row.doctype, item_row.name);
				frappe.throw({
					title: __("Not Available"),
					message: __("Item Code: {0} is not available under warehouse {1}.", [
						bold_item_code,
						bold_warehouse,
					]),
				});
			} else {
				return;
			}
		} else if (is_stock_item && available_qty < qty_needed) {
			frappe.throw({
				message: __(
					"Stock quantity not enough for Item Code: {0} under warehouse {1}. Available quantity {2} {3}.",
					[bold_item_code, bold_warehouse, bold_available_qty, bold_uom]
				),
				indicator: "orange",
			});
			frappe.utils.play_sound("error");
		}
		frappe.dom.freeze();
	}

	async check_serial_no_availablilty(item_code, warehouse, serial_no) {
		const method = "erpnext.stock.doctype.serial_no.serial_no.get_pos_reserved_serial_nos";
		const args = { filters: { item_code, warehouse } };
		const res = await frappe.call({ method, args });

		if (res.message.includes(serial_no)) {
			frappe.throw({
				title: __("Not Available"),
				message: __("Serial No: {0} has already been transacted into another Sales Invoice.", [
					serial_no.bold(),
				]),
			});
		}
	}

	get_available_stock(item_code, warehouse) {
		const me = this;
		return frappe.call({
			method: "erpnext.accounts.doctype.pos_invoice.pos_invoice.get_stock_availability",
			args: {
				item_code: item_code,
				warehouse: warehouse,
			},
			callback(res) {
				if (!me.item_stock_map[item_code]) me.item_stock_map[item_code] = {};
				me.item_stock_map[item_code][warehouse] = res.message;
			},
		});
	}

	update_item_field(value, field_or_action) {
		if (field_or_action === "checkout") {
			this.item_details.toggle_item_details_section(null);
		} else if (field_or_action === "remove") {
			this.remove_item_from_cart();
		} else {
			const field_control = this.item_details[`${field_or_action}_control`];
			if (!field_control) return;
			field_control.set_focus();
			value != "" && field_control.set_value(value);
		}
	}

	remove_item_from_cart() {
		frappe.dom.freeze();
		const { doctype, name, current_item } = this.item_details;

		return frappe.model
			.set_value(doctype, name, "qty", 0)
			.then(() => {
				frappe.model.clear_doc(doctype, name);
				this.update_cart_html(current_item, true);
				this.item_details.toggle_item_details_section(null);
				frappe.dom.unfreeze();
			})
			.catch((e) => console.log(e));
	}

	async save_and_checkout() {
		if (this.frm.is_dirty()) {
			let save_error = false;
			await this.frm.save(null, null, null, () => (save_error = true));
			// only move to payment section if save is successful
			!save_error && this.payment.checkout();
			// show checkout button on error
			save_error &&
				setTimeout(() => {
					this.cart.toggle_checkout_btn(true);
				}, 300); // wait for save to finish
		} else {
			this.payment.checkout();
		}
	}
};
