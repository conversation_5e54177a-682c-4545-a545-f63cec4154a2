.item-variant-title{
    font-size: 30px;
    font-weight: bold;
    color: #040969;
    text-shadow: 2px 2px 4px #93b1c9;
}
.item-variant-card {
    width: 100%;
    max-width: 450px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.item-variant-card img {
    object-fit: cover;
    height: 300px;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.6));
    transition: transform 0.3s ease;
}

.item-variant-card img:hover {
    transform: scale(1.05);
}

.item-variant-card img:not(:hover) {
    transform: scale(1);
}

.item-variant-card hr {
    margin-top: 8px;
    border: none;
    height: 1px;
    background-color: #ccc;
}

.item-variant-card .card-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.item-variant-card .btn-primary {
    max-width: 9rem;
    border: 2px solid white;
    background-color: #3a71bd;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
}

.item-variant-card .btn-primary:hover {
    background-color: #5b84bd;
}
