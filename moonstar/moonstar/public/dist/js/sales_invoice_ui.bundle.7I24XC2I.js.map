{"version": 3, "sources": ["../../../../../apps/erpnext/node_modules/onscan.js/onscan.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_item_cart.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_item_selector.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_item_details.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_number_pad.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_payment.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_past_order_list.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_past_order_summary.js", "../../../../../apps/moonstar/moonstar/moonstar/page/sales_invoice_ui/sales_invoice_controller.js"], "sourcesContent": ["/*\n * onScan.js - scan-events for hardware barcodes scanners in javascript\n */\n;(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory()) :\n    global.onScan = factory()\n}(this, (function () {\n\tvar onScan = {\t\n\t\t\n\t\t/**\n\t\t * \n\t\t * @param DomElement oDomElement\n\t\t * @param Object oOptions\n\t\t * @return self\n\t\t */\n\t\tattachTo: function(oDomElement, oOptions) {\n\t\n\t\t\tif(oDomElement.scannerDetectionData !== undefined){\n\t\t\t\tthrow new Error(\"onScan.js is already initialized for DOM element \" + oDomElement);\n\t\t\t}\n\t\n\t\t\tvar oDefaults = {\n\t\t\t\tonScan: function(sScanned, iQty){}, // Callback after detection of a successfull scanning:  function(){sScancode, iCount)}()\n\t\t\t\tonScanError: function(oDebug){}, // Callback after detection of a unsuccessfull scanning (scanned string in parameter)\n\t\t\t\tonKeyProcess: function(sChar, oEvent){}, // Callback after receiving and processing a char (scanned char in parameter)\n\t\t\t\tonKeyDetect: function(iKeyCode, oEvent){}, // Callback after detecting a keyDown (key char in parameter) - in contrast to onKeyProcess, this fires for non-character keys like tab, arrows, etc. too!\n\t\t\t\tonPaste: function(sPasted, oEvent){}, // Callback after receiving a value on paste, no matter if it is a valid code or not\n\t\t\t\tkeyCodeMapper: function(oEvent) {return onScan.decodeKeyEvent(oEvent)}, // Custom function to decode a keydown event into a character. Must return decoded character or NULL if the given event should not be processed.\n\t\t\t\tonScanButtonLongPress: function(){}, // Callback after detection of a successfull scan while the scan button was pressed and held down\n\t\t\t\tscanButtonKeyCode:false, // Key code of the scanner hardware button (if the scanner button a acts as a key itself) \n\t\t\t\tscanButtonLongPressTime:500, // How long (ms) the hardware button should be pressed, until a callback gets executed\n\t\t\t\ttimeBeforeScanTest:100, // Wait duration (ms) after keypress event to check if scanning is finished\n\t\t\t\tavgTimeByChar:30, // Average time (ms) between 2 chars. Used to do difference between keyboard typing and scanning\n\t\t\t\tminLength:6, // Minimum length for a scanning\n\t\t\t\tsuffixKeyCodes:[9,13], // Chars to remove and means end of scanning\n\t\t\t\tprefixKeyCodes:[], // Chars to remove and means start of scanning\n\t\t\t\tignoreIfFocusOn:false, // do not handle scans if the currently focused element matches this selector or object\n\t\t\t\tstopPropagation:false, // Stop immediate propagation on keypress event\n\t\t\t\tpreventDefault:false, // Prevent default action on keypress event\n\t\t\t\tcaptureEvents:false, // Get the events before any listeners deeper in the DOM\n\t\t\t\treactToKeydown:true, // look for scan input in keyboard events\n\t\t\t\treactToPaste:false, // look for scan input in paste events\n\t\t\t\tsingleScanQty: 1, // Quantity of Items put out to onScan in a single scan\n\t\t\t}\n\t\t\t\t\t\t\t\t\t\n\t\t\toOptions = this._mergeOptions(oDefaults, oOptions);\n\t\n\t\t\t// initializing options and variables on DomElement\n\t\t\toDomElement.scannerDetectionData = {\n\t\t\t\t\toptions: oOptions,\n\t\t\t\t\tvars:{\n\t\t\t\t\t\tfirstCharTime: 0,\n\t\t\t\t\t\tlastCharTime: 0,\n\t\t\t\t\t\taccumulatedString: '',\n\t\t\t\t\t\ttestTimer: false,\n\t\t\t\t\t\tlongPressTimeStart: 0,\n\t\t\t\t\t\tlongPressed: false\n\t\t\t\t\t}\n\t\t\t\t\n\t\t\t};\n\t\t\t\n\t\t\t// initializing handlers (based on settings)\n\t\t\tif (oOptions.reactToPaste === true){\n\t\t\t\toDomElement.addEventListener(\"paste\", this._handlePaste, oOptions.captureEvents);\n\t\t\t}\n\t\t\tif (oOptions.scanButtonKeyCode !== false){\n\t\t\t\toDomElement.addEventListener(\"keyup\", this._handleKeyUp, oOptions.captureEvents);\n\t\t\t}\n\t\t\tif (oOptions.reactToKeydown === true || oOptions.scanButtonKeyCode !== false){\t\n\t\t\t\toDomElement.addEventListener(\"keydown\", this._handleKeyDown, oOptions.captureEvents);\n\t\t\t}\n\t\t\treturn this;\n\t\t},\n\t\t\n\t\t/**\n\t\t * \n\t\t * @param DomElement oDomElement\n\t\t * @return void\n\t\t */\n\t\tdetachFrom: function(oDomElement) {\n\t\t\t// detaching all used events\n\t\t\tif (oDomElement.scannerDetectionData.options.reactToPaste){\n\t\t\t\toDomElement.removeEventListener(\"paste\", this._handlePaste);\n\t\t\t}\n\t\t\tif (oDomElement.scannerDetectionData.options.scanButtonKeyCode !== false){\n\t\t\t\toDomElement.removeEventListener(\"keyup\", this._handleKeyUp);\n\t\t\t}\n\t\t\toDomElement.removeEventListener(\"keydown\", this._handleKeyDown);\n\t\t\t\n\t\t\t// clearing data off DomElement\n\t\t\toDomElement.scannerDetectionData = undefined; \n\t\t\treturn;\n\t\t},\n\t\t\n\t\t/**\n\t\t * \n\t\t * @param DomElement oDomElement\n\t\t * @return Object\n\t\t */\n\t\tgetOptions: function(oDomElement){\n\t\t\treturn oDomElement.scannerDetectionData.options;\t\t\t\n\t\t},\n\t\n\t\t/**\n\t\t * \n\t\t * @param DomElement oDomElement\n\t\t * @param Object oOptions\n\t\t * @return self\n\t\t */\n\t\tsetOptions: function(oDomElement, oOptions){\n\t\t\t// check if some handlers need to be changed based on possible option changes\n\t\t\tswitch (oDomElement.scannerDetectionData.options.reactToPaste){\n\t\t\t\tcase true: \n\t\t\t\t\tif (oOptions.reactToPaste === false){\n\t\t\t\t\t\toDomElement.removeEventListener(\"paste\", this._handlePaste);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase false:\n\t\t\t\t\tif (oOptions.reactToPaste === true){\n\t\t\t\t\t\toDomElement.addEventListener(\"paste\", this._handlePaste);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\t\n\t\t\tswitch (oDomElement.scannerDetectionData.options.scanButtonKeyCode){\n\t\t\t\tcase false:\n\t\t\t\t\tif (oOptions.scanButtonKeyCode !== false){\n\t\t\t\t\t\toDomElement.addEventListener(\"keyup\", this._handleKeyUp);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tdefault: \n\t\t\t\t\tif (oOptions.scanButtonKeyCode === false){\n\t\t\t\t\t\toDomElement.removeEventListener(\"keyup\", this._handleKeyUp);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\t\n\t\t\t// merge old and new options\n\t\t\toDomElement.scannerDetectionData.options = this._mergeOptions(oDomElement.scannerDetectionData.options, oOptions);\n\t\t\n\t\t\t// reinitiallize\n\t\t\tthis._reinitialize(oDomElement);\n\t\t\treturn this;\n\t\t},\n\t\t\n\t\t/**\n\t\t * Transforms key codes into characters.\n\t\t * \n\t\t * By default, only the follwing key codes are taken into account\n\t\t * - 48-90 (letters and regular numbers)\n\t\t * - 96-105 (numeric keypad numbers)\n\t\t * - 106-111 (numeric keypad operations)\n\t\t * \n\t\t * All other keys will yield empty strings!\n\t\t * \n\t\t * The above keycodes will be decoded using the KeyboardEvent.key property on modern\n\t\t * browsers. On older browsers the method will fall back to String.fromCharCode()\n\t\t * putting the result to upper/lower case depending on KeyboardEvent.shiftKey if\n\t\t * it is set.\n\t\t * \n\t\t * @param KeyboardEvent oEvent\n\t\t * @return string\n\t\t */\n\t\tdecodeKeyEvent : function (oEvent) {\n\t\t\tvar iCode = this._getNormalizedKeyNum(oEvent);\n\t\t\tswitch (true) {\n\t\t\t\tcase iCode >= 48 && iCode <= 90: // numbers and letters\n\t\t\t\tcase iCode >= 106 && iCode <= 111: // operations on numeric keypad (+, -, etc.)\n\t\t\t\t\tif (oEvent.key !== undefined && oEvent.key !== '') {\n\t\t\t\t\t\treturn oEvent.key;\n\t\t\t\t\t}\n\t\t\t\t\n\t\t\t\t\tvar sDecoded = String.fromCharCode(iCode);\n\t\t\t\t\tswitch (oEvent.shiftKey) {\n\t\t\t\t\t\tcase false: sDecoded = sDecoded.toLowerCase(); break;\n\t\t\t\t\t\tcase true: sDecoded = sDecoded.toUpperCase(); break;\n\t\t\t\t\t}\n\t\t\t\t\treturn sDecoded;\n\t\t\t\tcase iCode >= 96 && iCode <= 105: // numbers on numeric keypad\n\t\t\t\t\treturn 0+(iCode-96);\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\t\t\n\t\t/**\n\t\t * Simulates a scan of the provided code.\n\t     *\n\t\t * The scan code can be defined as\n\t\t * - a string - in this case no keyCode decoding is done and the code is merely validated\n\t\t * against constraints like minLenght, etc.\n\t\t * - an array of keyCodes (e.g. `[70,71,80]`) - will produce `keydown` events with corresponding\n\t\t * `keyCode` properties. NOTE: these events will have empty `key` properties, so decoding may\n\t\t * yield different results than with native events.\n\t\t * - an array of objects (e.g. `[{keyCode: 70, key: \"F\", shiftKey: true}, {keyCode: 71, key: \"g\"}]`) -\n\t\t * this way almost any event can be simulated, but it's a lot of work to do.\n\t\t *\n\t\t * @param DomElement oDomElement\n\t\t * @param string|array mStringOrArray\n\t\t * @return self\n\t\t */\n\t\tsimulate: function(oDomElement, mStringOrArray){\n\t\t\tthis._reinitialize(oDomElement);\n\t\t\tif (Array.isArray(mStringOrArray)){\n\t\t\t\tmStringOrArray.forEach(function(mKey){\n\t\t\t\t\tvar oEventProps = {};\n\t\t\t\t\tif( (typeof mKey === \"object\" || typeof mKey === 'function') && (mKey !== null) ) {\n\t\t\t\t\t\toEventProps = mKey;\n\t\t\t\t\t} else {\n\t\t\t\t\t\toEventProps.keyCode = parseInt(mKey);\n\t\t\t\t\t}\n\t\t\t\t\tvar oEvent = new KeyboardEvent('keydown', oEventProps);\n\t\t\t\t\tdocument.dispatchEvent(oEvent);\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tthis._validateScanCode(oDomElement, mStringOrArray);\n\t\t\t}\n\t\t\treturn this;\n\t\t},\n\t\t\n\t\t/**\n\t\t * @private\n\t\t * @param DomElement oDomElement\n\t\t * @return void\n\t\t */\n\t\t_reinitialize: function(oDomElement){\n\t\t\tvar oVars = oDomElement.scannerDetectionData.vars;\n\t\t\toVars.firstCharTime = 0;\n\t\t\toVars.lastCharTime = 0;\n\t\t\toVars.accumulatedString = '';\n\t\t\treturn;\n\t\t},\n\t\t\n\t\t/**\n\t\t * @private\n\t\t * @param DomElement oDomElement\n\t     * @return boolean\n\t\t */\n\t\t_isFocusOnIgnoredElement: function(oDomElement){\n\t\t\t\n\t\t\tvar ignoreSelectors = oDomElement.scannerDetectionData.options.ignoreIfFocusOn;\n\t\n\t        if(!ignoreSelectors){\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\n\t\t\tvar oFocused = document.activeElement;\n\t\t\t\n\t\t\t// checks if ignored element is an array, and if so it checks if one of the elements of it is an active one\n\t\t\tif (Array.isArray(ignoreSelectors)){\n\t\t\t\tfor(var i=0; i<ignoreSelectors.length; i++){\n\t\t\t\t\tif(oFocused.matches(ignoreSelectors[i]) === true){\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t// if the option consists of an single element, it only checks this one\n\t\t\t} else if (oFocused.matches(ignoreSelectors)){\n\t\t\t\treturn true;\t\t\t\t\t\n\t\t\t}\n\t\t\t\n\t\t\t// if the active element is not listed in the ignoreIfFocusOn option, return false\n\t\t    return false;\n\t    },\n\t\t\n\t    /**\n\t     * Validates the scan code accumulated by the given DOM element and fires the respective events.\n\t     * \n\t     * @private\n\t     * @param DomElement oDomElement\n\t     * @return boolean\n\t     */\n\t\t_validateScanCode: function(oDomElement, sScanCode){\n\t\t\tvar oScannerData = oDomElement.scannerDetectionData;\t\t\t\n\t\t\tvar oOptions = oScannerData.options;\n\t\t\tvar iSingleScanQty = oScannerData.options.singleScanQty;\n\t\t\tvar iFirstCharTime = oScannerData.vars.firstCharTime;\n\t\t\tvar iLastCharTime = oScannerData.vars.lastCharTime;\n\t\t\tvar oScanError = {};\n\t        var oEvent;\n\t        \n\t\t\tswitch(true){\n\t\t\t\t\n\t\t\t\t// detect codes that are too short\n\t\t\t\tcase (sScanCode.length < oOptions.minLength):\n\t\t\t\t\toScanError = {\n\t\t\t\t\t\tmessage: \"Receieved code is shorter then minimal length\"\n\t\t\t\t\t};\n\t\t\t\t\tbreak;\n\t\t\t\t\t\n\t\t\t\t// detect codes that were entered too slow\t\n\t\t\t\tcase ((iLastCharTime - iFirstCharTime) > (sScanCode.length * oOptions.avgTimeByChar)):\n\t\t\t\t\toScanError = {\n\t\t\t\t\t\tmessage: \"Receieved code was not entered in time\"\n\t\t\t\t\t};\t\t\t\t\n\t\t\t\t\tbreak;\n\t\t\t\t\t\n\t\t\t\t// if a code was not filtered out earlier it is valid\t\n\t\t\t\tdefault:\n\t\t\t\t\toOptions.onScan.call(oDomElement, sScanCode, iSingleScanQty);\n\t\t\t\t\toEvent = new CustomEvent(\n\t\t\t\t\t\t'scan',\n\t\t\t\t\t\t{\t\n\t\t\t\t\t\t\tdetail: { \n\t\t\t\t\t\t\t\tscanCode: sScanCode,\n\t\t\t\t\t\t\t\tqty: iSingleScanQty\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t);\n\t\t\t\t\toDomElement.dispatchEvent(oEvent);\n\t\t\t\t\tonScan._reinitialize(oDomElement);\n\t\t\t\t\treturn true;\n\t\t\t}\n\t\t\t\n\t\t\t// If an error occurred (otherwise the method would return earlier) create an object for errordetection\n\t\t\toScanError.scanCode = sScanCode;\n\t\t\toScanError.scanDuration = iLastCharTime - iFirstCharTime;\n\t\t\toScanError.avgTimeByChar = oOptions.avgTimeByChar;\n\t\t\toScanError.minLength = oOptions.minLength;\n\t\t\t\n\t\t\toOptions.onScanError.call(oDomElement, oScanError);\n\t\t\t\n\t\t\toEvent = new CustomEvent(\n\t\t\t\t'scanError', \n\t\t\t\t{detail: oScanError}\n\t\t\t);\n\t\t\toDomElement.dispatchEvent(oEvent);\n\t\t\t\n\t\t\tonScan._reinitialize(oDomElement);\n\t\t\treturn false;\n\t    },\n\t\n\t    /**\n\t     * @private\n\t     * @param Object oDefaults\n\t     * @param Object oOptions\n\t     * @return Object\n\t     */\n\t\t_mergeOptions: function(oDefaults, oOptions){\n\t\t\tvar oExtended = {};\n\t\t\tvar prop;\n\t\t\tfor (prop in oDefaults){\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(oDefaults, prop)){\n\t\t\t\t\toExtended[prop] = oDefaults[prop];\n\t\t\t\t}\n\t\t\t}\t\t\t\n\t\t\tfor (prop in oOptions){\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(oOptions, prop)){\n\t\t\t\t\toExtended[prop] = oOptions[prop];\n\t\t\t\t}\n\t\t\t}\t\t\t\n\t\t\treturn oExtended;\n\t\t},\n\t\n\t\t/**\n\t\t * @private\n\t\t * @param KeyboardEvent e\n\t\t * @return int\n\t\t * @see https://www.w3schools.com/jsref/event_key_keycode.asp\n\t\t */\n\t\t_getNormalizedKeyNum: function(e){\n\t\t\treturn e.which || e.keyCode;\n\t\t},\n\t\n\t\n\t\t/**\n\t\t * @private\n\t\t * @param KeyboardEvent e\n\t\t * @return void\n\t\t */\n\t\t_handleKeyDown: function(e){\n\t\t\tvar iKeyCode = onScan._getNormalizedKeyNum(e);\n\t\t\tvar oOptions = this.scannerDetectionData.options;\n\t\t\tvar oVars = this.scannerDetectionData.vars;\n\t\t\tvar bScanFinished = false;\n\t\t\t\n\t\t\tif (oOptions.onKeyDetect.call(this, iKeyCode, e) === false) {\n\t\t\t\treturn;\n\t\t\t}\t\t\n\t\t\t\n\t\t\tif (onScan._isFocusOnIgnoredElement(this)){\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\t\t\t\n\t        // If it's just the button of the scanner, ignore it and wait for the real input\n\t\t    if(oOptions.scanButtonKeyCode !== false && iKeyCode==oOptions.scanButtonKeyCode) {\n\t\t\t\t\n\t\t\t\t// if the button was first pressed, start a timeout for the callback, which gets interrupted if the scanbutton gets released\n\t\t\t\tif (!oVars.longPressed){\n\t\t\t\t\toVars.longPressTimer = setTimeout( oOptions.onScanButtonLongPress, oOptions.scanButtonLongPressTime, this);\n\t\t\t\t\toVars.longPressed = true;\n\t\t\t\t}\n\t\n\t\t\t\treturn;\n\t        }\n\t\t\t\n\t\t\tswitch(true){\n\t\t\t\t// If it's not the first character and we encounter a terminating character, trigger scan process\n\t\t\t\tcase (oVars.firstCharTime && oOptions.suffixKeyCodes.indexOf(iKeyCode)!==-1):\n\t\t\t\t\te.preventDefault();\n\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\tbScanFinished=true;\n\t\t\t\t\tbreak;\n\t\t\t\t\t\n\t\t\t\t// If it's the first character and we encountered one of the starting characters, don't process the scan\t\n\t\t\t\tcase (!oVars.firstCharTime && oOptions.prefixKeyCodes.indexOf(iKeyCode)!==-1):\n\t\t\t\t\te.preventDefault();\n\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\tbScanFinished=false;\n\t\t\t\t\tbreak;\n\t\t\t\t\t\n\t\t\t\t// Otherwise, just add the character to the scan string we're building\t\n\t\t\t\tdefault:\n\t\t\t\t\tvar character = oOptions.keyCodeMapper.call(this, e);\n\t\t\t\t\tif (character === null){\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\toVars.accumulatedString += character;\n\t\t\t\t\t\n\t\t\t\t\tif (oOptions.preventDefault) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t}\n\t\t\t\t\tif (oOptions.stopPropagation) {\n\t\t\t\t\t\te.stopImmediatePropagation();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tbScanFinished=false;\n\t\t\t\t\tbreak;\n\t\t\t}\n\t        \n\t\t\tif(!oVars.firstCharTime){\n\t\t\t\toVars.firstCharTime=Date.now();\n\t\t\t}\n\t\t\t\n\t\t\toVars.lastCharTime=Date.now();\n\t\n\t\t\tif(oVars.testTimer){ \n\t\t\t\tclearTimeout(oVars.testTimer);\n\t\t\t}\n\t\t\t\n\t\t\tif(bScanFinished){\n\t\t\t\tonScan._validateScanCode(this, oVars.accumulatedString);\n\t\t\t\toVars.testTimer=false;\n\t\t\t} else {\n\t\t\t\toVars.testTimer=setTimeout(onScan._validateScanCode, oOptions.timeBeforeScanTest, this, oVars.accumulatedString);\n\t\t\t}\n\t\n\t\t\toOptions.onKeyProcess.call(this, character, e);\n\t\t\treturn;\n\t\t},\n\t\t\n\t\t/**\n\t\t * @private\n\t\t * @param Event e\n\t\t * @return void\n\t\t */\n\t\t_handlePaste: function(e){\n\t\n\t\t\tvar oOptions = this.scannerDetectionData.options;\n\t\t\tvar oVars = this.scannerDetectionData.vars;\n\t\t\tvar sPasteString = (event.clipboardData || window.clipboardData).getData('text');\n\t\t\t\n\t\t\t// if the focus is on an ignored element, abort\n\t\t\tif (onScan._isFocusOnIgnoredElement(this)){\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\te.preventDefault();\n\n\t\t\tif (oOptions.stopPropagation) {\n\t\t\t\te.stopImmediatePropagation();\n\t\t\t}\n\t\t\t\t\t\t\n\t\t\toOptions.onPaste.call(this, sPasteString, event);\n\t\t\t\n\t\t\toVars.firstCharTime = 0;\n\t\t\toVars.lastCharTime = 0;\n\t\t\t\n\t\t\t// validate the string\n\t\t\tonScan._validateScanCode(this, sPasteString);\n\t\t\treturn;\n\t\t},\n\t\t\n\t\t/**\n\t\t * @private\n\t\t * @param KeyboardEvent e\n\t\t * @return void\n\t\t */\n\t\t_handleKeyUp: function(e){\n\t\t\t// if the focus is on an ignored element, abort\n\t\t\tif (onScan._isFocusOnIgnoredElement(this)){\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tvar iKeyCode = onScan._getNormalizedKeyNum(e);\n\t\t\t\n\t\t\t// if hardware key is not being pressed anymore stop the timeout and reset\n\t\t\tif (iKeyCode == this.scannerDetectionData.options.scanButtonKeyCode){\n\t\t\t\tclearTimeout(this.scannerDetectionData.vars.longPressTimer);\n\t\t\t\tthis.scannerDetectionData.vars.longPressed = false;\n\t\t\t}\n\t\t\treturn;\n\t\t},\n\t\t\n\t\t/**\n\t\t * Returns TRUE the scanner is currently in the middle of a scan sequence.\n\t\t * \n\t\t * @param DomElement\n\t\t * @return boolean\n\t\t */\n\t\tisScanInProgressFor: function(oDomElement) {\n\t\t\treturn oDomElement.scannerDetectionData.vars.firstCharTime > 0;\n\t\t},\n\t\t\n\t\t/**\n\t\t * Returns TRUE if onScan is attached to the given DOM element and FALSE otherwise.\n\t\t * \n\t\t * @param DomElement\n\t\t * @return boolean\n\t\t */\n\t\tisAttachedTo: function(oDomElement) {\n\t\t\treturn (oDomElement.scannerDetectionData !== undefined);\n\t\t}\n\t};\n\t\n\treturn onScan;\n})));", "erpnext.SalesInvoiceUI.ItemCart = class {\n\tconstructor({ wrapper, events, settings }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\t\tthis.customer_info = undefined;\n\t\tthis.hide_images = settings.hide_images;\n\t\tthis.allowed_customer_groups = settings.customer_groups;\n\t\tthis.allow_rate_change = settings.allow_rate_change;\n\t\tthis.allow_discount_change = settings.allow_discount_change;\n\t\tthis.init_component();\n\t}\n\n\tinit_component() {\n\t\tthis.prepare_dom();\n\t\tthis.init_child_components();\n\t\tthis.bind_events();\n\t\tthis.attach_shortcuts();\n\t}\n\n\tprepare_dom() {\n\t\tthis.wrapper.append(`<section class=\"customer-cart-container\"></section>`);\n\n\t\tthis.$component = this.wrapper.find(\".customer-cart-container\");\n\t}\n\n\tinit_child_components() {\n\t\tthis.init_customer_selector();\n\t\tthis.init_cart_components();\n\t}\n\n\tinit_customer_selector() {\n\t\tthis.$component.append(`<div class=\"customer-section\"></div>`);\n\t\tthis.$customer_section = this.$component.find(\".customer-section\");\n\t\tthis.make_customer_selector();\n\t}\n\n\treset_customer_selector() {\n\t\tconst frm = this.events.get_frm();\n\t\tfrm.set_value(\"customer\", \"\");\n\t\tthis.make_customer_selector();\n\t\tthis.customer_field.set_focus();\n\t}\n\n\tinit_cart_components() {\n\t\tthis.$component.append(\n\t\t\t`<div class=\"cart-container\">\n\t\t\t\t<div class=\"abs-cart-container\">\n\t\t\t\t\t<div class=\"cart-label\">${__(\"Item Cart\")}</div>\n\t\t\t\t\t<div class=\"cart-header\">\n\t\t\t\t\t\t<div class=\"name-header\">${__(\"Item\")}</div>\n\t\t\t\t\t\t<div class=\"qty-header\">${__(\"Quantity\")}</div>\n\t\t\t\t\t\t<div class=\"rate-amount-header\">${__(\"Amount\")}</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"cart-items-section\"></div>\n\t\t\t\t\t<div class=\"cart-totals-section\"></div>\n\t\t\t\t\t<div class=\"numpad-section\"></div>\n\t\t\t\t</div>\n\t\t\t</div>`\n\t\t);\n\t\tthis.$cart_container = this.$component.find(\".cart-container\");\n\n\t\tthis.make_cart_totals_section();\n\t\tthis.make_cart_items_section();\n\t\tthis.make_cart_numpad();\n\t}\n\n\tmake_cart_items_section() {\n\t\tthis.$cart_header = this.$component.find(\".cart-header\");\n\t\tthis.$cart_items_wrapper = this.$component.find(\".cart-items-section\");\n\n\t\tthis.make_no_items_placeholder();\n\t}\n\n\tmake_no_items_placeholder() {\n\t\tthis.$cart_header.css(\"display\", \"none\");\n\t\tthis.$cart_items_wrapper.html(`<div class=\"no-item-wrapper\">${__(\"No items in cart\")}</div>`);\n\t}\n\n\tget_discount_icon() {\n\t\treturn `<svg class=\"discount-icon\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t\t\t<path d=\"M19 15.6213C19 15.2235 19.158 14.842 19.4393 14.5607L20.9393 13.0607C21.5251 12.4749 21.5251 11.5251 20.9393 10.9393L19.4393 9.43934C19.158 9.15804 19 8.7765 19 8.37868V6.5C19 5.67157 18.3284 5 17.5 5H15.6213C15.2235 5 14.842 4.84196 14.5607 4.56066L13.0607 3.06066C12.4749 2.47487 11.5251 2.47487 10.9393 3.06066L9.43934 4.56066C9.15804 4.84196 8.7765 5 8.37868 5H6.5C5.67157 5 5 5.67157 5 6.5V8.37868C5 8.7765 4.84196 9.15804 4.56066 9.43934L3.06066 10.9393C2.47487 11.5251 2.47487 12.4749 3.06066 13.0607L4.56066 14.5607C4.84196 14.842 5 15.2235 5 15.6213V17.5C5 18.3284 5.67157 19 6.5 19H8.37868C8.7765 19 9.15804 19.158 9.43934 19.4393L10.9393 20.9393C11.5251 21.5251 12.4749 21.5251 13.0607 20.9393L14.5607 19.4393C14.842 19.158 15.2235 19 15.6213 19H17.5C18.3284 19 19 18.3284 19 17.5V15.6213Z\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t<path d=\"M15 9L9 15\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t<path d=\"M10.5 9.5C10.5 10.0523 10.0523 10.5 9.5 10.5C8.94772 10.5 8.5 10.0523 8.5 9.5C8.5 8.94772 8.94772 8.5 9.5 8.5C10.0523 8.5 10.5 8.94772 10.5 9.5Z\" fill=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t\t<path d=\"M15.5 14.5C15.5 15.0523 15.0523 15.5 14.5 15.5C13.9477 15.5 13.5 15.0523 13.5 14.5C13.5 13.9477 13.9477 13.5 14.5 13.5C15.0523 13.5 15.5 13.9477 15.5 14.5Z\" fill=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n\t\t\t</svg>`;\n\t}\n\n\tmake_cart_totals_section() {\n\t\tthis.$totals_section = this.$component.find(\".cart-totals-section\");\n\n\t\tthis.$totals_section.append(\n\t\t\t`<div class=\"add-discount-wrapper\">\n\t\t\t\t${this.get_discount_icon()} ${__(\"Add Discount\")}\n\t\t\t</div>\n\t\t\t<div class=\"item-qty-total-container\">\n\t\t\t\t<div class=\"item-qty-total-label\">${__(\"Total Items\")}</div>\n\t\t\t\t<div class=\"item-qty-total-value\">0.00</div>\n\t\t\t</div>\n\t\t\t<div class=\"net-total-container\">\n\t\t\t\t<div class=\"net-total-label\">${__(\"Net Total\")}</div>\n\t\t\t\t<div class=\"net-total-value\">0.00</div>\n\t\t\t</div>\n\t\t\t<div class=\"taxes-container\"></div>\n\t\t\t<div class=\"grand-total-container\">\n\t\t\t\t<div>${__(\"Grand Total\")}</div>\n\t\t\t\t<div>0.00</div>\n\t\t\t</div>\n\t\t\t<div class=\"checkout-btn\">${__(\"Checkout\")}</div>\n\t\t\t<div class=\"edit-cart-btn\">${__(\"Edit Cart\")}</div>`\n\t\t);\n\n\t\tthis.$add_discount_elem = this.$component.find(\".add-discount-wrapper\");\n\t}\n\n\tmake_cart_numpad() {\n\t\tthis.$numpad_section = this.$component.find(\".numpad-section\");\n\n\t\tthis.number_pad = new erpnext.SalesInvoiceUI.NumberPad({\n\t\t\twrapper: this.$numpad_section,\n\t\t\tevents: {\n\t\t\t\tnumpad_event: this.on_numpad_event.bind(this),\n\t\t\t},\n\t\t\tcols: 5,\n\t\t\tkeys: [\n\t\t\t\t[1, 2, 3, \"Quantity\"],\n\t\t\t\t[4, 5, 6, \"Discount\"],\n\t\t\t\t[7, 8, 9, \"Rate\"],\n\t\t\t\t[\".\", 0, \"Delete\", \"Remove\"],\n\t\t\t],\n\t\t\tcss_classes: [\n\t\t\t\t[\"\", \"\", \"\", \"col-span-2\"],\n\t\t\t\t[\"\", \"\", \"\", \"col-span-2\"],\n\t\t\t\t[\"\", \"\", \"\", \"col-span-2\"],\n\t\t\t\t[\"\", \"\", \"\", \"col-span-2 remove-btn\"],\n\t\t\t],\n\t\t\tfieldnames_map: { Quantity: \"qty\", Discount: \"discount_percentage\" },\n\t\t});\n\n\t\tthis.$numpad_section.prepend(\n\t\t\t`<div class=\"numpad-totals\">\n\t\t\t<span class=\"numpad-item-qty-total\"></span>\n\t\t\t\t<span class=\"numpad-net-total\"></span>\n\t\t\t\t<span class=\"numpad-grand-total\"></span>\n\t\t\t</div>`\n\t\t);\n\n\t\tthis.$numpad_section.append(\n\t\t\t`<div class=\"numpad-btn checkout-btn\" data-button-value=\"checkout\">${__(\"Checkout\")}</div>`\n\t\t);\n\t}\n\n\tbind_events() {\n\t\tconst me = this;\n\t\tthis.$customer_section.on(\"click\", \".reset-customer-btn\", function () {\n\t\t\tme.reset_customer_selector();\n\t\t});\n\n\t\tthis.$customer_section.on(\"click\", \".close-details-btn\", function () {\n\t\t\tme.toggle_customer_info(false);\n\t\t});\n\n\t\tthis.$customer_section.on(\"click\", \".customer-display\", function (e) {\n\t\t\tif ($(e.target).closest(\".reset-customer-btn\").length) return;\n\n\t\t\tconst show = me.$cart_container.is(\":visible\");\n\t\t\tme.toggle_customer_info(show);\n\t\t});\n\n\t\tthis.$cart_items_wrapper.on(\"click\", \".cart-item-wrapper\", function () {\n\t\t\tconst $cart_item = $(this);\n\n\t\t\tme.toggle_item_highlight(this);\n\n\t\t\tconst payment_section_hidden = !me.$totals_section.find(\".edit-cart-btn\").is(\":visible\");\n\t\t\tif (!payment_section_hidden) {\n\t\t\t\t// payment section is visible\n\t\t\t\t// edit cart first and then open item details section\n\t\t\t\tme.$totals_section.find(\".edit-cart-btn\").click();\n\t\t\t}\n\n\t\t\tconst item_row_name = unescape($cart_item.attr(\"data-row-name\"));\n\t\t\tme.events.cart_item_clicked({ name: item_row_name });\n\t\t\tthis.numpad_value = \"\";\n\t\t});\n\n\t\tthis.$component.on(\"click\", \".checkout-btn\", async function () {\n\t\t\tif ($(this).attr(\"style\").indexOf(\"--blue-500\") == -1) return;\n\n\t\t\tawait me.events.checkout();\n\t\t\tme.toggle_checkout_btn(false);\n\n\t\t\tme.allow_discount_change && me.$add_discount_elem.removeClass(\"d-none\");\n\t\t});\n\n\t\tthis.$totals_section.on(\"click\", \".edit-cart-btn\", () => {\n\t\t\tthis.events.edit_cart();\n\t\t\tthis.toggle_checkout_btn(true);\n\t\t});\n\n\t\tthis.$component.on(\"click\", \".add-discount-wrapper\", () => {\n\t\t\tconst can_edit_discount = this.$add_discount_elem.find(\".edit-discount-btn\").length;\n\n\t\t\tif (!this.discount_field || can_edit_discount) this.show_discount_control();\n\t\t});\n\n\t\tfrappe.ui.form.on(\"POS Invoice\", \"paid_amount\", (frm) => {\n\t\t\t// called when discount is applied\n\t\t\tthis.update_totals_section(frm);\n\t\t});\n\t}\n\n\tattach_shortcuts() {\n\t\tfor (let row of this.number_pad.keys) {\n\t\t\tfor (let btn of row) {\n\t\t\t\tif (typeof btn !== \"string\") continue; // do not make shortcuts for numbers\n\n\t\t\t\tlet shortcut_key = `ctrl+${frappe.scrub(String(btn))[0]}`;\n\t\t\t\tif (btn === \"Delete\") shortcut_key = \"ctrl+backspace\";\n\t\t\t\tif (btn === \"Remove\") shortcut_key = \"shift+ctrl+backspace\";\n\t\t\t\tif (btn === \".\") shortcut_key = \"ctrl+>\";\n\n\t\t\t\t// to account for fieldname map\n\t\t\t\tconst fieldname = this.number_pad.fieldnames[btn]\n\t\t\t\t\t? this.number_pad.fieldnames[btn]\n\t\t\t\t\t: typeof btn === \"string\"\n\t\t\t\t\t? frappe.scrub(btn)\n\t\t\t\t\t: btn;\n\n\t\t\t\tlet shortcut_label = shortcut_key.split(\"+\").map(frappe.utils.to_title_case).join(\"+\");\n\t\t\t\tshortcut_label = frappe.utils.is_mac() ? shortcut_label.replace(\"Ctrl\", \"⌘\") : shortcut_label;\n\t\t\t\tthis.$numpad_section\n\t\t\t\t\t.find(`.numpad-btn[data-button-value=\"${fieldname}\"]`)\n\t\t\t\t\t.attr(\"title\", shortcut_label);\n\n\t\t\t\tfrappe.ui.keys.on(`${shortcut_key}`, () => {\n\t\t\t\t\tconst cart_is_visible = this.$component.is(\":visible\");\n\t\t\t\t\tif (cart_is_visible && this.item_is_selected && this.$numpad_section.is(\":visible\")) {\n\t\t\t\t\t\tthis.$numpad_section.find(`.numpad-btn[data-button-value=\"${fieldname}\"]`).click();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t\tconst ctrl_label = frappe.utils.is_mac() ? \"⌘\" : \"Ctrl\";\n\t\tthis.$component.find(\".checkout-btn\").attr(\"title\", `${ctrl_label}+Enter`);\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"ctrl+enter\",\n\t\t\taction: () => this.$component.find(\".checkout-btn\").click(),\n\t\t\tcondition: () =>\n\t\t\t\tthis.$component.is(\":visible\") && !this.$totals_section.find(\".edit-cart-btn\").is(\":visible\"),\n\t\t\tdescription: __(\"Checkout Order / Submit Order / New Order\"),\n\t\t\tignore_inputs: true,\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\t\tthis.$component.find(\".edit-cart-btn\").attr(\"title\", `${ctrl_label}+E`);\n\t\tfrappe.ui.keys.on(\"ctrl+e\", () => {\n\t\t\tconst item_cart_visible = this.$component.is(\":visible\");\n\t\t\tconst checkout_btn_invisible = !this.$totals_section.find(\".checkout-btn\").is(\"visible\");\n\t\t\tif (item_cart_visible && checkout_btn_invisible) {\n\t\t\t\tthis.$component.find(\".edit-cart-btn\").click();\n\t\t\t}\n\t\t});\n\t\tthis.$component.find(\".add-discount-wrapper\").attr(\"title\", `${ctrl_label}+D`);\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"ctrl+d\",\n\t\t\taction: () => this.$component.find(\".add-discount-wrapper\").click(),\n\t\t\tcondition: () => this.$add_discount_elem.is(\":visible\"),\n\t\t\tdescription: __(\"Add Order Discount\"),\n\t\t\tignore_inputs: true,\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\t\tfrappe.ui.keys.on(\"escape\", () => {\n\t\t\tconst item_cart_visible = this.$component.is(\":visible\");\n\t\t\tif (item_cart_visible && this.discount_field && this.discount_field.parent.is(\":visible\")) {\n\t\t\t\tthis.discount_field.set_value(0);\n\t\t\t}\n\t\t});\n\t}\n\n\ttoggle_item_highlight(item) {\n\t\tconst $cart_item = $(item);\n\t\tconst item_is_highlighted = $cart_item.attr(\"style\") == \"background-color:var(--gray-50);\";\n\n\t\tif (!item || item_is_highlighted) {\n\t\t\tthis.item_is_selected = false;\n\t\t\tthis.$cart_container.find(\".cart-item-wrapper\").css(\"background-color\", \"\");\n\t\t} else {\n\t\t\t$cart_item.css(\"background-color\", \"var(--control-bg)\");\n\t\t\tthis.item_is_selected = true;\n\t\t\tthis.$cart_container.find(\".cart-item-wrapper\").not(item).css(\"background-color\", \"\");\n\t\t}\n\t}\n\n\tmake_customer_selector() {\n\t\tthis.$customer_section.html(`\n\t\t\t<div class=\"customer-field\"></div>\n\t\t`);\n\t\tconst me = this;\n\t\tconst allowed_customer_group = this.allowed_customer_groups || [];\n\t\tlet filters = {};\n\t\tif (allowed_customer_group.length) {\n\t\t\tfilters = {\n\t\t\t\tcustomer_group: [\"in\", allowed_customer_group],\n\t\t\t};\n\t\t}\n\t\tthis.customer_field = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Customer\"),\n\t\t\t\tfieldtype: \"Link\",\n\t\t\t\toptions: \"Customer\",\n\t\t\t\tplaceholder: __(\"Search by customer name, phone, email.\"),\n\t\t\t\tget_query: function () {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tfilters: filters,\n\t\t\t\t\t};\n\t\t\t\t},\n\t\t\t\tonchange: function () {\n\t\t\t\t\tif (this.value) {\n\t\t\t\t\t\tconst frm = me.events.get_frm();\n\t\t\t\t\t\tfrappe.dom.freeze();\n\t\t\t\t\t\tfrappe.model.set_value(frm.doc.doctype, frm.doc.name, \"customer\", this.value);\n\t\t\t\t\t\tfrm.script_manager.trigger(\"customer\", frm.doc.doctype, frm.doc.name).then(() => {\n\t\t\t\t\t\t\tfrappe.run_serially([\n\t\t\t\t\t\t\t\t() => me.fetch_customer_details(this.value),\n\t\t\t\t\t\t\t\t() => me.events.customer_details_updated(me.customer_info),\n\t\t\t\t\t\t\t\t() => me.update_customer_section(),\n\t\t\t\t\t\t\t\t() => me.update_totals_section(),\n\t\t\t\t\t\t\t\t() => frappe.dom.unfreeze(),\n\t\t\t\t\t\t\t]);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t},\n\t\t\tparent: this.$customer_section.find(\".customer-field\"),\n\t\t\trender_input: true,\n\t\t});\n\t\tthis.customer_field.toggle_label(false);\n\t}\n\n\tfetch_customer_details(customer) {\n\t\tif (customer) {\n\t\t\treturn new Promise((resolve) => {\n\t\t\t\tfrappe.db\n\t\t\t\t\t.get_value(\"Customer\", customer, [\"email_id\", \"mobile_no\", \"image\", \"loyalty_program\"])\n\t\t\t\t\t.then(({ message }) => {\n\t\t\t\t\t\tconst { loyalty_program } = message;\n\t\t\t\t\t\t// if loyalty program then fetch loyalty points too\n\t\t\t\t\t\tif (loyalty_program) {\n\t\t\t\t\t\t\tfrappe.call({\n\t\t\t\t\t\t\t\tmethod: \"erpnext.accounts.doctype.loyalty_program.loyalty_program.get_loyalty_program_details_with_points\",\n\t\t\t\t\t\t\t\targs: { customer, loyalty_program, silent: true },\n\t\t\t\t\t\t\t\tcallback: (r) => {\n\t\t\t\t\t\t\t\t\tconst { loyalty_points, conversion_factor } = r.message;\n\t\t\t\t\t\t\t\t\tif (!r.exc) {\n\t\t\t\t\t\t\t\t\t\tthis.customer_info = {\n\t\t\t\t\t\t\t\t\t\t\t...message,\n\t\t\t\t\t\t\t\t\t\t\tcustomer,\n\t\t\t\t\t\t\t\t\t\t\tloyalty_points,\n\t\t\t\t\t\t\t\t\t\t\tconversion_factor,\n\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.customer_info = { ...message, customer };\n\t\t\t\t\t\t\tresolve();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t});\n\t\t} else {\n\t\t\treturn new Promise((resolve) => {\n\t\t\t\tthis.customer_info = {};\n\t\t\t\tresolve();\n\t\t\t});\n\t\t}\n\t}\n\n\tshow_discount_control() {\n\t\tthis.$add_discount_elem.css({ padding: \"0px\", border: \"none\" });\n\t\tthis.$add_discount_elem.html(`<div class=\"add-discount-field\"></div>`);\n\t\tconst me = this;\n\t\tconst frm = me.events.get_frm();\n\t\tlet discount = frm.doc.additional_discount_percentage;\n\n\t\tthis.discount_field = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Discount\"),\n\t\t\t\tfieldtype: \"Data\",\n\t\t\t\tplaceholder: discount ? discount + \"%\" : __(\"Enter discount percentage.\"),\n\t\t\t\tinput_class: \"input-xs\",\n\t\t\t\tonchange: function () {\n\t\t\t\t\tthis.value = flt(this.value);\n\t\t\t\t\tif (this.value > 100) {\n\t\t\t\t\t\tfrappe.msgprint({\n\t\t\t\t\t\t\ttitle: __(\"Invalid Discount\"),\n\t\t\t\t\t\t\tindicator: \"red\",\n\t\t\t\t\t\t\tmessage: __(\"Discount cannot be greater than 100%.\"),\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.value = 0;\n\t\t\t\t\t}\n\t\t\t\t\tfrappe.model.set_value(\n\t\t\t\t\t\tfrm.doc.doctype,\n\t\t\t\t\t\tfrm.doc.name,\n\t\t\t\t\t\t\"additional_discount_percentage\",\n\t\t\t\t\t\tflt(this.value)\n\t\t\t\t\t);\n\t\t\t\t\tme.hide_discount_control(this.value);\n\t\t\t\t},\n\t\t\t},\n\t\t\tparent: this.$add_discount_elem.find(\".add-discount-field\"),\n\t\t\trender_input: true,\n\t\t});\n\t\tthis.discount_field.toggle_label(false);\n\t\tthis.discount_field.set_focus();\n\t}\n\n\thide_discount_control(discount) {\n\t\tif (!flt(discount)) {\n\t\t\tthis.$add_discount_elem.css({\n\t\t\t\tborder: \"1px dashed var(--gray-500)\",\n\t\t\t\tpadding: \"var(--padding-sm) var(--padding-md)\",\n\t\t\t});\n\t\t\tthis.$add_discount_elem.html(`${this.get_discount_icon()} ${__(\"Add Discount\")}`);\n\t\t\tthis.discount_field = undefined;\n\t\t} else {\n\t\t\tthis.$add_discount_elem.css({\n\t\t\t\tborder: \"1px dashed var(--dark-green-500)\",\n\t\t\t\tpadding: \"var(--padding-sm) var(--padding-md)\",\n\t\t\t});\n\t\t\tthis.$add_discount_elem.html(\n\t\t\t\t`<div class=\"edit-discount-btn\">\n\t\t\t\t\t${this.get_discount_icon()} ${__(\"Additional\")}&nbsp;${String(discount).bold()}% ${__(\"discount applied\")}\n\t\t\t\t</div>`\n\t\t\t);\n\t\t}\n\t}\n\n\tupdate_customer_section() {\n\t\tconst me = this;\n\t\tconst { customer, email_id = \"\", mobile_no = \"\", image } = this.customer_info || {};\n\n\t\tif (customer) {\n\t\t\tthis.$customer_section.html(\n\t\t\t\t`<div class=\"customer-details\">\n\t\t\t\t\t<div class=\"customer-display\">\n\t\t\t\t\t\t${this.get_customer_image()}\n\t\t\t\t\t\t<div class=\"customer-name-desc\">\n\t\t\t\t\t\t\t<div class=\"customer-name\">${customer}</div>\n\t\t\t\t\t\t\t${get_customer_description()}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"reset-customer-btn\" data-customer=\"${escape(customer)}\">\n\t\t\t\t\t\t\t<svg width=\"32\" height=\"32\" viewBox=\"0 0 14 14\" fill=\"none\">\n\t\t\t\t\t\t\t\t<path d=\"M4.93764 4.93759L7.00003 6.99998M9.06243 9.06238L7.00003 6.99998M7.00003 6.99998L4.93764 9.06238L9.06243 4.93759\" stroke=\"#8D99A6\"/>\n\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>`\n\t\t\t);\n\t\t} else {\n\t\t\t// reset customer selector\n\t\t\tthis.reset_customer_selector();\n\t\t}\n\n\t\tfunction get_customer_description() {\n\t\t\tif (!email_id && !mobile_no) {\n\t\t\t\treturn `<div class=\"customer-desc\">${__(\"Click to add email / phone\")}</div>`;\n\t\t\t} else if (email_id && !mobile_no) {\n\t\t\t\treturn `<div class=\"customer-desc\">${email_id}</div>`;\n\t\t\t} else if (mobile_no && !email_id) {\n\t\t\t\treturn `<div class=\"customer-desc\">${mobile_no}</div>`;\n\t\t\t} else {\n\t\t\t\treturn `<div class=\"customer-desc\">${email_id} - ${mobile_no}</div>`;\n\t\t\t}\n\t\t}\n\t}\n\n\tget_customer_image() {\n\t\tconst { customer, image } = this.customer_info || {};\n\t\tif (image) {\n\t\t\treturn `<div class=\"customer-image\"><img src=\"${image}\" alt=\"${image}\"\"></div>`;\n\t\t} else {\n\t\t\treturn `<div class=\"customer-image customer-abbr\">${frappe.get_abbr(customer)}</div>`;\n\t\t}\n\t}\n\n\tupdate_totals_section(frm) {\n\t\tif (!frm) frm = this.events.get_frm();\n\n\t\tthis.render_net_total(frm.doc.net_total);\n\t\tthis.render_total_item_qty(frm.doc.items);\n\t\tconst grand_total = cint(frappe.sys_defaults.disable_rounded_total)\n\t\t\t? frm.doc.grand_total\n\t\t\t: frm.doc.rounded_total;\n\t\tthis.render_grand_total(grand_total);\n\n\t\tthis.render_taxes(frm.doc.taxes);\n\t}\n\n\trender_net_total(value) {\n\t\tconst currency = this.events.get_frm().doc.currency;\n\t\tthis.$totals_section\n\t\t\t.find(\".net-total-container\")\n\t\t\t.html(`<div>${__(\"Net Total\")}</div><div>${format_currency(value, currency)}</div>`);\n\n\t\tthis.$numpad_section\n\t\t\t.find(\".numpad-net-total\")\n\t\t\t.html(`<div>${__(\"Net Total\")}: <span>${format_currency(value, currency)}</span></div>`);\n\t}\n\n\trender_total_item_qty(items) {\n\t\tvar total_item_qty = 0;\n\t\titems.map((item) => {\n\t\t\ttotal_item_qty = total_item_qty + item.qty;\n\t\t});\n\n\t\tthis.$totals_section\n\t\t\t.find(\".item-qty-total-container\")\n\t\t\t.html(`<div>${__(\"Total Quantity\")}</div><div>${total_item_qty}</div>`);\n\n\t\tthis.$numpad_section\n\t\t\t.find(\".numpad-item-qty-total\")\n\t\t\t.html(`<div>${__(\"Total Quantity\")}: <span>${total_item_qty}</span></div>`);\n\t}\n\n\trender_grand_total(value) {\n\t\tconst currency = this.events.get_frm().doc.currency;\n\t\tthis.$totals_section\n\t\t\t.find(\".grand-total-container\")\n\t\t\t.html(`<div>${__(\"Grand Total\")}</div><div>${format_currency(value, currency)}</div>`);\n\n\t\tthis.$numpad_section\n\t\t\t.find(\".numpad-grand-total\")\n\t\t\t.html(`<div>${__(\"Grand Total\")}: <span>${format_currency(value, currency)}</span></div>`);\n\t}\n\n\trender_taxes(taxes) {\n\t\tif (taxes && taxes.length) {\n\t\t\tconst currency = this.events.get_frm().doc.currency;\n\t\t\tconst taxes_html = taxes\n\t\t\t\t.map((t) => {\n\t\t\t\t\tif (t.tax_amount_after_discount_amount == 0.0) return;\n\t\t\t\t\t// if tax rate is 0, don't print it.\n\t\t\t\t\tconst description = /[0-9]+/.test(t.description)\n\t\t\t\t\t\t? t.description\n\t\t\t\t\t\t: t.rate != 0\n\t\t\t\t\t\t? `${t.description} @ ${t.rate}%`\n\t\t\t\t\t\t: t.description;\n\t\t\t\t\treturn `<div class=\"tax-row\">\n\t\t\t\t\t<div class=\"tax-label\">${description}</div>\n\t\t\t\t\t<div class=\"tax-value\">${format_currency(t.tax_amount_after_discount_amount, currency)}</div>\n\t\t\t\t</div>`;\n\t\t\t\t})\n\t\t\t\t.join(\"\");\n\t\t\tthis.$totals_section.find(\".taxes-container\").css(\"display\", \"flex\").html(taxes_html);\n\t\t} else {\n\t\t\tthis.$totals_section.find(\".taxes-container\").css(\"display\", \"none\").html(\"\");\n\t\t}\n\t}\n\n\tget_cart_item({ name }) {\n\t\tconst item_selector = `.cart-item-wrapper[data-row-name=\"${escape(name)}\"]`;\n\t\treturn this.$cart_items_wrapper.find(item_selector);\n\t}\n\n\tget_item_from_frm(item) {\n\t\tconst doc = this.events.get_frm().doc;\n\t\treturn doc.items.find((i) => i.name == item.name);\n\t}\n\n\tupdate_item_html(item, remove_item) {\n\t\tconst $item = this.get_cart_item(item);\n\n\t\tif (remove_item) {\n\t\t\t$item && $item.next().remove() && $item.remove();\n\t\t} else {\n\t\t\tconst item_row = this.get_item_from_frm(item);\n\t\t\tthis.render_cart_item(item_row, $item);\n\t\t}\n\n\t\tconst no_of_cart_items = this.$cart_items_wrapper.find(\".cart-item-wrapper\").length;\n\t\tthis.highlight_checkout_btn(no_of_cart_items > 0);\n\n\t\tthis.update_empty_cart_section(no_of_cart_items);\n\t}\n\n\trender_cart_item(item_data, $item_to_update) {\n\t\tconst currency = this.events.get_frm().doc.currency;\n\t\tconst me = this;\n\n\t\tif (!$item_to_update.length) {\n\t\t\tthis.$cart_items_wrapper.append(\n\t\t\t\t`<div class=\"cart-item-wrapper\" data-row-name=\"${escape(item_data.name)}\"></div>\n\t\t\t\t<div class=\"seperator\"></div>`\n\t\t\t);\n\t\t\t$item_to_update = this.get_cart_item(item_data);\n\t\t}\n\n\t\t$item_to_update.html(\n\t\t\t`${get_item_image_html()}\n\t\t\t<div class=\"item-name-desc\">\n\t\t\t\t<div class=\"item-name\">\n\t\t\t\t\t${item_data.item_name}\n\t\t\t\t</div>\n\t\t\t\t${get_description_html()}\n\t\t\t</div>\n\t\t\t${get_rate_discount_html()}`\n\t\t);\n\n\t\tset_dynamic_rate_header_width();\n\n\t\tfunction set_dynamic_rate_header_width() {\n\t\t\tconst rate_cols = Array.from(me.$cart_items_wrapper.find(\".item-rate-amount\"));\n\t\t\tme.$cart_header.find(\".rate-amount-header\").css(\"width\", \"\");\n\t\t\tme.$cart_items_wrapper.find(\".item-rate-amount\").css(\"width\", \"\");\n\t\t\tlet max_width = rate_cols.reduce((max_width, elm) => {\n\t\t\t\tif ($(elm).width() > max_width) max_width = $(elm).width();\n\t\t\t\treturn max_width;\n\t\t\t}, 0);\n\n\t\t\tmax_width += 1;\n\t\t\tif (max_width == 1) max_width = \"\";\n\n\t\t\tme.$cart_header.find(\".rate-amount-header\").css(\"width\", max_width);\n\t\t\tme.$cart_items_wrapper.find(\".item-rate-amount\").css(\"width\", max_width);\n\t\t}\n\n\t\tfunction get_rate_discount_html() {\n\t\t\tif (item_data.rate && item_data.amount && item_data.rate !== item_data.amount) {\n\t\t\t\treturn `\n\t\t\t\t\t<div class=\"item-qty-rate\">\n\t\t\t\t\t\t<div class=\"item-qty\"><span>${item_data.qty || 0} ${item_data.uom}</span></div>\n\t\t\t\t\t\t<div class=\"item-rate-amount\">\n\t\t\t\t\t\t\t<div class=\"item-rate\">${format_currency(item_data.amount, currency)}</div>\n\t\t\t\t\t\t\t<div class=\"item-amount\">${format_currency(item_data.rate, currency)}</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>`;\n\t\t\t} else {\n\t\t\t\treturn `\n\t\t\t\t\t<div class=\"item-qty-rate\">\n\t\t\t\t\t\t<div class=\"item-qty\"><span>${item_data.qty || 0} ${item_data.uom}</span></div>\n\t\t\t\t\t\t<div class=\"item-rate-amount\">\n\t\t\t\t\t\t\t<div class=\"item-rate\">${format_currency(item_data.rate, currency)}</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>`;\n\t\t\t}\n\t\t}\n\n\t\tfunction get_description_html() {\n\t\t\tif (item_data.description) {\n\t\t\t\tif (item_data.description.indexOf(\"<div>\") != -1) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\titem_data.description = $(item_data.description).text();\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\titem_data.description = item_data.description\n\t\t\t\t\t\t\t.replace(/<div>/g, \" \")\n\t\t\t\t\t\t\t.replace(/<\\/div>/g, \" \")\n\t\t\t\t\t\t\t.replace(/ +/g, \" \");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\titem_data.description = frappe.ellipsis(item_data.description, 45);\n\t\t\t\treturn `<div class=\"item-desc\">${item_data.description}</div>`;\n\t\t\t}\n\t\t\treturn ``;\n\t\t}\n\n\t\tfunction get_item_image_html() {\n\t\t\tconst { image, item_name } = item_data;\n\t\t\tif (!me.hide_images && image) {\n\t\t\t\treturn `\n\t\t\t\t\t<div class=\"item-image\">\n\t\t\t\t\t\t<img\n\t\t\t\t\t\t\tonerror=\"cur_pos.cart.handle_broken_image(this)\"\n\t\t\t\t\t\t\tsrc=\"${image}\" alt=\"${frappe.get_abbr(item_name)}\"\">\n\t\t\t\t\t</div>`;\n\t\t\t} else {\n\t\t\t\treturn `<div class=\"item-image item-abbr\">${frappe.get_abbr(item_name)}</div>`;\n\t\t\t}\n\t\t}\n\t}\n\n\thandle_broken_image($img) {\n\t\tconst item_abbr = $($img).attr(\"alt\");\n\t\t$($img).parent().replaceWith(`<div class=\"item-image item-abbr\">${item_abbr}</div>`);\n\t}\n\n\tupdate_selector_value_in_cart_item(selector, value, item) {\n\t\tconst $item_to_update = this.get_cart_item(item);\n\t\t$item_to_update.attr(`data-${selector}`, escape(value));\n\t}\n\n\ttoggle_checkout_btn(show_checkout) {\n\t\tif (show_checkout) {\n\t\t\tthis.$totals_section.find(\".checkout-btn\").css(\"display\", \"flex\");\n\t\t\tthis.$totals_section.find(\".edit-cart-btn\").css(\"display\", \"none\");\n\t\t} else {\n\t\t\tthis.$totals_section.find(\".checkout-btn\").css(\"display\", \"none\");\n\t\t\tthis.$totals_section.find(\".edit-cart-btn\").css(\"display\", \"flex\");\n\t\t}\n\t}\n\n\thighlight_checkout_btn(toggle) {\n\t\tif (toggle) {\n\t\t\tthis.$add_discount_elem.css(\"display\", \"flex\");\n\t\t\tthis.$cart_container.find(\".checkout-btn\").css({\n\t\t\t\t\"background-color\": \"var(--blue-500)\",\n\t\t\t});\n\t\t} else {\n\t\t\tthis.$add_discount_elem.css(\"display\", \"none\");\n\t\t\tthis.$cart_container.find(\".checkout-btn\").css({\n\t\t\t\t\"background-color\": \"var(--blue-200)\",\n\t\t\t});\n\t\t}\n\t}\n\n\tupdate_empty_cart_section(no_of_cart_items) {\n\t\tconst $no_item_element = this.$cart_items_wrapper.find(\".no-item-wrapper\");\n\n\t\t// if cart has items and no item is present\n\t\tno_of_cart_items > 0 &&\n\t\t\t$no_item_element &&\n\t\t\t$no_item_element.remove() &&\n\t\t\tthis.$cart_header.css(\"display\", \"flex\");\n\n\t\tno_of_cart_items === 0 && !$no_item_element.length && this.make_no_items_placeholder();\n\t}\n\n\ton_numpad_event($btn) {\n\t\tconst current_action = $btn.attr(\"data-button-value\");\n\t\tconst action_is_field_edit = [\"qty\", \"discount_percentage\", \"rate\"].includes(current_action);\n\t\tconst action_is_allowed = action_is_field_edit\n\t\t\t? (current_action == \"rate\" && this.allow_rate_change) ||\n\t\t\t  (current_action == \"discount_percentage\" && this.allow_discount_change) ||\n\t\t\t  current_action == \"qty\"\n\t\t\t: true;\n\n\t\tconst action_is_pressed_twice = this.prev_action === current_action;\n\t\tconst first_click_event = !this.prev_action;\n\t\tconst field_to_edit_changed = this.prev_action && this.prev_action != current_action;\n\n\t\tif (action_is_field_edit) {\n\t\t\tif (!action_is_allowed) {\n\t\t\t\tconst label = current_action == \"rate\" ? \"Rate\".bold() : \"Discount\".bold();\n\t\t\t\tconst message = __(\"Editing {0} is not allowed as per POS Profile settings\", [label]);\n\t\t\t\tfrappe.show_alert({\n\t\t\t\t\tindicator: \"red\",\n\t\t\t\t\tmessage: message,\n\t\t\t\t});\n\t\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.highlight_numpad_btn($btn, current_action);\n\n\t\t\tif (first_click_event || field_to_edit_changed) {\n\t\t\t\tthis.prev_action = current_action;\n\t\t\t} else if (action_is_pressed_twice) {\n\t\t\t\tthis.prev_action = undefined;\n\t\t\t}\n\t\t\tthis.numpad_value = \"\";\n\t\t} else if (current_action === \"checkout\") {\n\t\t\tthis.prev_action = undefined;\n\t\t\tthis.toggle_item_highlight();\n\t\t\tthis.events.numpad_event(undefined, current_action);\n\t\t\treturn;\n\t\t} else if (current_action === \"remove\") {\n\t\t\tthis.prev_action = undefined;\n\t\t\tthis.toggle_item_highlight();\n\t\t\tthis.events.numpad_event(undefined, current_action);\n\t\t\treturn;\n\t\t} else {\n\t\t\tthis.numpad_value =\n\t\t\t\tcurrent_action === \"delete\"\n\t\t\t\t\t? this.numpad_value.slice(0, -1)\n\t\t\t\t\t: this.numpad_value + current_action;\n\t\t\tthis.numpad_value = this.numpad_value || 0;\n\t\t}\n\n\t\tconst first_click_event_is_not_field_edit = !action_is_field_edit && first_click_event;\n\n\t\tif (first_click_event_is_not_field_edit) {\n\t\t\tfrappe.show_alert({\n\t\t\t\tindicator: \"red\",\n\t\t\t\tmessage: __(\"Please select a field to edit from numpad\"),\n\t\t\t});\n\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\treturn;\n\t\t}\n\n\t\tif (flt(this.numpad_value) > 100 && this.prev_action === \"discount_percentage\") {\n\t\t\tfrappe.show_alert({\n\t\t\t\tmessage: __(\"Discount cannot be greater than 100%\"),\n\t\t\t\tindicator: \"orange\",\n\t\t\t});\n\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\tthis.numpad_value = current_action;\n\t\t}\n\n\t\tthis.events.numpad_event(this.numpad_value, this.prev_action);\n\t}\n\n\thighlight_numpad_btn($btn, curr_action) {\n\t\tconst curr_action_is_highlighted = $btn.hasClass(\"highlighted-numpad-btn\");\n\t\tconst curr_action_is_action = [\"qty\", \"discount_percentage\", \"rate\", \"done\"].includes(curr_action);\n\n\t\tif (!curr_action_is_highlighted) {\n\t\t\t$btn.addClass(\"highlighted-numpad-btn\");\n\t\t}\n\t\tif (this.prev_action === curr_action && curr_action_is_highlighted) {\n\t\t\t// if Qty is pressed twice\n\t\t\t$btn.removeClass(\"highlighted-numpad-btn\");\n\t\t}\n\t\tif (this.prev_action && this.prev_action !== curr_action && curr_action_is_action) {\n\t\t\t// Order: Qty -> Rate then remove Qty highlight\n\t\t\tconst prev_btn = $(`[data-button-value='${this.prev_action}']`);\n\t\t\tprev_btn.removeClass(\"highlighted-numpad-btn\");\n\t\t}\n\t\tif (!curr_action_is_action || curr_action === \"done\") {\n\t\t\t// if numbers are clicked\n\t\t\tsetTimeout(() => {\n\t\t\t\t$btn.removeClass(\"highlighted-numpad-btn\");\n\t\t\t}, 200);\n\t\t}\n\t}\n\n\ttoggle_numpad(show) {\n\t\tif (show) {\n\t\t\tthis.$totals_section.css(\"display\", \"none\");\n\t\t\tthis.$numpad_section.css(\"display\", \"flex\");\n\t\t} else {\n\t\t\tthis.$totals_section.css(\"display\", \"flex\");\n\t\t\tthis.$numpad_section.css(\"display\", \"none\");\n\t\t}\n\t\tthis.reset_numpad();\n\t}\n\n\treset_numpad() {\n\t\tthis.numpad_value = \"\";\n\t\tthis.prev_action = undefined;\n\t\tthis.$numpad_section.find(\".highlighted-numpad-btn\").removeClass(\"highlighted-numpad-btn\");\n\t}\n\n\ttoggle_numpad_field_edit(fieldname) {\n\t\tif ([\"qty\", \"discount_percentage\", \"rate\"].includes(fieldname)) {\n\t\t\tthis.$numpad_section.find(`[data-button-value=\"${fieldname}\"]`).click();\n\t\t}\n\t}\n\n\ttoggle_customer_info(show) {\n\t\tif (show) {\n\t\t\tconst { customer } = this.customer_info || {};\n\n\t\t\tthis.$cart_container.css(\"display\", \"none\");\n\t\t\tthis.$customer_section.css({\n\t\t\t\theight: \"100%\",\n\t\t\t\t\"padding-top\": \"0px\",\n\t\t\t});\n\t\t\tthis.$customer_section.find(\".customer-details\").html(\n\t\t\t\t`<div class=\"header\">\n\t\t\t\t\t<div class=\"label\">${__(\"Contact Details\")}</div>\n\t\t\t\t\t<div class=\"close-details-btn\">\n\t\t\t\t\t\t<svg width=\"32\" height=\"32\" viewBox=\"0 0 14 14\" fill=\"none\">\n\t\t\t\t\t\t\t<path d=\"M4.93764 4.93759L7.00003 6.99998M9.06243 9.06238L7.00003 6.99998M7.00003 6.99998L4.93764 9.06238L9.06243 4.93759\" stroke=\"#8D99A6\"/>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"customer-display\">\n\t\t\t\t\t${this.get_customer_image()}\n\t\t\t\t\t<div class=\"customer-name-desc\">\n\t\t\t\t\t\t<div class=\"customer-name\">${customer}</div>\n\t\t\t\t\t\t<div class=\"customer-desc\"></div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"customer-fields-container\">\n\t\t\t\t\t<div class=\"email_id-field\"></div>\n\t\t\t\t\t<div class=\"mobile_no-field\"></div>\n\t\t\t\t\t<div class=\"loyalty_program-field\"></div>\n\t\t\t\t\t<div class=\"loyalty_points-field\"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"transactions-label\">${__(\"Recent Transactions\")}</div>`\n\t\t\t);\n\t\t\t// transactions need to be in diff div from sticky elem for scrolling\n\t\t\tthis.$customer_section.append(`<div class=\"customer-transactions\"></div>`);\n\n\t\t\tthis.render_customer_fields();\n\t\t\tthis.fetch_customer_transactions();\n\t\t} else {\n\t\t\tthis.$cart_container.css(\"display\", \"flex\");\n\t\t\tthis.$customer_section.css({\n\t\t\t\theight: \"\",\n\t\t\t\t\"padding-top\": \"\",\n\t\t\t});\n\n\t\t\tthis.update_customer_section();\n\t\t}\n\t}\n\n\trender_customer_fields() {\n\t\tconst $customer_form = this.$customer_section.find(\".customer-fields-container\");\n\n\t\tconst dfs = [\n\t\t\t{\n\t\t\t\tfieldname: \"email_id\",\n\t\t\t\tlabel: __(\"Email\"),\n\t\t\t\tfieldtype: \"Data\",\n\t\t\t\toptions: \"email\",\n\t\t\t\tplaceholder: __(\"Enter customer's email\"),\n\t\t\t},\n\t\t\t{\n\t\t\t\tfieldname: \"mobile_no\",\n\t\t\t\tlabel: __(\"Phone Number\"),\n\t\t\t\tfieldtype: \"Data\",\n\t\t\t\tplaceholder: __(\"Enter customer's phone number\"),\n\t\t\t},\n\t\t\t{\n\t\t\t\tfieldname: \"loyalty_program\",\n\t\t\t\tlabel: __(\"Loyalty Program\"),\n\t\t\t\tfieldtype: \"Link\",\n\t\t\t\toptions: \"Loyalty Program\",\n\t\t\t\tplaceholder: __(\"Select Loyalty Program\"),\n\t\t\t},\n\t\t\t{\n\t\t\t\tfieldname: \"loyalty_points\",\n\t\t\t\tlabel: __(\"Loyalty Points\"),\n\t\t\t\tfieldtype: \"Data\",\n\t\t\t\tread_only: 1,\n\t\t\t},\n\t\t];\n\n\t\tconst me = this;\n\t\tdfs.forEach((df) => {\n\t\t\tthis[`customer_${df.fieldname}_field`] = frappe.ui.form.make_control({\n\t\t\t\tdf: df,\n\t\t\t\tparent: $customer_form.find(`.${df.fieldname}-field`),\n\t\t\t\trender_input: true,\n\t\t\t});\n\t\t\tthis[`customer_${df.fieldname}_field`].$input?.on(\"blur\", () => {\n\t\t\t\thandle_customer_field_change.apply(this[`customer_${df.fieldname}_field`]);\n\t\t\t});\n\t\t\tthis[`customer_${df.fieldname}_field`].set_value(this.customer_info[df.fieldname]);\n\t\t});\n\n\t\tfunction handle_customer_field_change() {\n\t\t\tconst current_value = me.customer_info[this.df.fieldname];\n\t\t\tconst current_customer = me.customer_info.customer;\n\n\t\t\tif (this.value && current_value != this.value && this.df.fieldname != \"loyalty_points\") {\n\t\t\t\tfrappe.call({\n\t\t\t\t\tmethod: \"erpnext.selling.page.point_of_sale.point_of_sale.set_customer_info\",\n\t\t\t\t\targs: {\n\t\t\t\t\t\tfieldname: this.df.fieldname,\n\t\t\t\t\t\tcustomer: current_customer,\n\t\t\t\t\t\tvalue: this.value,\n\t\t\t\t\t},\n\t\t\t\t\tcallback: (r) => {\n\t\t\t\t\t\tif (!r.exc) {\n\t\t\t\t\t\t\tme.customer_info[this.df.fieldname] = this.value;\n\t\t\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\t\t\tmessage: __(\"Customer contact updated successfully.\"),\n\t\t\t\t\t\t\t\tindicator: \"green\",\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tfrappe.utils.play_sound(\"submit\");\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\tfetch_customer_transactions() {\n\t\tfrappe.db\n\t\t\t.get_list(\"POS Invoice\", {\n\t\t\t\tfilters: { customer: this.customer_info.customer, docstatus: 1 },\n\t\t\t\tfields: [\"name\", \"grand_total\", \"status\", \"posting_date\", \"posting_time\", \"currency\"],\n\t\t\t\tlimit: 20,\n\t\t\t})\n\t\t\t.then((res) => {\n\t\t\t\tconst transaction_container = this.$customer_section.find(\".customer-transactions\");\n\n\t\t\t\tif (!res.length) {\n\t\t\t\t\ttransaction_container.html(\n\t\t\t\t\t\t`<div class=\"no-transactions-placeholder\">${__(\"No recent transactions found\")}</div>`\n\t\t\t\t\t);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst elapsed_time = moment(res[0].posting_date + \" \" + res[0].posting_time).fromNow();\n\t\t\t\tthis.$customer_section\n\t\t\t\t\t.find(\".customer-desc\")\n\t\t\t\t\t.html(`${__(\"Last transacted\")} ${__(elapsed_time)}`);\n\n\t\t\t\tres.forEach((invoice) => {\n\t\t\t\t\tconst posting_datetime = frappe.datetime.str_to_user(\n\t\t\t\t\t\tinvoice.posting_date + \" \" + invoice.posting_time\n\t\t\t\t\t);\n\t\t\t\t\tlet indicator_color = {\n\t\t\t\t\t\tPaid: \"green\",\n\t\t\t\t\t\tDraft: \"red\",\n\t\t\t\t\t\tReturn: \"gray\",\n\t\t\t\t\t\tConsolidated: \"blue\",\n\t\t\t\t\t};\n\n\t\t\t\t\ttransaction_container.append(\n\t\t\t\t\t\t`<div class=\"invoice-wrapper\" data-invoice-name=\"${escape(invoice.name)}\">\n\t\t\t\t\t\t<div class=\"invoice-name-date\">\n\t\t\t\t\t\t\t<div class=\"invoice-name\">${invoice.name}</div>\n\t\t\t\t\t\t\t<div class=\"invoice-date\">${posting_datetime}</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"invoice-total-status\">\n\t\t\t\t\t\t\t<div class=\"invoice-total\">\n\t\t\t\t\t\t\t\t${format_currency(invoice.grand_total, invoice.currency, 0) || 0}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"invoice-status\">\n\t\t\t\t\t\t\t\t<span class=\"indicator-pill whitespace-nowrap ${indicator_color[invoice.status]}\">\n\t\t\t\t\t\t\t\t\t<span>${__(invoice.status)}</span>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"seperator\"></div>`\n\t\t\t\t\t);\n\t\t\t\t});\n\t\t\t});\n\t}\n\n\tattach_refresh_field_event(frm) {\n\t\t$(frm.wrapper).off(\"refresh-fields\");\n\t\t$(frm.wrapper).on(\"refresh-fields\", () => {\n\t\t\tif (frm.doc.items.length) {\n\t\t\t\tthis.$cart_items_wrapper.html(\"\");\n\t\t\t\tfrm.doc.items.forEach((item) => {\n\t\t\t\t\tthis.update_item_html(item);\n\t\t\t\t});\n\t\t\t}\n\t\t\tthis.update_totals_section(frm);\n\t\t});\n\t}\n\n\tload_invoice() {\n\t\tconst frm = this.events.get_frm();\n\n\t\tthis.attach_refresh_field_event(frm);\n\n\t\tthis.fetch_customer_details(frm.doc.customer).then(() => {\n\t\t\tthis.events.customer_details_updated(this.customer_info);\n\t\t\tthis.update_customer_section();\n\t\t});\n\n\t\tthis.$cart_items_wrapper.html(\"\");\n\t\tif (frm.doc.items.length) {\n\t\t\tfrm.doc.items.forEach((item) => {\n\t\t\t\tthis.update_item_html(item);\n\t\t\t});\n\t\t} else {\n\t\t\tthis.make_no_items_placeholder();\n\t\t\tthis.highlight_checkout_btn(false);\n\t\t}\n\n\t\tthis.hide_discount_control(frm.doc.additional_discount_percentage);\n\t\tthis.update_totals_section(frm);\n\n\t\tif (frm.doc.docstatus === 1) {\n\t\t\tthis.$totals_section.find(\".checkout-btn\").css(\"display\", \"none\");\n\t\t\tthis.$totals_section.find(\".edit-cart-btn\").css(\"display\", \"none\");\n\t\t} else {\n\t\t\tthis.$totals_section.find(\".checkout-btn\").css(\"display\", \"flex\");\n\t\t\tthis.$totals_section.find(\".edit-cart-btn\").css(\"display\", \"none\");\n\t\t}\n\n\t\tthis.toggle_component(true);\n\t}\n\n\ttoggle_component(show) {\n\t\tshow ? this.$component.css(\"display\", \"flex\") : this.$component.css(\"display\", \"none\");\n\t}\n};\n", "import onScan from \"onscan.js\";\n\nerpnext.SalesInvoiceUI.ItemSelector = class {\n\t// eslint-disable-next-line no-unused-vars\n\tconstructor({ frm, wrapper, events, pos_profile, settings }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\t\tthis.pos_profile = pos_profile;\n\t\tthis.hide_images = settings.hide_images;\n\t\tthis.auto_add_item = settings.auto_add_item_to_cart;\n\n\t\tthis.inti_component();\n\t}\n\n\tinti_component() {\n\t\tthis.prepare_dom();\n\t\tthis.make_search_bar();\n\t\tthis.load_items_data();\n\t\tthis.bind_events();\n\t\tthis.attach_shortcuts();\n\t\tthis.setup_responsive_layout();\n\t}\nx\n\tprepare_dom() {\n\t\tthis.wrapper.append(\n\t\t\t`<section class=\"items-selector\">\n\t\t\t\t<div class=\"filter-section\">\n\t\t\t\t\t<div class=\"label\">${__(\"All Items\")}</div>\n\t\t\t\t\t<div class=\"search-field\"></div>\n\t\t\t\t\t<div class=\"item-group-field\"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"items-container\"></div>\n\t\t\t</section>`\n\t\t);\n\n\t\tthis.$component = this.wrapper.find(\".items-selector\");\n\t\tthis.$items_container = this.$component.find(\".items-container\");\n\t\t\n\t\t// Add responsive CSS classes\n\t\tthis.$component.addClass('pos-responsive');\n\t\tthis.$items_container.addClass('items-grid-responsive');\n\t}\n\n\tsetup_responsive_layout() {\n\t\t// Add responsive CSS styles\n\t\tif (!document.getElementById('pos-responsive-styles')) {\n\t\t\tconst style = document.createElement('style');\n\t\t\tstyle.id = 'pos-responsive-styles';\n\t\t\tstyle.textContent = `\n\t\t\t\t/* Base styles for POS */\n\t\t\t\t.pos-responsive {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tmin-height: 0;\n\t\t\t\t}\n\n\t\t\t\t.pos-responsive .filter-section {\n\t\t\t\t\tdisplay: grid;\n\t\t\t\t\tgap: var(--margin-sm, 8px);\n\t\t\t\t\tpadding: var(--margin-sm, 8px);\n\t\t\t\t\tborder-bottom: 1px solid var(--border-color, #e5e7eb);\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t}\n\n\t\t\t\t.pos-responsive .items-container {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\toverflow-y: auto;\n\t\t\t\t\tpadding: var(--margin-sm, 8px);\n\t\t\t\t\tmin-height: 0;\n\t\t\t\t}\n\n\t\t\t\t.items-grid-responsive {\n\t\t\t\t\tdisplay: grid;\n\t\t\t\t\tgap: var(--margin-sm, 8px);\n\t\t\t\t\tgrid-auto-rows: min-content;\n\t\t\t\t}\n\n\t\t\t\t/* Item wrapper responsive styles */\n\t\t\t\t.item-wrapper {\n\t\t\t\t\tborder: 1px solid var(--border-color, #e5e7eb);\n\t\t\t\t\tborder-radius: var(--border-radius, 8px);\n\t\t\t\t\tcursor: pointer;\n\t\t\t\t\ttransition: all 0.2s ease;\n\t\t\t\t\tbackground: white;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tmin-height: 120px;\n\t\t\t\t}\n\n\t\t\t\t.item-wrapper:hover {\n\t\t\t\t\tborder-color: var(--primary-color, #2563eb);\n\t\t\t\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\t\t\t\t\ttransform: translateY(-1px);\n\t\t\t\t}\n\n\t\t\t\t.item-qty-pill {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tz-index: 1;\n\t\t\t\t}\n\n\t\t\t\t.item-qty-pill .indicator-pill {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 4px;\n\t\t\t\t\tright: 4px;\n\t\t\t\t\tpadding: 2px 6px;\n\t\t\t\t\tborder-radius: 12px;\n\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: white;\n\t\t\t\t\tmin-width: 16px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t\t.item-detail {\n\t\t\t\t\tpadding: 8px;\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t}\n\n\t\t\t\t.item-name {\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tline-height: 1.3;\n\t\t\t\t\tmargin-bottom: 4px;\n\t\t\t\t\tcolor: var(--text-color, #374151);\n\t\t\t\t}\n\n\t\t\t\t.item-rate {\n\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\tcolor: var(--text-muted, #6b7280);\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t}\n\n\t\t\t\t.item-display.abbr {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\theight: 80px;\n\t\t\t\t\tfont-size: 24px;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tcolor: var(--text-muted, #6b7280);\n\t\t\t\t\tbackground: var(--bg-light, #f9fafb);\n\t\t\t\t\tborder-bottom: 1px solid var(--border-color, #e5e7eb);\n\t\t\t\t}\n\n\t\t\t\t/* Mobile First - up to 480px */\n\t\t\t\t@media (max-width: 480px) {\n\t\t\t\t\t.pos-responsive .filter-section {\n\t\t\t\t\t\tgrid-template-columns: 1fr;\n\t\t\t\t\t\tgap: var(--margin-xs, 4px);\n\t\t\t\t\t\tpadding: var(--margin-xs, 4px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.pos-responsive .filter-section .label {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t\tpadding: 8px 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.items-grid-responsive {\n\t\t\t\t\t\tgrid-template-columns: repeat(2, 1fr);\n\t\t\t\t\t\tgap: var(--margin-xs, 4px);\n\t\t\t\t\t\tpadding: var(--margin-xs, 4px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tmin-height: 100px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-name {\n\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-rate {\n\t\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-display.abbr {\n\t\t\t\t\t\theight: 60px;\n\t\t\t\t\t\tfont-size: 18px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-detail {\n\t\t\t\t\t\tpadding: 6px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Mobile Large - 481px to 768px */\n\t\t\t\t@media (min-width: 481px) and (max-width: 768px) {\n\t\t\t\t\t.pos-responsive .filter-section {\n\t\t\t\t\t\tgrid-template-columns: 1fr 2fr 1fr;\n\t\t\t\t\t\talign-items: end;\n\t\t\t\t\t\tgap: var(--margin-sm, 8px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.items-grid-responsive {\n\t\t\t\t\t\tgrid-template-columns: repeat(4, 1fr);\n\t\t\t\t\t\tgap: var(--margin-sm, 8px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tmin-height: 110px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Tablet - 769px to 1024px */\n\t\t\t\t@media (min-width: 769px) and (max-width: 1024px) {\n\t\t\t\t\t.pos-responsive .filter-section {\n\t\t\t\t\t\tgrid-template-columns: auto 1fr auto;\n\t\t\t\t\t\talign-items: end;\n\t\t\t\t\t\tgap: var(--margin-md, 12px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.items-grid-responsive {\n\t\t\t\t\t\tgrid-template-columns: repeat(4, 1fr);\n\t\t\t\t\t\tgap: var(--margin-md, 12px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tmin-height: 130px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Desktop Small - 1025px to 1440px */\n\t\t\t\t@media (min-width: 1025px) and (max-width: 1440px) {\n\t\t\t\t\t.pos-responsive .filter-section {\n\t\t\t\t\t\tgrid-template-columns: auto 1fr auto;\n\t\t\t\t\t\talign-items: end;\n\t\t\t\t\t\tgap: var(--margin-md, 12px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.items-grid-responsive {\n\t\t\t\t\t\tgrid-template-columns: repeat(5, 1fr);\n\t\t\t\t\t\tgap: var(--margin-md, 12px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tmin-height: 140px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Desktop Large - above 1440px */\n\t\t\t\t@media (min-width: 1441px) {\n\t\t\t\t\t.pos-responsive .filter-section {\n\t\t\t\t\t\tgrid-template-columns: auto 1fr auto;\n\t\t\t\t\t\talign-items: end;\n\t\t\t\t\t\tgap: var(--margin-lg, 16px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.items-grid-responsive {\n\t\t\t\t\t\tgrid-template-columns: repeat(6, 1fr);\n\t\t\t\t\t\tgap: var(--margin-lg, 16px);\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tmin-height: 150px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Landscape mode adjustments for mobile */\n\t\t\t\t@media (max-width: 768px) and (orientation: landscape) {\n\t\t\t\t\t.items-grid-responsive {\n\t\t\t\t\t\tgrid-template-columns: repeat(4, 1fr);\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tmin-height: 90px;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-display.abbr {\n\t\t\t\t\t\theight: 50px;\n\t\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* High DPI displays */\n\t\t\t\t@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {\n\t\t\t\t\t.item-wrapper {\n\t\t\t\t\t\tborder-width: 0.5px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t/* Minimized state responsive */\n\t\t\t\t.pos-responsive.minimized .filter-section {\n\t\t\t\t\tgrid-template-columns: 1fr !important;\n\t\t\t\t}\n\n\t\t\t\t.pos-responsive.minimized .items-grid-responsive {\n\t\t\t\t\tgrid-template-columns: 1fr !important;\n\t\t\t\t}\n\n\t\t\t\t.pos-responsive.minimized .item-wrapper {\n\t\t\t\t\tflex-direction: row;\n\t\t\t\t\tmin-height: 60px;\n\t\t\t\t\talign-items: center;\n\t\t\t\t}\n\n\t\t\t\t.pos-responsive.minimized .item-detail {\n\t\t\t\t\tflex-direction: row;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t}\n\n\t\t\t\t.pos-responsive.minimized .item-display.abbr {\n\t\t\t\t\theight: 40px;\n\t\t\t\t\twidth: 40px;\n\t\t\t\t\tmin-width: 40px;\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t\tmargin-right: 8px;\n\t\t\t\t}\n\t\t\t`;\n\t\t\tdocument.head.appendChild(style);\n\t\t}\n\n\t\t// Setup resize observer for dynamic adjustments\n\t\tthis.setup_resize_observer();\n\t}\n\n\tsetup_resize_observer() {\n\t\tif (window.ResizeObserver) {\n\t\t\tthis.resizeObserver = new ResizeObserver((entries) => {\n\t\t\t\tfor (let entry of entries) {\n\t\t\t\t\tthis.handle_container_resize(entry.contentRect);\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\tthis.resizeObserver.observe(this.$component[0]);\n\t\t} else {\n\t\t\t// Fallback for browsers without ResizeObserver\n\t\t\t$(window).on('resize.pos-selector', frappe.utils.debounce(() => {\n\t\t\t\tthis.handle_window_resize();\n\t\t\t}, 250));\n\t\t}\n\t}\n\n\thandle_container_resize(rect) {\n\t\tconst width = rect.width;\n\t\t\n\t\t// Adjust grid columns based on container width\n\t\tif (width < 300) {\n\t\t\tthis.$items_container.css('grid-template-columns', 'repeat(1, 1fr)');\n\t\t} else if (width < 500) {\n\t\t\tthis.$items_container.css('grid-template-columns', 'repeat(2, 1fr)');\n\t\t} else if (width < 700) {\n\t\t\tthis.$items_container.css('grid-template-columns', 'repeat(3, 1fr)');\n\t\t} else if (width < 900) {\n\t\t\tthis.$items_container.css('grid-template-columns', 'repeat(4, 1fr)');\n\t\t} else if (width < 1200) {\n\t\t\tthis.$items_container.css('grid-template-columns', 'repeat(5, 1fr)');\n\t\t}\n\t\t// Let CSS handle larger screens\n\t}\n\n\thandle_window_resize() {\n\t\t// Force layout recalculation\n\t\tthis.$component.hide().show();\n\t}\n\n\tasync load_items_data() {\n\t\tif (!this.item_group) {\n\t\t\tfrappe.call({\n\t\t\t\tmethod: \"erpnext.selling.page.point_of_sale.point_of_sale.get_parent_item_group\",\n\t\t\t\tasync: false,\n\t\t\t\tcallback: (r) => {\n\t\t\t\t\tif (r.message) this.parent_item_group = r.message;\n\t\t\t\t},\n\t\t\t});\n\t\t}\n\t\tif (!this.price_list) {\n\t\t\tconst res = await frappe.db.get_value(\"POS Profile\", this.pos_profile, \"selling_price_list\");\n\t\t\tthis.price_list = res.message.selling_price_list;\n\t\t}\n\n\t\tthis.get_items({}).then(({ message }) => {\n\t\t\tthis.render_item_list(message.items);\n\t\t});\n\t}\n\n\tget_items({ start = 0, page_length = 40, search_term = \"\" }) {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tconst price_list = (doc && doc.selling_price_list) || this.price_list;\n\t\tlet { item_group, pos_profile } = this;\n\n\t\t!item_group && (item_group = this.parent_item_group);\n\n\t\treturn frappe.call({\n\t\t\tmethod: \"erpnext.selling.page.point_of_sale.point_of_sale.get_items\",\n\t\t\tfreeze: true,\n\t\t\targs: { start, page_length, price_list, item_group, search_term, pos_profile },\n\t\t});\n\t}\n\n\trender_item_list(items) {\n\t\tthis.$items_container.html(\"\");\n\n\t\titems.forEach((item) => {\n\t\t\tconst item_html = this.get_item_html(item);\n\t\t\tthis.$items_container.append(item_html);\n\t\t});\n\t}\n\n\tget_item_html(item) {\n\t\tconst me = this;\n\t\t// eslint-disable-next-line no-unused-vars\n\t\tconst { item_image, serial_no, batch_no, barcode, actual_qty, uom, price_list_rate } = item;\n\t\tconst precision = flt(price_list_rate, 2) % 1 != 0 ? 2 : 0;\n\t\tlet indicator_color;\n\t\tlet qty_to_display = actual_qty;\n\n\t\tif (item.is_stock_item) {\n\t\t\tindicator_color = actual_qty > 10 ? \"green\" : actual_qty <= 0 ? \"red\" : \"orange\";\n\n\t\t\tif (Math.round(qty_to_display) > 999) {\n\t\t\t\tqty_to_display = Math.round(qty_to_display) / 1000;\n\t\t\t\tqty_to_display = qty_to_display.toFixed(1) + \"K\";\n\t\t\t}\n\t\t} else {\n\t\t\tindicator_color = \"\";\n\t\t\tqty_to_display = \"\";\n\t\t}\n\n\t\tfunction get_item_image_html() {\n\t\t\tif (!me.hide_images && item_image) {\n\t\t\t\treturn `<div class=\"item-qty-pill\">\n\t\t\t\t\t\t\t<span class=\"indicator-pill whitespace-nowrap ${indicator_color}\" style=\"color: black;\">${qty_to_display}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"flex items-center justify-center border-b-grey text-6xl text-grey-100\" style=\"height:80px; min-height:80px; position: relative;\">\n\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\tonerror=\"cur_pos.item_selector.handle_broken_image(this)\"\n\t\t\t\t\t\t\t\tclass=\"h-full w-full object-cover item-img\" src=\"${item_image}\"\n\t\t\t\t\t\t\t\talt=\"${frappe.get_abbr(item.item_name)}\"\n\t\t\t\t\t\t\t\tstyle=\"max-height: 100%; max-width: 100%;\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</div>`;\n\t\t\t} else {\n\t\t\t\treturn `<div class=\"item-qty-pill\">\n\t\t\t\t\t\t\t<span class=\"indicator-pill whitespace-nowrap ${indicator_color}\" style=\"color: black;\">${qty_to_display}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"item-display abbr\">${frappe.get_abbr(item.item_name)}</div>`;\n\t\t\t}\n\t\t}\n\n\n\t\treturn `<div class=\"item-wrapper\"\n\t\t\t\tdata-item-code=\"${escape(item.item_code)}\" data-serial-no=\"${escape(serial_no)}\"\n\t\t\t\tdata-batch-no=\"${escape(batch_no)}\" data-uom=\"${escape(uom)}\"\n\t\t\t\tdata-rate=\"${escape(price_list_rate || 0)}\"\n\t\t\t\tdata-stock-uom=\"${escape(item.stock_uom)}\"\n\t\t\t\ttitle=\"${item.item_name}\">\n\n\t\t\t\t${get_item_image_html()}\n\n\t\t\t\t<div class=\"item-detail\">\n\t\t\t\t\t<div class=\"item-name\">\n\t\t\t\t\t\t${frappe.ellipsis(item.item_name, 18)}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"item-rate\">${format_currency(price_list_rate, item.currency, precision) || 0} / ${uom}</div>\n\t\t\t\t</div>\n\t\t\t</div>`;\n\t}\n\n\thandle_broken_image($img) {\n\t\tconst item_abbr = $($img).attr(\"alt\");\n\t\t$($img).parent().replaceWith(`<div class=\"item-display abbr\">${item_abbr}</div>`);\n\t}\n\n\tmake_search_bar() {\n\t\tconst me = this;\n\t\tthis.$component.find(\".search-field\").html(\"\");\n\t\tthis.$component.find(\".item-group-field\").html(\"\");\n\n\t\tthis.search_field = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Search\"),\n\t\t\t\tfieldtype: \"Data\",\n\t\t\t\tplaceholder: __(\"Search by item code, serial number or barcode\"),\n\t\t\t},\n\t\t\tparent: this.$component.find(\".search-field\"),\n\t\t\trender_input: true,\n\t\t});\n\t\tthis.item_group_field = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Item Group\"),\n\t\t\t\tfieldtype: \"Link\",\n\t\t\t\toptions: \"Item Group\",\n\t\t\t\tplaceholder: __(\"Select item group\"),\n\t\t\t\tonchange: function () {\n\t\t\t\t\tme.item_group = this.value;\n\t\t\t\t\t!me.item_group && (me.item_group = me.parent_item_group);\n\t\t\t\t\tme.filter_items();\n\t\t\t\t},\n\t\t\t\tget_query: function () {\n\t\t\t\t\tconst doc = me.events.get_frm().doc;\n\t\t\t\t\treturn {\n\t\t\t\t\t\tquery: \"erpnext.selling.page.point_of_sale.point_of_sale.item_group_query\",\n\t\t\t\t\t\tfilters: {\n\t\t\t\t\t\t\tpos_profile: doc ? doc.pos_profile : \"\",\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t},\n\t\t\t},\n\t\t\tparent: this.$component.find(\".item-group-field\"),\n\t\t\trender_input: true,\n\t\t});\n\t\tthis.search_field.toggle_label(false);\n\t\tthis.item_group_field.toggle_label(false);\n\n\t\tthis.attach_clear_btn();\n\t}\n\n\tattach_clear_btn() {\n\t\tthis.search_field.$wrapper.find(\".control-input\").append(\n\t\t\t`<span class=\"link-btn\" style=\"top: 2px;\">\n\t\t\t\t<a class=\"btn-open no-decoration\" title=\"${__(\"Clear\")}\">\n\t\t\t\t\t${frappe.utils.icon(\"close\", \"sm\")}\n\t\t\t\t</a>\n\t\t\t</span>`\n\t\t);\n\n\t\tthis.$clear_search_btn = this.search_field.$wrapper.find(\".link-btn\");\n\n\t\tthis.$clear_search_btn.on(\"click\", \"a\", () => {\n\t\t\tthis.set_search_value(\"\");\n\t\t\tthis.search_field.set_focus();\n\t\t});\n\t}\n\n\tset_search_value(value) {\n\t\t$(this.search_field.$input[0]).val(value).trigger(\"input\");\n\t}\n\n\tbind_events() {\n\t\tconst me = this;\n\t\twindow.onScan = onScan;\n\n\t\tonScan.decodeKeyEvent = function (oEvent) {\n\t\t\tvar iCode = this._getNormalizedKeyNum(oEvent);\n\t\t\tswitch (true) {\n\t\t\t\tcase iCode >= 48 && iCode <= 90: // numbers and letters\n\t\t\t\tcase iCode >= 106 && iCode <= 111: // operations on numeric keypad (+, -, etc.)\n\t\t\t\tcase (iCode >= 160 && iCode <= 164) || iCode == 170: // ^ ! # $ *\n\t\t\t\tcase iCode >= 186 && iCode <= 194: // (; = , - . / `)\n\t\t\t\tcase iCode >= 219 && iCode <= 222: // ([ \\ ] ')\n\t\t\t\tcase iCode == 32: // spacebar\n\t\t\t\t\tif (oEvent.key !== undefined && oEvent.key !== \"\") {\n\t\t\t\t\t\treturn oEvent.key;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar sDecoded = String.fromCharCode(iCode);\n\t\t\t\t\tswitch (oEvent.shiftKey) {\n\t\t\t\t\t\tcase false:\n\t\t\t\t\t\t\tsDecoded = sDecoded.toLowerCase();\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase true:\n\t\t\t\t\t\t\tsDecoded = sDecoded.toUpperCase();\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\treturn sDecoded;\n\t\t\t\tcase iCode >= 96 && iCode <= 105: // numbers on numeric keypad\n\t\t\t\t\treturn 0 + (iCode - 96);\n\t\t\t}\n\t\t\treturn \"\";\n\t\t};\n\n\t\tonScan.attachTo(document, {\n\t\t\tonScan: (sScancode) => {\n\t\t\t\tif (this.search_field && this.$component.is(\":visible\")) {\n\t\t\t\t\tthis.search_field.set_focus();\n\t\t\t\t\tthis.set_search_value(sScancode);\n\t\t\t\t\tthis.barcode_scanned = true;\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\n\t\tthis.$component.on(\"click\", \".item-wrapper\", function () {\n\t\t\tconst $item = $(this);\n\t\t\tconst item_code = unescape($item.attr(\"data-item-code\"));\n\t\t\tlet batch_no = unescape($item.attr(\"data-batch-no\"));\n\t\t\tlet serial_no = unescape($item.attr(\"data-serial-no\"));\n\t\t\tlet uom = unescape($item.attr(\"data-uom\"));\n\t\t\tlet rate = unescape($item.attr(\"data-rate\"));\n\t\t\tlet stock_uom = unescape($item.attr(\"data-stock-uom\"));\n\n\t\t\t// escape(undefined) returns \"undefined\" then unescape returns \"undefined\"\n\t\t\tbatch_no = batch_no === \"undefined\" ? undefined : batch_no;\n\t\t\tserial_no = serial_no === \"undefined\" ? undefined : serial_no;\n\t\t\tuom = uom === \"undefined\" ? undefined : uom;\n\t\t\trate = rate === \"undefined\" ? undefined : rate;\n\t\t\tstock_uom = stock_uom === \"undefined\" ? undefined : stock_uom;\n\n\t\t\tme.events.item_selected({\n\t\t\t\tfield: \"qty\",\n\t\t\t\tvalue: \"+1\",\n\t\t\t\titem: { item_code, batch_no, serial_no, uom, rate, stock_uom },\n\t\t\t});\n\t\t\tme.search_field.set_focus();\n\t\t});\n\n\t\tthis.search_field.$input.on(\"input\", (e) => {\n\t\t\tclearTimeout(this.last_search);\n\t\t\tthis.last_search = setTimeout(() => {\n\t\t\t\tconst search_term = e.target.value;\n\t\t\t\tthis.filter_items({ search_term });\n\t\t\t}, 300);\n\n\t\t\tthis.$clear_search_btn.toggle(Boolean(this.search_field.$input.val()));\n\t\t});\n\n\t\tthis.search_field.$input.on(\"focus\", () => {\n\t\t\tthis.$clear_search_btn.toggle(Boolean(this.search_field.$input.val()));\n\t\t});\n\t}\n\n\tattach_shortcuts() {\n\t\tconst ctrl_label = frappe.utils.is_mac() ? \"⌘\" : \"Ctrl\";\n\t\tthis.search_field.parent.attr(\"title\", `${ctrl_label}+I`);\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"ctrl+i\",\n\t\t\taction: () => this.search_field.set_focus(),\n\t\t\tcondition: () => this.$component.is(\":visible\"),\n\t\t\tdescription: __(\"Focus on search input\"),\n\t\t\tignore_inputs: true,\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\t\tthis.item_group_field.parent.attr(\"title\", `${ctrl_label}+G`);\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"ctrl+g\",\n\t\t\taction: () => this.item_group_field.set_focus(),\n\t\t\tcondition: () => this.$component.is(\":visible\"),\n\t\t\tdescription: __(\"Focus on Item Group filter\"),\n\t\t\tignore_inputs: true,\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\n\t\t// for selecting the last filtered item on search\n\t\tfrappe.ui.keys.on(\"enter\", () => {\n\t\t\tconst selector_is_visible = this.$component.is(\":visible\");\n\t\t\tif (!selector_is_visible || this.search_field.get_value() === \"\") return;\n\n\t\t\tif (this.items.length == 1) {\n\t\t\t\tthis.$items_container.find(\".item-wrapper\").click();\n\t\t\t\tfrappe.utils.play_sound(\"submit\");\n\t\t\t\tthis.set_search_value(\"\");\n\t\t\t} else if (this.items.length == 0 && this.barcode_scanned) {\n\t\t\t\t// only show alert of barcode is scanned and enter is pressed\n\t\t\t\tfrappe.show_alert({\n\t\t\t\t\tmessage: __(\"No items found. Scan barcode again.\"),\n\t\t\t\t\tindicator: \"orange\",\n\t\t\t\t});\n\t\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\t\tthis.barcode_scanned = false;\n\t\t\t\tthis.set_search_value(\"\");\n\t\t\t}\n\t\t});\n\t}\n\n\tfilter_items({ search_term = \"\" } = {}) {\n\t\tconst selling_price_list = this.events.get_frm().doc.selling_price_list;\n\n\t\tif (search_term) {\n\t\t\tsearch_term = search_term.toLowerCase();\n\n\t\t\t// memoize\n\t\t\tthis.search_index = this.search_index || {};\n\t\t\tthis.search_index[selling_price_list] = this.search_index[selling_price_list] || {};\n\t\t\tif (this.search_index[selling_price_list][search_term]) {\n\t\t\t\tconst items = this.search_index[selling_price_list][search_term];\n\t\t\t\tthis.items = items;\n\t\t\t\tthis.render_item_list(items);\n\t\t\t\tthis.auto_add_item && this.items.length == 1 && this.add_filtered_item_to_cart();\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\tthis.get_items({ search_term }).then(({ message }) => {\n\t\t\t// eslint-disable-next-line no-unused-vars\n\t\t\tconst { items, serial_no, batch_no, barcode } = message;\n\t\t\tif (search_term && !barcode) {\n\t\t\t\tthis.search_index[selling_price_list][search_term] = items;\n\t\t\t}\n\t\t\tthis.items = items;\n\t\t\tthis.render_item_list(items);\n\t\t\tthis.auto_add_item && this.items.length == 1 && this.add_filtered_item_to_cart();\n\t\t});\n\t}\n\n\tadd_filtered_item_to_cart() {\n\t\tthis.$items_container.find(\".item-wrapper\").click();\n\t\tthis.set_search_value(\"\");\n\t}\n\n\tresize_selector(minimize) {\n\t\tthis.$component.toggleClass('minimized', minimize);\n\t\t\n\t\tif (minimize) {\n\t\t\tthis.$component\n\t\t\t\t.find(\".filter-section\")\n\t\t\t\t.css(\"grid-template-columns\", \"repeat(1, minmax(0, 1fr))\");\n\t\t\tthis.$component.find(\".search-field\").css(\"margin\", \"var(--margin-sm) 0px\");\n\t\t\tthis.$component.css(\"grid-column\", \"span 2 / span 2\");\n\t\t\tthis.$items_container.css(\"grid-template-columns\", \"repeat(1, minmax(0, 1fr))\");\n\t\t} else {\n\t\t\t// Reset to responsive defaults\n\t\t\tthis.$component\n\t\t\t\t.find(\".filter-section\")\n\t\t\t\t.css(\"grid-template-columns\", \"\");\n\t\t\tthis.$component.find(\".search-field\").css(\"margin\", \"\");\n\t\t\tthis.$component.css(\"grid-column\", \"\");\n\t\t\tthis.$items_container.css(\"grid-template-columns\", \"\");\n\t\t}\n\t}\n\n\ttoggle_component(show) {\n\t\tthis.set_search_value(\"\");\n\t\tthis.$component.css(\"display\", show ? \"flex\" : \"none\");\n\t}\n\n\tdestroy() {\n\t\tif (this.resizeObserver) {\n\t\t\tthis.resizeObserver.disconnect();\n\t\t}\n\t\t$(window).off('resize.pos-selector');\n\t}\n};", "erpnext.SalesInvoiceUI.ItemDetails = class {\n\tconstructor({ wrapper, events, settings }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\t\tthis.hide_images = settings.hide_images;\n\t\tthis.allow_rate_change = settings.allow_rate_change;\n\t\tthis.allow_discount_change = settings.allow_discount_change;\n\t\tthis.current_item = {};\n\n\t\tthis.init_component();\n\t}\n\n\tinit_component() {\n\t\tthis.prepare_dom();\n\t\tthis.init_child_components();\n\t\tthis.bind_events();\n\t\tthis.attach_shortcuts();\n\t}\n\n\tprepare_dom() {\n\t\tthis.wrapper.append(`<section class=\"item-details-container\"></section>`);\n\n\t\tthis.$component = this.wrapper.find(\".item-details-container\");\n\t}\n\n\tinit_child_components() {\n\t\tthis.$component.html(\n\t\t\t`<div class=\"item-details-header\">\n\t\t\t\t<div class=\"label\">${__(\"Item Details\")}</div>\n\t\t\t\t<div class=\"close-btn\">\n\t\t\t\t\t<svg width=\"32\" height=\"32\" viewBox=\"0 0 14 14\" fill=\"none\">\n\t\t\t\t\t\t<path d=\"M4.93764 4.93759L7.00003 6.99998M9.06243 9.06238L7.00003 6.99998M7.00003 6.99998L4.93764 9.06238L9.06243 4.93759\" stroke=\"#8D99A6\"/>\n\t\t\t\t\t</svg>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class=\"item-display\">\n\t\t\t\t<div class=\"item-name-desc-price\">\n\t\t\t\t\t// <div class=\"item-name\"></div>\n\t\t\t\t\t<div class=\"item-desc\"></div>\n\t\t\t\t\t<div class=\"item-price\"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"item-image\"></div>\n\t\t\t</div>\n\t\t\t<div class=\"discount-section\"></div>\n\t\t\t<div class=\"form-container\"></div>\n\t\t\t<div class=\"serial-batch-container\"></div>`\n\t\t);\n\n\t\tthis.$item_name = this.$component.find(\".item-name\");\n\t\tthis.$item_description = this.$component.find(\".item-desc\");\n\t\tthis.$item_price = this.$component.find(\".item-price\");\n\t\tthis.$item_image = this.$component.find(\".item-image\");\n\t\tthis.$form_container = this.$component.find(\".form-container\");\n\t\tthis.$dicount_section = this.$component.find(\".discount-section\");\n\t\tthis.$serial_batch_container = this.$component.find(\".serial-batch-container\");\n\t}\n\n\tcompare_with_current_item(item) {\n\t\t// returns true if `item` is currently being edited\n\t\treturn item && item.name == this.current_item.name;\n\t}\n\n\tasync toggle_item_details_section(item) {\n\t\tconst current_item_changed = !this.compare_with_current_item(item);\n\n\t\t// if item is null or highlighted cart item is clicked twice\n\t\tconst hide_item_details = !Boolean(item) || !current_item_changed;\n\n\t\tif ((!hide_item_details && current_item_changed) || hide_item_details) {\n\t\t\t// if item details is being closed OR if item details is opened but item is changed\n\t\t\t// in both cases, if the current item is a serialized item, then validate and remove the item\n\t\t\tawait this.validate_serial_batch_item();\n\t\t}\n\n\t\tthis.events.toggle_item_selector(!hide_item_details);\n\t\tthis.toggle_component(!hide_item_details);\n\n\t\tif (item && current_item_changed) {\n\t\t\tthis.doctype = item.doctype;\n\t\t\tthis.item_meta = frappe.get_meta(this.doctype);\n\t\t\tthis.name = item.name;\n\t\t\tthis.item_row = item;\n\t\t\tthis.currency = this.events.get_frm().doc.currency;\n\n\t\t\tthis.current_item = item;\n\n\t\t\tthis.render_dom(item);\n\t\t\tthis.render_discount_dom(item);\n\t\t\tthis.render_form(item);\n\t\t\tthis.events.highlight_cart_item(item);\n\t\t} else {\n\t\t\tthis.current_item = {};\n\t\t}\n\t}\n\n\tvalidate_serial_batch_item() {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tconst item_row = doc.items.find((item) => item.name === this.name);\n\n\t\tif (!item_row) return;\n\n\t\tconst serialized = item_row.has_serial_no;\n\t\tconst batched = item_row.has_batch_no;\n\t\tconst no_bundle_selected =\n\t\t\t!item_row.serial_and_batch_bundle && !item_row.serial_no && !item_row.batch_no;\n\n\t\tif ((serialized && no_bundle_selected) || (batched && no_bundle_selected)) {\n\t\t\tfrappe.show_alert({\n\t\t\t\tmessage: __(\"Item is removed since no serial / batch no selected.\"),\n\t\t\t\tindicator: \"orange\",\n\t\t\t});\n\t\t\tfrappe.utils.play_sound(\"cancel\");\n\t\t\treturn this.events.remove_item_from_cart();\n\t\t}\n\t}\n\n\trender_dom(item) {\n\t\tlet { item_name, description, image, price_list_rate } = item;\n\n\t\tfunction get_description_html() {\n\t\t\tif (description) {\n\t\t\t\tdescription =\n\t\t\t\t\tdescription.indexOf(\"...\") === -1 && description.length > 140\n\t\t\t\t\t\t? description.substr(0, 139) + \"...\"\n\t\t\t\t\t\t: description;\n\t\t\t\treturn description;\n\t\t\t}\n\t\t\treturn ``;\n\t\t}\n\n\t\tthis.$item_name.html(item_name);\n\t\tthis.$item_description.html(get_description_html());\n\t\tthis.$item_price.html(format_currency(price_list_rate, this.currency));\n\t\tif (!this.hide_images && image) {\n\t\t\tthis.$item_image.html(\n\t\t\t\t`<img\n\t\t\t\t\tonerror=\"cur_pos.item_details.handle_broken_image(this)\"\n\t\t\t\t\tclass=\"h-full\" src=\"${image}\"\n\t\t\t\t\talt=\"${frappe.get_abbr(item_name)}\"\n\t\t\t\t\tstyle=\"object-fit: cover;\">`\n\t\t\t);\n\t\t} else {\n\t\t\tthis.$item_image.html(`<div class=\"item-abbr\">${frappe.get_abbr(item_name)}</div>`);\n\t\t}\n\t}\n\n\thandle_broken_image($img) {\n\t\tconst item_abbr = $($img).attr(\"alt\");\n\t\t$($img).replaceWith(`<div class=\"item-abbr\">${item_abbr}</div>`);\n\t}\n\n\trender_discount_dom(item) {\n\t\tif (item.discount_percentage) {\n\t\t\tthis.$dicount_section.html(\n\t\t\t\t`<div class=\"item-rate\">${format_currency(item.price_list_rate, this.currency)}</div>\n\t\t\t\t<div class=\"item-discount\">${item.discount_percentage}% off</div>`\n\t\t\t);\n\t\t\tthis.$item_price.html(format_currency(item.rate, this.currency));\n\t\t} else {\n\t\t\tthis.$dicount_section.html(``);\n\t\t}\n\t}\n\n\trender_form(item) {\n\t\tconst fields_to_display = this.get_form_fields(item);\n\t\tthis.$form_container.html(\"\");\n\n\t\tfields_to_display.forEach((fieldname, idx) => {\n\t\t\tthis.$form_container.append(\n\t\t\t\t`<div class=\"${fieldname}-control\" data-fieldname=\"${fieldname}\"></div>`\n\t\t\t);\n\n\t\t\tconst field_meta = this.item_meta.fields.find((df) => df.fieldname === fieldname);\n\t\t\tfieldname === \"discount_percentage\" ? (field_meta.label = __(\"Discount (%)\")) : \"\";\n\t\t\tconst me = this;\n\n\t\t\tthis[`${fieldname}_control`] = frappe.ui.form.make_control({\n\t\t\t\tdf: {\n\t\t\t\t\t...field_meta,\n\t\t\t\t\tonchange: function () {\n\t\t\t\t\t\tme.events.form_updated(me.current_item, fieldname, this.value);\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tparent: this.$form_container.find(`.${fieldname}-control`),\n\t\t\t\trender_input: true,\n\t\t\t});\n\t\t\tthis[`${fieldname}_control`].set_value(item[fieldname]);\n\t\t});\n\n\t\tthis.resize_serial_control(item);\n\t\tthis.make_auto_serial_selection_btn(item);\n\n\t\tthis.bind_custom_control_change_event();\n\t}\n\n\tget_form_fields(item) {\n\t\tconst fields = [\n\t\t\t\"qty\",\n\t\t\t\"uom\",\n\t\t\t\"rate\",\n\t\t\t\"conversion_factor\",\n\t\t\t\"discount_percentage\",\n\t\t\t\"warehouse\",\n\t\t\t\"actual_qty\",\n\t\t\t\"price_list_rate\",\n\t\t];\n\t\tif (item.has_serial_no || item.serial_no) fields.push(\"serial_no\");\n\t\tif (item.has_batch_no || item.batch_no) fields.push(\"batch_no\");\n\t\treturn fields;\n\t}\n\n\tresize_serial_control(item) {\n\t\tif (item.has_serial_no || item.serial_no) {\n\t\t\tthis.$form_container.find(\".serial_no-control\").find(\"textarea\").css(\"height\", \"6rem\");\n\t\t}\n\t}\n\n\tmake_auto_serial_selection_btn(item) {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tif (!doc.is_return && (item.has_serial_no || item.serial_no)) {\n\t\t\tif (!item.has_batch_no) {\n\t\t\t\tthis.$form_container.append(`<div class=\"grid-filler no-select\"></div>`);\n\t\t\t}\n\t\t\tconst label = __(\"Auto Fetch Serial Numbers\");\n\t\t\tthis.$form_container.append(\n\t\t\t\t`<div class=\"btn btn-sm btn-secondary auto-fetch-btn\">${label}</div>`\n\t\t\t);\n\t\t\tthis.$form_container.find(\".serial_no-control\").find(\"textarea\").css(\"height\", \"6rem\");\n\t\t}\n\t}\n\n\tbind_custom_control_change_event() {\n\t\tconst me = this;\n\t\tif (this.rate_control) {\n\t\t\tthis.rate_control.df.onchange = function () {\n\t\t\t\tif (this.value || flt(this.value) === 0) {\n\t\t\t\t\tme.events.form_updated(me.current_item, \"rate\", this.value).then(() => {\n\t\t\t\t\t\tconst item_row = frappe.get_doc(me.doctype, me.name);\n\t\t\t\t\t\tconst doc = me.events.get_frm().doc;\n\t\t\t\t\t\tme.$item_price.html(format_currency(item_row.rate, doc.currency));\n\t\t\t\t\t\tme.render_discount_dom(item_row);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\t\t\tthis.rate_control.df.read_only = !this.allow_rate_change;\n\t\t\tthis.rate_control.refresh();\n\t\t}\n\n\t\tif (this.discount_percentage_control && !this.allow_discount_change) {\n\t\t\tthis.discount_percentage_control.df.read_only = 1;\n\t\t\tthis.discount_percentage_control.refresh();\n\t\t}\n\n\t\tif (this.warehouse_control) {\n\t\t\tthis.warehouse_control.df.reqd = 1;\n\t\t\tthis.warehouse_control.df.onchange = function () {\n\t\t\t\tif (this.value) {\n\t\t\t\t\tme.events.form_updated(me.current_item, \"warehouse\", this.value).then(() => {\n\t\t\t\t\t\tme.item_stock_map = me.events.get_item_stock_map();\n\t\t\t\t\t\tconst available_qty = me.item_stock_map[me.item_row.item_code][this.value][0];\n\t\t\t\t\t\tconst is_stock_item = Boolean(\n\t\t\t\t\t\t\tme.item_stock_map[me.item_row.item_code][this.value][1]\n\t\t\t\t\t\t);\n\t\t\t\t\t\tif (available_qty === undefined) {\n\t\t\t\t\t\t\tme.events.get_available_stock(me.item_row.item_code, this.value).then(() => {\n\t\t\t\t\t\t\t\t// item stock map is updated now reset warehouse\n\t\t\t\t\t\t\t\tme.warehouse_control.set_value(this.value);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (available_qty === 0 && is_stock_item) {\n\t\t\t\t\t\t\tme.warehouse_control.set_value(\"\");\n\t\t\t\t\t\t\tconst bold_item_code = me.item_row.item_code.bold();\n\t\t\t\t\t\t\tconst bold_warehouse = this.value.bold();\n\t\t\t\t\t\t\tfrappe.throw(\n\t\t\t\t\t\t\t\t__(\"Item Code: {0} is not available under warehouse {1}.\", [\n\t\t\t\t\t\t\t\t\tbold_item_code,\n\t\t\t\t\t\t\t\t\tbold_warehouse,\n\t\t\t\t\t\t\t\t])\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tme.actual_qty_control.set_value(available_qty);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\t\t\tthis.warehouse_control.df.get_query = () => {\n\t\t\t\treturn {\n\t\t\t\t\tfilters: { company: this.events.get_frm().doc.company, is_group: 0 },\n\t\t\t\t};\n\t\t\t};\n\t\t\tthis.warehouse_control.refresh();\n\t\t}\n\n\t\tif (this.serial_no_control) {\n\t\t\tthis.serial_no_control.df.reqd = 1;\n\t\t\tthis.serial_no_control.df.onchange = async function () {\n\t\t\t\t!me.current_item.batch_no && (await me.auto_update_batch_no());\n\t\t\t\tme.events.form_updated(me.current_item, \"serial_no\", this.value);\n\t\t\t};\n\t\t\tthis.serial_no_control.refresh();\n\t\t}\n\n\t\tif (this.batch_no_control) {\n\t\t\tthis.batch_no_control.df.reqd = 1;\n\t\t\tthis.batch_no_control.df.get_query = () => {\n\t\t\t\treturn {\n\t\t\t\t\tquery: \"erpnext.controllers.queries.get_batch_no\",\n\t\t\t\t\tfilters: {\n\t\t\t\t\t\titem_code: me.item_row.item_code,\n\t\t\t\t\t\twarehouse: me.item_row.warehouse,\n\t\t\t\t\t\tposting_date: me.events.get_frm().doc.posting_date,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t};\n\t\t\tthis.batch_no_control.refresh();\n\t\t}\n\n\t\tif (this.uom_control) {\n\t\t\tthis.uom_control.df.onchange = function () {\n\t\t\t\tme.events.form_updated(me.current_item, \"uom\", this.value);\n\n\t\t\t\tconst item_row = frappe.get_doc(me.doctype, me.name);\n\t\t\t\tme.conversion_factor_control.df.read_only = item_row.stock_uom == this.value;\n\t\t\t\tme.conversion_factor_control.refresh();\n\t\t\t};\n\t\t}\n\n\t\tfrappe.model.on(\"POS Invoice Item\", \"*\", (fieldname, value, item_row) => {\n\t\t\tconst field_control = this[`${fieldname}_control`];\n\t\t\tconst item_row_is_being_edited = this.compare_with_current_item(item_row);\n\t\t\tif (\n\t\t\t\titem_row_is_being_edited &&\n\t\t\t\tfield_control &&\n\t\t\t\tfield_control.get_value() !== value &&\n\t\t\t\tvalue == item_row[fieldname]\n\t\t\t) {\n\t\t\t\tfield_control.set_value(value);\n\t\t\t\tcur_pos.update_cart_html(item_row);\n\t\t\t}\n\t\t});\n\t}\n\n\tasync auto_update_batch_no() {\n\t\tif (this.serial_no_control && this.batch_no_control) {\n\t\t\tconst selected_serial_nos = this.serial_no_control\n\t\t\t\t.get_value()\n\t\t\t\t.split(`\\n`)\n\t\t\t\t.filter((s) => s);\n\t\t\tif (!selected_serial_nos.length) return;\n\n\t\t\t// find batch nos of the selected serial no\n\t\t\tconst serials_with_batch_no = await frappe.db.get_list(\"Serial No\", {\n\t\t\t\tfilters: { name: [\"in\", selected_serial_nos] },\n\t\t\t\tfields: [\"batch_no\", \"name\"],\n\t\t\t});\n\t\t\tconst batch_serial_map = serials_with_batch_no.reduce((acc, r) => {\n\t\t\t\tif (!acc[r.batch_no]) {\n\t\t\t\t\tacc[r.batch_no] = [];\n\t\t\t\t}\n\t\t\t\tacc[r.batch_no] = [...acc[r.batch_no], r.name];\n\t\t\t\treturn acc;\n\t\t\t}, {});\n\t\t\t// set current item's batch no and serial no\n\t\t\tconst batch_no = Object.keys(batch_serial_map)[0];\n\t\t\tconst batch_serial_nos = batch_serial_map[batch_no].join(`\\n`);\n\t\t\t// eg. 10 selected serial no. -> 5 belongs to first batch other 5 belongs to second batch\n\t\t\tconst serial_nos_belongs_to_other_batch =\n\t\t\t\tselected_serial_nos.length !== batch_serial_map[batch_no].length;\n\n\t\t\tconst current_batch_no = this.batch_no_control.get_value();\n\t\t\tcurrent_batch_no != batch_no && (await this.batch_no_control.set_value(batch_no));\n\n\t\t\tif (serial_nos_belongs_to_other_batch) {\n\t\t\t\tthis.serial_no_control.set_value(batch_serial_nos);\n\t\t\t\tthis.qty_control.set_value(batch_serial_map[batch_no].length);\n\n\t\t\t\tdelete batch_serial_map[batch_no];\n\t\t\t\tthis.events.clone_new_batch_item_in_frm(batch_serial_map, this.current_item);\n\t\t\t}\n\t\t}\n\t}\n\n\tbind_events() {\n\t\tthis.bind_auto_serial_fetch_event();\n\t\tthis.bind_fields_to_numpad_fields();\n\n\t\tthis.$component.on(\"click\", \".close-btn\", () => {\n\t\t\tthis.events.close_item_details();\n\t\t});\n\t}\n\n\tattach_shortcuts() {\n\t\tthis.wrapper.find(\".close-btn\").attr(\"title\", \"Esc\");\n\t\tfrappe.ui.keys.on(\"escape\", () => {\n\t\t\tconst item_details_visible = this.$component.is(\":visible\");\n\t\t\tif (item_details_visible) {\n\t\t\t\tthis.events.close_item_details();\n\t\t\t}\n\t\t});\n\t}\n\n\tbind_fields_to_numpad_fields() {\n\t\tconst me = this;\n\t\tthis.$form_container.on(\"click\", \".input-with-feedback\", function () {\n\t\t\tconst fieldname = $(this).attr(\"data-fieldname\");\n\t\t\tif (this.last_field_focused != fieldname) {\n\t\t\t\tme.events.item_field_focused(fieldname);\n\t\t\t\tthis.last_field_focused = fieldname;\n\t\t\t}\n\t\t});\n\t}\n\n\tbind_auto_serial_fetch_event() {\n\t\tthis.$form_container.on(\"click\", \".auto-fetch-btn\", () => {\n\t\t\tthis.batch_no_control && this.batch_no_control.set_value(\"\");\n\t\t\tlet qty = this.qty_control.get_value();\n\t\t\tlet conversion_factor = this.conversion_factor_control.get_value();\n\t\t\tlet expiry_date = this.item_row.has_batch_no ? this.events.get_frm().doc.posting_date : \"\";\n\n\t\t\tlet numbers = frappe.call({\n\t\t\t\tmethod: \"erpnext.stock.doctype.serial_no.serial_no.auto_fetch_serial_number\",\n\t\t\t\targs: {\n\t\t\t\t\tqty: qty * conversion_factor,\n\t\t\t\t\titem_code: this.current_item.item_code,\n\t\t\t\t\twarehouse: this.warehouse_control.get_value() || \"\",\n\t\t\t\t\tbatch_nos: this.current_item.batch_no || \"\",\n\t\t\t\t\tposting_date: expiry_date,\n\t\t\t\t\tfor_doctype: \"POS Invoice\",\n\t\t\t\t},\n\t\t\t});\n\n\t\t\tnumbers.then((data) => {\n\t\t\t\tlet auto_fetched_serial_numbers = data.message;\n\t\t\t\tlet records_length = auto_fetched_serial_numbers.length;\n\t\t\t\tif (!records_length) {\n\t\t\t\t\tconst warehouse = this.warehouse_control.get_value().bold();\n\t\t\t\t\tconst item_code = this.current_item.item_code.bold();\n\t\t\t\t\tfrappe.msgprint(\n\t\t\t\t\t\t__(\n\t\t\t\t\t\t\t\"Serial numbers unavailable for Item {0} under warehouse {1}. Please try changing warehouse.\",\n\t\t\t\t\t\t\t[item_code, warehouse]\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t\t\t} else if (records_length < qty) {\n\t\t\t\t\tfrappe.msgprint(__(\"Fetched only {0} available serial numbers.\", [records_length]));\n\t\t\t\t\tthis.qty_control.set_value(records_length);\n\t\t\t\t}\n\t\t\t\tnumbers = auto_fetched_serial_numbers.join(`\\n`);\n\t\t\t\tthis.serial_no_control.set_value(numbers);\n\t\t\t});\n\t\t});\n\t}\n\n\ttoggle_component(show) {\n\t\tshow ? this.$component.css(\"display\", \"flex\") : this.$component.css(\"display\", \"none\");\n\t}\n};\n", "erpnext.SalesInvoiceUI.NumberPad = class {\n\tconstructor({ wrapper, events, cols, keys, css_classes, fieldnames_map }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\t\tthis.cols = cols;\n\t\tthis.keys = keys;\n\t\tthis.css_classes = css_classes || [];\n\t\tthis.fieldnames = fieldnames_map || {};\n\n\t\tthis.init_component();\n\t}\n\n\tinit_component() {\n\t\tthis.prepare_dom();\n\t\tthis.bind_events();\n\t}\n\n\tprepare_dom() {\n\t\tconst { cols, keys, css_classes, fieldnames } = this;\n\n\t\tfunction get_keys() {\n\t\t\treturn keys.reduce((a, row, i) => {\n\t\t\t\treturn (\n\t\t\t\t\ta +\n\t\t\t\t\trow.reduce((a2, number, j) => {\n\t\t\t\t\t\tconst class_to_append = css_classes && css_classes[i] ? css_classes[i][j] : \"\";\n\t\t\t\t\t\tconst fieldname =\n\t\t\t\t\t\t\tfieldnames && fieldnames[number]\n\t\t\t\t\t\t\t\t? fieldnames[number]\n\t\t\t\t\t\t\t\t: typeof number === \"string\"\n\t\t\t\t\t\t\t\t? frappe.scrub(number)\n\t\t\t\t\t\t\t\t: number;\n\n\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\ta2 +\n\t\t\t\t\t\t\t`<div class=\"numpad-btn ${class_to_append}\" data-button-value=\"${fieldname}\">${__(\n\t\t\t\t\t\t\t\tnumber\n\t\t\t\t\t\t\t)}</div>`\n\t\t\t\t\t\t);\n\t\t\t\t\t}, \"\")\n\t\t\t\t);\n\t\t\t}, \"\");\n\t\t}\n\n\t\tthis.wrapper.html(\n\t\t\t`<div class=\"numpad-container\">\n\t\t\t\t${get_keys()}\n\t\t\t</div>`\n\t\t);\n\t}\n\n\tbind_events() {\n\t\tconst me = this;\n\t\tthis.wrapper.on(\"click\", \".numpad-btn\", function () {\n\t\t\tconst $btn = $(this);\n\t\t\tme.events.numpad_event($btn);\n\t\t});\n\t}\n};\n", "erpnext.SalesInvoiceUI.Payment = class {\n\tconstructor({ events, wrapper }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\n\t\tthis.init_component();\n\t}\n\n\tinit_component() {\n\t\tthis.prepare_dom();\n\t\tthis.initialize_numpad();\n\t\tthis.bind_events();\n\t\tthis.attach_shortcuts();\n\t}\n\n\tprepare_dom() {\n\t\tthis.wrapper.append(\n\t\t\t`<section class=\"payment-container\">\n\t\t\t\t<div class=\"section-label payment-section\">${__(\"Payment Method\")}</div>\n\t\t\t\t<div class=\"payment-modes\"></div>\n\t\t\t\t<div class=\"fields-numpad-container\">\n\t\t\t\t\t<div class=\"fields-section\">\n\t\t\t\t\t\t<div class=\"section-label\">${__(\"Additional Information\")}</div>\n\t\t\t\t\t\t<div class=\"invoice-fields\"></div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"number-pad\"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"totals-section\">\n\t\t\t\t\t<div class=\"totals\"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"submit-order-btn\">${__(\"Complete Order\")}</div>\n\t\t\t</section>`\n\t\t);\n\t\tthis.$component = this.wrapper.find(\".payment-container\");\n\t\tthis.$payment_modes = this.$component.find(\".payment-modes\");\n\t\tthis.$totals_section = this.$component.find(\".totals-section\");\n\t\tthis.$totals = this.$component.find(\".totals\");\n\t\tthis.$numpad = this.$component.find(\".number-pad\");\n\t\tthis.$invoice_fields_section = this.$component.find(\".fields-section\");\n\n\t\t// Add responsive CSS styles\n\t\tthis.add_responsive_styles();\n\t}\n\n\tadd_responsive_styles() {\n\t\t// Add responsive CSS for tablet view with enhanced touch support\n\t\tconst style = `\n\t\t\t<style>\n\t\t\t\t/* Enhanced touch support for all devices */\n\t\t\t\t.mode-of-payment {\n\t\t\t\t\ttouch-action: manipulation !important;\n\t\t\t\t\t-webkit-tap-highlight-color: rgba(0,0,0,0.1) !important;\n\t\t\t\t\tuser-select: none !important;\n\t\t\t\t\t-webkit-user-select: none !important;\n\t\t\t\t\t-moz-user-select: none !important;\n\t\t\t\t\t-ms-user-select: none !important;\n\t\t\t\t\tcursor: pointer !important;\n\t\t\t\t\tposition: relative !important;\n\t\t\t\t}\n\n\t\t\t\t/* Allow clicking on payment method container but prevent event bubbling from controls */\n\t\t\t\t.mode-of-payment-control,\n\t\t\t\t.payment-reference-fields,\n\t\t\t\t.cheque-reference-field,\n\t\t\t\t.cash-shortcuts {\n\t\t\t\t\tpointer-events: auto !important;\n\t\t\t\t}\n\n\t\t\t\t.mode-of-payment-control input,\n\t\t\t\t.payment-reference-fields input,\n\t\t\t\t.payment-reference-fields .btn,\n\t\t\t\t.cheque-reference-field input,\n\t\t\t\t.shortcut {\n\t\t\t\t\tpointer-events: auto !important;\n\t\t\t\t}\n\n\t\t\t\t.shortcut {\n\t\t\t\t\ttouch-action: manipulation !important;\n\t\t\t\t\t-webkit-tap-highlight-color: rgba(0,0,0,0.1) !important;\n\t\t\t\t\tuser-select: none !important;\n\t\t\t\t}\n\n\t\t\t\t/* Payment reference fields styling */\n\t\t\t\t.payment-reference-fields {\n\t\t\t\t\tmargin-top: 12px !important;\n\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\tgrid-template-columns: 1fr 1fr !important;\n\t\t\t\t\tgap: 12px !important;\n\t\t\t\t\twidth: 100% !important;\n\t\t\t\t}\n\n\t\t\t\t.mode-of-payment.border-primary .payment-reference-fields {\n\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t}\n\n\t\t\t\t.reference-field,\n\t\t\t\t.date-field {\n\t\t\t\t\twidth: 100% !important;\n\t\t\t\t}\n\n\t\t\t\t.reference-field .control-input,\n\t\t\t\t.date-field .control-input {\n\t\t\t\t\tpadding: 8px 12px !important;\n\t\t\t\t\tborder: 1px solid #d1d5db !important;\n\t\t\t\t\tborder-radius: 6px !important;\n\t\t\t\t\tfont-size: 14px !important;\n\t\t\t\t\twidth: 100% !important;\n\t\t\t\t}\n\n\t\t\t\t/* Tablet responsive styles */\n\t\t\t\t@media (max-width: 1024px) and (min-width: 768px) {\n\t\t\t\t\t.payment-modes {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\tgap: 12px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.payment-mode-wrapper {\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t\tmargin: 0 !important;\n\t\t\t\t\t\tpadding: 0 !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.mode-of-payment {\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\talign-items: stretch !important;\n\t\t\t\t\t\tpadding: 20px !important;\n\t\t\t\t\t\tmargin: 0 !important;\n\t\t\t\t\t\tborder: 2px solid #d1d5db !important;\n\t\t\t\t\t\tborder-radius: 8px !important;\n\t\t\t\t\t\tbackground: white !important;\n\t\t\t\t\t\tbox-sizing: border-box !important;\n\t\t\t\t\t\tmin-height: 60px !important;\n\t\t\t\t\t\ttransition: all 0.2s ease !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.mode-of-payment:hover,\n\t\t\t\t\t.mode-of-payment:active {\n\t\t\t\t\t\ttransform: translateY(-1px) !important;\n\t\t\t\t\t\tbox-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.mode-of-payment-header {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tjustify-content: space-between !important;\n\t\t\t\t\t\talign-items: center !important;\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.mode-of-payment.border-primary {\n\t\t\t\t\t\tborder-color: #3b82f6 !important;\n\t\t\t\t\t\tbackground-color: #eff6ff !important;\n\t\t\t\t\t\tbox-shadow: 0 0 0 1px #3b82f6 !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.mode-of-payment-control {\n\t\t\t\t\t\tmargin-top: 12px !important;\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t\tmax-width: none !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.cash-shortcuts {\n\t\t\t\t\t\tmargin-top: 12px !important;\n\t\t\t\t\t\tbackground: white !important;\n\t\t\t\t\t\tborder: 1px solid #d1d5db !important;\n\t\t\t\t\t\tborder-radius: 8px !important;\n\t\t\t\t\t\tpadding: 12px !important;\n\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t\tgrid-template-columns: repeat(3, 1fr) !important;\n\t\t\t\t\t\tgap: 8px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.mode-of-payment.border-primary .cash-shortcuts {\n\t\t\t\t\t\tdisplay: grid !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.shortcut {\n\t\t\t\t\t\tpadding: 12px 16px !important;\n\t\t\t\t\t\tbackground: #f3f4f6 !important;\n\t\t\t\t\t\tborder: 1px solid #d1d5db !important;\n\t\t\t\t\t\tborder-radius: 6px !important;\n\t\t\t\t\t\ttext-align: center !important;\n\t\t\t\t\t\tcursor: pointer !important;\n\t\t\t\t\t\tfont-size: 14px !important;\n\t\t\t\t\t\tmin-height: 44px !important;\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\talign-items: center !important;\n\t\t\t\t\t\tjustify-content: center !important;\n\t\t\t\t\t\ttransition: all 0.2s ease !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.shortcut:hover,\n\t\t\t\t\t.shortcut:active {\n\t\t\t\t\t\tbackground: #e5e7eb !important;\n\t\t\t\t\t\ttransform: scale(0.98) !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t/* Totals section responsive */\n\t\t\t\t\t.totals {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\tgap: 16px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.totals .col {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tjustify-content: space-between !important;\n\t\t\t\t\t\talign-items: center !important;\n\t\t\t\t\t\tpadding: 12px 16px !important;\n\t\t\t\t\t\tbackground: #f9fafb !important;\n\t\t\t\t\t\tborder-radius: 8px !important;\n\t\t\t\t\t\tborder: 1px solid #e5e7eb !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.totals .seperator-y {\n\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.total-label {\n\t\t\t\t\t\tfont-weight: 500 !important;\n\t\t\t\t\t\tcolor: #374151 !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.totals .value {\n\t\t\t\t\t\tfont-weight: 600 !important;\n\t\t\t\t\t\tcolor: #111827 !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t/* Fields and numpad container */\n\t\t\t\t\t.fields-numpad-container {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\tgap: 20px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.fields-section {\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.number-pad {\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t\tmax-width: 300px !important;\n\t\t\t\t\t\tmargin: 0 auto !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t/* Mobile responsive styles */\n\t\t\t\t@media (max-width: 767px) {\n\t\t\t\t\t.payment-modes {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\tgap: 12px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.payment-mode-wrapper {\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.mode-of-payment {\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\talign-items: stretch !important;\n\t\t\t\t\t\tpadding: 16px !important;\n\t\t\t\t\t\tmin-height: 60px !important;\n\t\t\t\t\t\tcursor: pointer !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.mode-of-payment-header {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tjustify-content: space-between !important;\n\t\t\t\t\t\talign-items: center !important;\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.mode-of-payment-control {\n\t\t\t\t\t\tmargin-top: 12px !important;\n\t\t\t\t\t\twidth: 100% !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.payment-reference-fields {\n\t\t\t\t\t\tgrid-template-columns: 1fr !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.totals {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\t\tgap: 12px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.totals .col {\n\t\t\t\t\t\tdisplay: flex !important;\n\t\t\t\t\t\tjustify-content: space-between !important;\n\t\t\t\t\t\tpadding: 8px 12px !important;\n\t\t\t\t\t\tbackground: #f8f9fa !important;\n\t\t\t\t\t\tborder-radius: 6px !important;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.totals .seperator-y {\n\t\t\t\t\t\tdisplay: none !important;\n\t\t\t\t\t}\n\n\t\t\t\t\t.shortcut {\n\t\t\t\t\t\tmin-height: 44px !important;\n\t\t\t\t\t\tpadding: 12px 8px !important;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t</style>\n\t\t`;\n\t\t\n\t\tif (!$('#pos-payment-responsive-styles').length) {\n\t\t\t$('head').append(style.replace('<style>', '<style id=\"pos-payment-responsive-styles\">'));\n\t\t}\n\t}\n\n\tmake_invoice_fields_control() {\n\t\tthis.reqd_invoice_fields = [];\n\t\tfrappe.db.get_doc(\"POS Settings\", undefined).then((doc) => {\n\t\t\tconst fields = doc.invoice_fields;\n\t\t\tif (!fields.length) return;\n\n\t\t\tthis.$invoice_fields = this.$invoice_fields_section.find(\".invoice-fields\");\n\t\t\tthis.$invoice_fields.html(\"\");\n\t\t\tconst frm = this.events.get_frm();\n\n\t\t\tfields.forEach((df) => {\n\t\t\t\tthis.$invoice_fields.append(\n\t\t\t\t\t`<div class=\"invoice_detail_field ${df.fieldname}-field\" data-fieldname=\"${df.fieldname}\"></div>`\n\t\t\t\t);\n\t\t\t\tlet df_events = {\n\t\t\t\t\tonchange: function () {\n\t\t\t\t\t\tfrm.set_value(this.df.fieldname, this.get_value());\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t\tif (df.fieldtype == \"Button\") {\n\t\t\t\t\tdf_events = {\n\t\t\t\t\t\tclick: function () {\n\t\t\t\t\t\t\tif (frm.script_manager.has_handlers(df.fieldname, frm.doc.doctype)) {\n\t\t\t\t\t\t\t\tfrm.script_manager.trigger(df.fieldname, frm.doc.doctype, frm.doc.docname);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (df.reqd && (df.fieldtype !== \"Button\" || !df.read_only)) {\n\t\t\t\t\tthis.reqd_invoice_fields.push({ fieldname: df.fieldname, label: df.label });\n\t\t\t\t}\n\n\t\t\t\tthis[`${df.fieldname}_field`] = frappe.ui.form.make_control({\n\t\t\t\t\tdf: {\n\t\t\t\t\t\t...df,\n\t\t\t\t\t\t...df_events,\n\t\t\t\t\t},\n\t\t\t\t\tparent: this.$invoice_fields.find(`.${df.fieldname}-field`),\n\t\t\t\t\trender_input: true,\n\t\t\t\t});\n\t\t\t\tthis[`${df.fieldname}_field`].set_value(frm.doc[df.fieldname]);\n\t\t\t});\n\t\t});\n\t}\n\n\tinitialize_numpad() {\n\t\tconst me = this;\n\t\tthis.number_pad = new erpnext.SalesInvoiceUI.NumberPad({\n\t\t\twrapper: this.$numpad,\n\t\t\tevents: {\n\t\t\t\tnumpad_event: function ($btn) {\n\t\t\t\t\tme.on_numpad_clicked($btn);\n\t\t\t\t},\n\t\t\t},\n\t\t\tcols: 3,\n\t\t\tkeys: [\n\t\t\t\t[1, 2, 3],\n\t\t\t\t[4, 5, 6],\n\t\t\t\t[7, 8, 9],\n\t\t\t\t[\".\", 0, \"Delete\"],\n\t\t\t],\n\t\t});\n\n\t\tthis.numpad_value = \"\";\n\t}\n\n\ton_numpad_clicked($btn) {\n\t\tconst button_value = $btn.attr(\"data-button-value\");\n\n\t\thighlight_numpad_btn($btn);\n\t\tthis.numpad_value =\n\t\t\tbutton_value === \"delete\" ? this.numpad_value.slice(0, -1) : this.numpad_value + button_value;\n\t\tthis.selected_mode.$input.get(0).focus();\n\t\tthis.selected_mode.set_value(this.numpad_value);\n\n\t\tfunction highlight_numpad_btn($btn) {\n\t\t\t$btn.addClass(\"shadow-base-inner bg-selected\");\n\t\t\tsetTimeout(() => {\n\t\t\t\t$btn.removeClass(\"shadow-base-inner bg-selected\");\n\t\t\t}, 100);\n\t\t}\n\t}\n\n\tbind_events() {\n\t\tconst me = this;\n\n\t\tthis.$payment_modes.on(\"click\", \".mode-of-payment\", function (e) {\n\t\t\tif ($(e.target).closest('.mode-of-payment-control, .payment-reference-fields, .cash-shortcuts').length) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst mode_clicked = $(this);\n\t\t\tconst scrollLeft =\n\t\t\t\tmode_clicked.offset().left - me.$payment_modes.offset().left + me.$payment_modes.scrollLeft();\n\t\t\tme.$payment_modes.animate({ scrollLeft });\n\n\t\t\tconst mode = mode_clicked.attr(\"data-mode\");\n\n\t\t\t$(`.mode-of-payment-control`).css(\"display\", \"none\");\n\t\t\t$(`.payment-reference-fields`).css(\"display\", \"none\");\n\t\t\t$(`.cash-shortcuts`).css(\"display\", \"none\");\n\t\t\tme.$payment_modes.find(`.pay-amount`).css(\"display\", \"inline\");\n\t\t\tme.$payment_modes.find(`.loyalty-amount-name`).css(\"display\", \"none\");\n\n\t\t\t$(\".mode-of-payment\").removeClass(\"border-primary\");\n\n\t\t\tif (mode_clicked.hasClass(\"border-primary\")) {\n\t\t\t\tmode_clicked.removeClass(\"border-primary\");\n\t\t\t\tme.selected_mode = \"\";\n\t\t\t} else {\n\t\t\t\tmode_clicked.addClass(\"border-primary\");\n\t\t\t\tmode_clicked.find(\".mode-of-payment-control\").css(\"display\", \"flex\");\n\t\t\t\tmode_clicked.find(\".payment-reference-fields\").css(\"display\", \"grid\");\n\t\t\t\tmode_clicked.find(\".cash-shortcuts\").css(\"display\", \"grid\");\n\t\t\t\tme.$payment_modes.find(`.${mode}-amount`).css(\"display\", \"none\");\n\t\t\t\tme.$payment_modes.find(`.${mode}-name`).css(\"display\", \"inline\");\n\n\t\t\t\tme.selected_mode = me[`${mode}_control`];\n\t\t\t\tme.selected_mode && me.selected_mode.$input.get(0).focus();\n\t\t\t\tme.auto_set_remaining_amount();\n\t\t\t}\n\t\t});\n\n\t\tfrappe.ui.form.on(\"Sales Invoice\", \"contact_mobile\", (frm) => {\n\t\t\tconst contact = frm.doc.contact_mobile;\n\t\t\tconst request_button = $(this.request_for_payment_field?.$input[0]);\n\t\t\tif (contact) {\n\t\t\t\trequest_button.removeClass(\"btn-default\").addClass(\"btn-primary\");\n\t\t\t} else {\n\t\t\t\trequest_button.removeClass(\"btn-primary\").addClass(\"btn-default\");\n\t\t\t}\n\t\t});\n\n\t\tfrappe.ui.form.on(\"Sales Invoice\", \"coupon_code\", (frm) => {\n\t\t\tif (frm.doc.coupon_code && !frm.applying_pos_coupon_code) {\n\t\t\t\tif (!frm.doc.ignore_pricing_rule) {\n\t\t\t\t\tfrm.applying_pos_coupon_code = true;\n\t\t\t\t\tfrappe.run_serially([\n\t\t\t\t\t\t() => (frm.doc.ignore_pricing_rule = 1),\n\t\t\t\t\t\t() => frm.trigger(\"ignore_pricing_rule\"),\n\t\t\t\t\t\t() => (frm.doc.ignore_pricing_rule = 0),\n\t\t\t\t\t\t() => frm.trigger(\"apply_pricing_rule\"),\n\t\t\t\t\t\t() => frm.save(),\n\t\t\t\t\t\t() => this.update_totals_section(frm.doc),\n\t\t\t\t\t\t() => (frm.applying_pos_coupon_code = false),\n\t\t\t\t\t]);\n\t\t\t\t} else if (frm.doc.ignore_pricing_rule) {\n\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\tmessage: __(\"Ignore Pricing Rule is enabled. Cannot apply coupon code.\"),\n\t\t\t\t\t\tindicator: \"orange\",\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tthis.setup_listener_for_payments();\n\n\t\tthis.$payment_modes.on(\"click\", \".shortcut\", function () {\n\t\t\tconst value = $(this).attr(\"data-value\");\n\t\t\tme.selected_mode.set_value(value);\n\t\t});\n\n\t\tthis.$component.on(\"click\", \".submit-order-btn\", () => {\n\t\t\tconst doc = this.events.get_frm().doc;\n\t\t\tconst paid_amount = doc.paid_amount;\n\t\t\tconst items = doc.items;\n\n\t\t\tif (!this.validate_reqd_invoice_fields()) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// For Sales Invoice, allow partial payments\n\t\t\tif (!items.length) {\n\t\t\t\tconst message = __(\"You cannot submit empty order.\");\n\t\t\t\tfrappe.show_alert({ message, indicator: \"orange\" });\n\t\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Allow submission even with partial payment for Sales Invoice\n\t\t\tif (paid_amount == 0 && doc.additional_discount_percentage != 100) {\n\t\t\t\tfrappe.confirm(\n\t\t\t\t\t__(\"No payment has been made. Do you want to submit this as an unpaid invoice?\"),\n\t\t\t\t\t() => {\n\t\t\t\t\t\tthis.events.submit_invoice();\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tthis.events.submit_invoice();\n\t\t\t}\n\t\t});\n\n\t\tfrappe.ui.form.on(\"Sales Invoice\", \"paid_amount\", (frm) => {\n\t\t\tthis.update_totals_section(frm.doc);\n\n\t\t\t// need to re calculate cash shortcuts after discount is applied\n\t\t\tconst is_cash_shortcuts_invisible = !this.$payment_modes.find(\".cash-shortcuts\").is(\":visible\");\n\t\t\t!is_cash_shortcuts_invisible &&\n\t\t\t\tthis.$payment_modes.find(\".cash-shortcuts\").css(\"display\", \"grid\");\n\t\t\tthis.render_payment_mode_dom();\n\t\t});\n\n\t\tfrappe.ui.form.on(\"Sales Invoice\", \"loyalty_amount\", (frm) => {\n\t\t\tconst formatted_currency = format_currency(frm.doc.loyalty_amount, frm.doc.currency);\n\t\t\tthis.$payment_modes.find(`.loyalty-amount-amount`).html(formatted_currency);\n\t\t});\n\n\t\tfrappe.ui.form.on(\"Sales Invoice Payment\", \"amount\", (frm, cdt, cdn) => {\n\t\t\t// for setting correct amount after loyalty points are redeemed\n\t\t\tconst default_mop = locals[cdt][cdn];\n\t\t\tconst mode = this.sanitize_mode_of_payment(default_mop.mode_of_payment);\n\t\t\tif (this[`${mode}_control`] && this[`${mode}_control`].get_value() != default_mop.amount) {\n\t\t\t\tthis[`${mode}_control`].set_value(default_mop.amount);\n\t\t\t}\n\t\t});\n\t}\n\n\tsetup_listener_for_payments() {\n\t\tfrappe.realtime.on(\"process_phone_payment\", (data) => {\n\t\t\tconst doc = this.events.get_frm().doc;\n\t\t\tconst { response, amount, success, failure_message } = data;\n\t\t\tlet message, title;\n\n\t\t\tif (success) {\n\t\t\t\ttitle = __(\"Payment Received\");\n\t\t\t\tconst grand_total = cint(frappe.sys_defaults.disable_rounded_total)\n\t\t\t\t\t? doc.grand_total\n\t\t\t\t\t: doc.rounded_total;\n\t\t\t\tif (amount >= grand_total) {\n\t\t\t\t\tfrappe.dom.unfreeze();\n\t\t\t\t\tmessage = __(\"Payment of {0} received successfully.\", [\n\t\t\t\t\t\tformat_currency(amount, doc.currency, 0),\n\t\t\t\t\t]);\n\t\t\t\t\tthis.events.submit_invoice();\n\t\t\t\t\tcur_frm.reload_doc();\n\t\t\t\t} else {\n\t\t\t\t\tmessage = __(\n\t\t\t\t\t\t\"Payment of {0} received successfully. Waiting for other requests to complete...\",\n\t\t\t\t\t\t[format_currency(amount, doc.currency, 0)]\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t} else if (failure_message) {\n\t\t\t\tmessage = failure_message;\n\t\t\t\ttitle = __(\"Payment Failed\");\n\t\t\t}\n\n\t\t\tfrappe.msgprint({ message: message, title: title });\n\t\t});\n\t}\n\n\tauto_set_remaining_amount() {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tconst grand_total = cint(frappe.sys_defaults.disable_rounded_total)\n\t\t\t? doc.grand_total\n\t\t\t: doc.rounded_total;\n\t\tconst remaining_amount = grand_total - doc.paid_amount;\n\t\tconst current_value = this.selected_mode ? this.selected_mode.get_value() : undefined;\n\t\tif (!current_value && remaining_amount > 0 && this.selected_mode) {\n\t\t\tthis.selected_mode.set_value(remaining_amount);\n\t\t}\n\t}\n\n\tattach_shortcuts() {\n\t\tconst ctrl_label = frappe.utils.is_mac() ? \"⌘\" : \"Ctrl\";\n\t\tthis.$component.find(\".submit-order-btn\").attr(\"title\", `${ctrl_label}+Enter`);\n\t\tfrappe.ui.keys.on(\"ctrl+enter\", () => {\n\t\t\tconst payment_is_visible = this.$component.is(\":visible\");\n\t\t\tconst active_mode = this.$payment_modes.find(\".border-primary\");\n\t\t\tif (payment_is_visible && active_mode.length) {\n\t\t\t\tthis.$component.find(\".submit-order-btn\").click();\n\t\t\t}\n\t\t});\n\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"tab\",\n\t\t\taction: () => {\n\t\t\t\tconst payment_is_visible = this.$component.is(\":visible\");\n\t\t\t\tlet active_mode = this.$payment_modes.find(\".border-primary\");\n\t\t\t\tactive_mode = active_mode.length ? active_mode.attr(\"data-mode\") : undefined;\n\n\t\t\t\tif (!active_mode) return;\n\n\t\t\t\tconst mode_of_payments = Array.from(this.$payment_modes.find(\".mode-of-payment\")).map((m) =>\n\t\t\t\t\t$(m).attr(\"data-mode\")\n\t\t\t\t);\n\t\t\t\tconst mode_index = mode_of_payments.indexOf(active_mode);\n\t\t\t\tconst next_mode_index = (mode_index + 1) % mode_of_payments.length;\n\t\t\t\tconst next_mode_to_be_clicked = this.$payment_modes.find(\n\t\t\t\t\t`.mode-of-payment[data-mode=\"${mode_of_payments[next_mode_index]}\"]`\n\t\t\t\t);\n\n\t\t\t\tif (payment_is_visible && mode_index != next_mode_index) {\n\t\t\t\t\tnext_mode_to_be_clicked.click();\n\t\t\t\t}\n\t\t\t},\n\t\t\tcondition: () =>\n\t\t\t\tthis.$component.is(\":visible\") && this.$payment_modes.find(\".border-primary\").length,\n\t\t\tdescription: __(\"Switch Between Payment Modes\"),\n\t\t\tignore_inputs: true,\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\t}\n\n\ttoggle_numpad() {\n\t\t// pass\n\t}\n\n\trender_payment_section() {\n\t\tthis.render_payment_mode_dom();\n\t\tthis.make_invoice_fields_control();\n\t\tthis.update_totals_section();\n\t\tthis.unset_grand_total_to_default_mop();\n\t}\n\n\tafter_render() {\n\t\tconst frm = this.events.get_frm();\n\t\tfrm.script_manager.trigger(\"after_payment_render\", frm.doc.doctype, frm.doc.docname);\n\t}\n\n\tedit_cart() {\n\t\tthis.events.toggle_other_sections(false);\n\t\tthis.toggle_component(false);\n\t}\n\n\tcheckout() {\n\t\tconst frm = this.events.get_frm();\n\t\tfrm.cscript.calculate_outstanding_amount();\n\t\tfrm.refresh_field(\"outstanding_amount\");\n\t\tfrm.refresh_field(\"paid_amount\");\n\t\tfrm.refresh_field(\"base_paid_amount\");\n\t\tthis.events.toggle_other_sections(true);\n\t\tthis.toggle_component(true);\n\n\t\tthis.render_payment_section();\n\t\tthis.after_render();\n\t}\n\n\ttoggle_remarks_control() {\n\t\tif (this.$remarks.find(\".frappe-control\").length) {\n\t\t\tthis.$remarks.html(\"+ Add Remark\");\n\t\t} else {\n\t\t\tthis.$remarks.html(\"\");\n\t\t\tthis[`remark_control`] = frappe.ui.form.make_control({\n\t\t\t\tdf: {\n\t\t\t\t\tlabel: __(\"Remark\"),\n\t\t\t\t\tfieldtype: \"Data\",\n\t\t\t\t\tonchange: function () {},\n\t\t\t\t},\n\t\t\t\tparent: this.$totals_section.find(`.remarks`),\n\t\t\t\trender_input: true,\n\t\t\t});\n\t\t\tthis[`remark_control`].set_value(\"\");\n\t\t}\n\t}\n\n\trender_payment_mode_dom() {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tconst payments = doc.payments;\n\t\tconst currency = doc.currency;\n\n\t\tthis.$payment_modes.html(\n\t\t\t`${payments\n\t\t\t\t.map((p, i) => {\n\t\t\t\t\tconst mode = this.sanitize_mode_of_payment(p.mode_of_payment);\n\t\t\t\t\tconst payment_type = p.type;\n\t\t\t\t\tconst amount = p.amount > 0 ? format_currency(p.amount, currency) : \"\";\n\n\t\t\t\t\treturn `\n\t\t\t\t\t<div class=\"payment-mode-wrapper\">\n\t\t\t\t\t\t<div class=\"mode-of-payment\" data-mode=\"${mode}\" data-payment-type=\"${payment_type}\">\n\t\t\t\t\t\t\t<div class=\"mode-of-payment-header\">\n\t\t\t\t\t\t\t\t<span class=\"payment-method-name\">${p.mode_of_payment}</span>\n\t\t\t\t\t\t\t\t<div class=\"${mode}-amount pay-amount\">${amount}</div>\n\t\t\t\t\t\t\t\t<div class=\"${mode} mode-of-payment-control\"></div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"payment-reference-fields\">\n\t\t\t\t\t\t\t\t<div class=\"reference-field\" data-mode=\"${mode}\"></div>\n\t\t\t\t\t\t\t\t<div class=\"date-field\" data-mode=\"${mode}\"></div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t`;\n\t\t\t\t})\n\t\t\t\t.join(\"\")}`\n\t\t);\n\n\t\tpayments.forEach((p) => {\n\t\t\tconst mode = this.sanitize_mode_of_payment(p.mode_of_payment);\n\t\t\tconst me = this;\n\t\t\t\n\t\t\t// Create amount control\n\t\t\tthis[`${mode}_control`] = frappe.ui.form.make_control({\n\t\t\t\tdf: {\n\t\t\t\t\tlabel: p.mode_of_payment,\n\t\t\t\t\tfieldtype: \"Currency\",\n\t\t\t\t\tplaceholder: __(\"Enter {0} amount.\", [p.mode_of_payment]),\n\t\t\t\t\tonchange: function () {\n\t\t\t\t\t\tconst current_value = frappe.model.get_value(p.doctype, p.name, \"amount\");\n\t\t\t\t\t\tif (current_value != this.value) {\n\t\t\t\t\t\t\tfrappe.model\n\t\t\t\t\t\t\t\t.set_value(p.doctype, p.name, \"amount\", flt(this.value))\n\t\t\t\t\t\t\t\t.then(() => me.update_totals_section());\n\n\t\t\t\t\t\t\tconst formatted_currency = format_currency(this.value, currency);\n\t\t\t\t\t\t\tme.$payment_modes.find(`.${mode}-amount`).html(formatted_currency);\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tparent: this.$payment_modes.find(`.${mode}.mode-of-payment-control`),\n\t\t\t\trender_input: true,\n\t\t\t});\n\t\t\tthis[`${mode}_control`].toggle_label(false);\n\t\t\tthis[`${mode}_control`].set_value(p.amount);\n\n\t\t\tif (p.type !== \"Cash\"){\n\n\t\t\tthis[`${mode}_reference_control`] = frappe.ui.form.make_control({\n\t\t\t\tdf: {\n\t\t\t\t\tlabel: __(\"Reference No\"),\n\t\t\t\t\tfieldtype: \"Data\",\n\t\t\t\t\tplaceholder: __(\"Reference No.\"),\n\t\t\t\t\tonchange: function () {\n\t\t\t\t\t\tfrappe.model.set_value(p.doctype, p.name, \"reference_no\", this.value);\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\tparent: this.$payment_modes.find(`.reference-field[data-mode=\"${mode}\"]`),\n\t\t\t\trender_input: true,\n\t\t\t});\n\t\t\tthis[`${mode}_reference_control`].toggle_label(false);\n\t\t\tthis[`${mode}_reference_control`].set_value(p.reference_no || \"\");\n\t\t\t}\n\n\t\t\t// Create date field - only show for non-cash payments\n\t\t\tif (p.type !== \"Cash\") {\n\t\t\t\tthis[`${mode}_date_control`] = frappe.ui.form.make_control({\n\t\t\t\t\tdf: {\n\t\t\t\t\t\tlabel: __(\"Reference Date\"),\n\t\t\t\t\t\tfieldtype: \"Date\",\n\t\t\t\t\t\tdefault: frappe.datetime.get_today(),\n\t\t\t\t\t\tonchange: function () {\n\t\t\t\t\t\t\tfrappe.model.set_value(p.doctype, p.name, \"reference_date\", this.value);\n\t\t\t\t\t\t\tfrappe.model.set_value(p.doctype, p.name, \"clearance_date\", this.value);\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\tparent: this.$payment_modes.find(`.date-field[data-mode=\"${mode}\"]`),\n\t\t\t\t\trender_input: true,\n\t\t\t\t});\n\t\t\t\tthis[`${mode}_date_control`].toggle_label(false);\n\t\t\t\tthis[`${mode}_date_control`].set_value(p.reference_date || frappe.datetime.get_today());\n\t\t\t} else {\n\t\t\t\t// Hide date field for cash payments\n\t\t\t\tthis.$payment_modes.find(`.date-field[data-mode=\"${mode}\"]`).hide();\n\t\t\t}\n\t\t});\n\n\t\tthis.render_loyalty_points_payment_mode();\n\t}\n\n\tfocus_on_default_mop() {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tconst payments = doc.payments;\n\t\tpayments.forEach((p) => {\n\t\t\tconst mode = this.sanitize_mode_of_payment(p.mode_of_payment);\n\t\t\tif (p.default) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.$payment_modes.find(`.mode-of-payment[data-mode=\"${mode}\"]`).click();\n\t\t\t\t}, 500);\n\t\t\t}\n\t\t});\n\t}\n\n\trender_loyalty_points_payment_mode() {\n\t\tconst me = this;\n\t\tconst doc = this.events.get_frm().doc;\n\t\tconst { loyalty_program, loyalty_points, conversion_factor } = this.events.get_customer_details();\n\n\t\tthis.$payment_modes.find(`.mode-of-payment[data-mode=\"loyalty-amount\"]`).parent().remove();\n\n\t\tif (!loyalty_program) return;\n\n\t\tlet description, read_only, max_redeemable_amount;\n\t\tif (!loyalty_points) {\n\t\t\tdescription = __(\"You don't have enough points to redeem.\");\n\t\t\tread_only = true;\n\t\t} else {\n\t\t\tmax_redeemable_amount = flt(\n\t\t\t\tflt(loyalty_points) * flt(conversion_factor),\n\t\t\t\tprecision(\"loyalty_amount\", doc)\n\t\t\t);\n\t\t\tdescription = __(\"You can redeem upto {0}.\", [format_currency(max_redeemable_amount)]);\n\t\t\tread_only = false;\n\t\t}\n\n\t\tconst margin = this.$payment_modes.children().length % 2 === 0 ? \"pr-2\" : \"pl-2\";\n\t\tconst amount = doc.loyalty_amount > 0 ? format_currency(doc.loyalty_amount, doc.currency) : \"\";\n\t\tthis.$payment_modes.append(\n\t\t\t`<div class=\"payment-mode-wrapper\">\n\t\t\t\t<div class=\"mode-of-payment loyalty-card\" data-mode=\"loyalty-amount\" data-payment-type=\"loyalty-amount\">\n\t\t\t\t\t<span class=\"payment-method-name\">Redeem Loyalty Points</span>\n\t\t\t\t\t<div class=\"loyalty-amount-amount pay-amount\">${amount}</div>\n\t\t\t\t\t<div class=\"loyalty-amount-name\">${loyalty_program}</div>\n\t\t\t\t\t<div class=\"loyalty-amount mode-of-payment-control\"></div>\n\t\t\t\t</div>\n\t\t\t</div>`\n\t\t);\n\n\t\tthis[\"loyalty-amount_control\"] = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Redeem Loyalty Points\"),\n\t\t\t\tfieldtype: \"Currency\",\n\t\t\t\tplaceholder: __(\"Enter amount to be redeemed.\"),\n\t\t\t\toptions: \"company:currency\",\n\t\t\t\tread_only,\n\t\t\t\tonchange: async function () {\n\t\t\t\t\tif (!loyalty_points) return;\n\n\t\t\t\t\tif (this.value > max_redeemable_amount) {\n\t\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\t\tmessage: __(\"You cannot redeem more than {0}.\", [\n\t\t\t\t\t\t\t\tformat_currency(max_redeemable_amount),\n\t\t\t\t\t\t\t]),\n\t\t\t\t\t\t\tindicator: \"red\",\n\t\t\t\t\t\t});\n\t\t\t\t\t\tfrappe.utils.play_sound(\"submit\");\n\t\t\t\t\t\tme[\"loyalty-amount_control\"].set_value(0);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tconst redeem_loyalty_points = this.value > 0 ? 1 : 0;\n\t\t\t\t\tawait frappe.model.set_value(\n\t\t\t\t\t\tdoc.doctype,\n\t\t\t\t\t\tdoc.name,\n\t\t\t\t\t\t\"redeem_loyalty_points\",\n\t\t\t\t\t\tredeem_loyalty_points\n\t\t\t\t\t);\n\t\t\t\t\tfrappe.model.set_value(\n\t\t\t\t\t\tdoc.doctype,\n\t\t\t\t\t\tdoc.name,\n\t\t\t\t\t\t\"loyalty_points\",\n\t\t\t\t\t\tparseInt(this.value / conversion_factor)\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\tdescription,\n\t\t\t},\n\t\t\tparent: this.$payment_modes.find(`.loyalty-amount.mode-of-payment-control`),\n\t\t\trender_input: true,\n\t\t});\n\t\tthis[\"loyalty-amount_control\"].toggle_label(false);\n\n\t}\n\n\trender_add_payment_method_dom() {\n\t\tconst docstatus = this.events.get_frm().doc.docstatus;\n\t\tif (docstatus === 0)\n\t\t\tthis.$payment_modes.append(\n\t\t\t\t`<div class=\"w-full pr-2\">\n\t\t\t\t\t<div class=\"add-mode-of-payment w-half text-grey mb-4 no-select pointer\">+ Add Payment Method</div>\n\t\t\t\t</div>`\n\t\t\t);\n\t}\n\n\tupdate_totals_section(doc) {\n\t\tif (!doc) doc = this.events.get_frm().doc;\n\t\tconst paid_amount = doc.paid_amount;\n\t\tconst grand_total = cint(frappe.sys_defaults.disable_rounded_total)\n\t\t\t? doc.grand_total\n\t\t\t: doc.rounded_total;\n\t\tconst remaining = grand_total - doc.paid_amount;\n\t\tconst change = doc.change_amount || remaining <= 0 ? -1 * remaining : undefined;\n\t\tconst currency = doc.currency;\n\t\t\n\t\tconst label = remaining > 0 ? __(\"Outstanding Amount\") : __(\"Change Amount\");\n\n\t\tthis.$totals.html(\n\t\t\t`<div class=\"col\">\n\t\t\t\t<div class=\"total-label\">${__(\"Grand Total\")}</div>\n\t\t\t\t<div class=\"value\">${format_currency(grand_total, currency)}</div>\n\t\t\t</div>\n\t\t\t<div class=\"seperator-y\"></div>\n\t\t\t<div class=\"col\">\n\t\t\t\t<div class=\"total-label\">${__(\"Paid Amount\")}</div>\n\t\t\t\t<div class=\"value\">${format_currency(paid_amount, currency)}</div>\n\t\t\t</div>\n\t\t\t<div class=\"seperator-y\"></div>\n\t\t\t<div class=\"col\">\n\t\t\t\t<div class=\"total-label\">${label}</div>\n\t\t\t\t<div class=\"value\">${format_currency(Math.abs(change || remaining), currency)}</div>\n\t\t\t</div>`\n\t\t);\n\t}\n\n\ttoggle_component(show) {\n\t\tshow ? this.$component.css(\"display\", \"flex\") : this.$component.css(\"display\", \"none\");\n\t}\n\n\tsanitize_mode_of_payment(mode_of_payment) {\n\t\treturn mode_of_payment\n\t\t\t.replace(/ +/g, \"_\")\n\t\t\t.replace(/[^\\p{L}\\p{N}_-]/gu, \"\")\n\t\t\t.replace(/^[^_a-zA-Z\\p{L}]+/u, \"\")\n\t\t\t.toLowerCase();\n\t}\n\n\tasync unset_grand_total_to_default_mop() {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tlet r = await frappe.db.get_value(\n\t\t\t\"POS Profile\",\n\t\t\tdoc.pos_profile,\n\t\t\t\"disable_grand_total_to_default_mop\"\n\t\t);\n\n\t\tif (!r.message.disable_grand_total_to_default_mop) {\n\t\t\tthis.focus_on_default_mop();\n\t\t}\n\t}\n\n\tvalidate_reqd_invoice_fields() {\n\t\tconst doc = this.events.get_frm().doc;\n\t\tlet validation_flag = true;\n\t\tfor (let field of this.reqd_invoice_fields) {\n\t\t\tif (!doc[field.fieldname]) {\n\t\t\t\tvalidation_flag = false;\n\t\t\t\tfrappe.show_alert({\n\t\t\t\t\tmessage: __(\"{0} is a mandatory field.\", [field.label]),\n\t\t\t\t\tindicator: \"orange\",\n\t\t\t\t});\n\t\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\t}\n\t\t}\n\t\treturn validation_flag;\n\t}\n};", "erpnext.SalesInvoiceUI.PastOrderList = class {\n\tconstructor({ wrapper, events }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\t\tthis.current_filters = { search_term: \"\", status: \"Draft\" };\n\t\tthis.pagination = {\n\t\t\tcurrent_page: 1,\n\t\t\titems_per_page: 7,\n\t\t\ttotal_pages: 1,\n\t\t\ttotal_items: 0\n\t\t};\n\t\tthis.all_invoices = [];\n\t\tthis.init_component();\n\t}\n\n\tinit_component() {\n\t\tthis.prepare_dom();\n\t\tthis.setup_responsive_layout();\n\t\tthis.make_filter_section();\n\t\tthis.bind_events();\n\t\t\n\t}\n\n\tprepare_dom() {\n\t\tthis.wrapper.append(\n\t\t\t`<div class=\"past-order-list\">\n\t\t\t\t<div class=\"filter-section\">\n\t\t\t\t\t<div class=\"label\">${__(\"Recent Orders\")}</div>\n\t\t\t\t\t<div class=\"search-field\"></div>\n\t\t\t\t\t<div class=\"status-field\"></div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"invoices-container\"></div>\n\t\t\t\t<div class=\"pagination-section\"></div>\n\t\t\t</div>`\n\t\t);\n\n\t\tthis.$component = this.wrapper.find(\".past-order-list\");\n\t\tthis.$invoices_container = this.$component.find(\".invoices-container\");\n\t\tthis.$pagination_section = this.$component.find(\".pagination-section\");\n\t}\n\n\tsetup_responsive_layout() {\n\t\t// Remove existing styles first to avoid duplicates\n\t\tconst existingStyle = document.getElementById('pos-responsive-styles');\n\t\tif (existingStyle) {\n\t\t\texistingStyle.remove();\n\t\t}\n\n\t\tconst style = document.createElement('style');\n\t\tstyle.id = 'pos-responsive-styles';\n\t\tstyle.textContent = `\n\t\t\t/* Pagination Styles - with high specificity */\n\t\t\t.past-order-list .pagination-section {\n\t\t\t\tflex-shrink: 0 !important;\n\t\t\t\tpadding: 1rem !important;\n\t\t\t\tbackground: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\n\t\t\t\tborder-top: 1px solid #d1d8dd !important;\n\t\t\t\tborder-radius: 0 0 8px 8px !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-container {\n\t\t\t\tdisplay: flex !important;\n\t\t\t\tjustify-content: space-between !important;\n\t\t\t\talign-items: center !important;\n\t\t\t\tgap: 1rem !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-info {\n\t\t\t\tcolor: #6c757d !important;\n\t\t\t\tfont-size: 0.875rem !important;\n\t\t\t\tfont-weight: 500 !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-controls {\n\t\t\t\tdisplay: flex !important;\n\t\t\t\tgap: 0.5rem !important;\n\t\t\t\talign-items: center !important;\n\t\t\t}\n\n\t\t\t/* Critical pagination button styles with maximum specificity */\n\t\t\t.past-order-list .pagination-section .pagination-btn {\n\t\t\t\tdisplay: flex !important;\n\t\t\t\talign-items: center !important;\n\t\t\t\tjustify-content: center !important;\n\t\t\t\twidth: 36px !important;\n\t\t\t\theight: 36px !important;\n\t\t\t\tborder: 1px solid #d1d8dd !important;\n\t\t\t\tbackground: white !important;\n\t\t\t\tcolor: #6c757d !important;\n\t\t\t\tborder-radius: 6px !important;\n\t\t\t\tcursor: pointer !important;\n\t\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\n\t\t\t\tfont-size: 0.875rem !important;\n\t\t\t\tfont-weight: 500 !important;\n\t\t\t\tposition: relative !important;\n\t\t\t\toverflow: hidden !important;\n\t\t\t\tbox-sizing: border-box !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn::before {\n\t\t\t\tcontent: '' !important;\n\t\t\t\tposition: absolute !important;\n\t\t\t\ttop: 0 !important;\n\t\t\t\tleft: -100% !important;\n\t\t\t\twidth: 100% !important;\n\t\t\t\theight: 100% !important;\n\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent) !important;\n\t\t\t\ttransition: left 0.5s !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn:hover::before {\n\t\t\t\tleft: 100% !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn:hover {\n\t\t\t\tborder-color: #667eea !important;\n\t\t\t\tcolor: #667eea !important;\n\t\t\t\ttransform: translateY(-2px) !important;\n\t\t\t\tbox-shadow: 0 4px 12px rgba(102, 126, 234, 0.2) !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn:active {\n\t\t\t\ttransform: translateY(0) !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn.active {\n\t\t\t\tbackground: linear-gradient(135deg,rgb(102, 234, 120),rgb(38, 173, 72)) !important;\n\t\t\t\tcolor: white !important;\n\t\t\t\tborder-color:rgb(66, 245, 245) !important;\n\t\t\t\tbox-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn:disabled {\n\t\t\t\topacity: 0.4 !important;\n\t\t\t\tcursor: not-allowed !important;\n\t\t\t\ttransform: none !important;\n\t\t\t\tbox-shadow: none !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn:disabled:hover {\n\t\t\t\tborder-color: #d1d8dd !important;\n\t\t\t\tcolor: #6c757d !important;\n\t\t\t\ttransform: none !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-section .pagination-btn:disabled::before {\n\t\t\t\tdisplay: none !important;\n\t\t\t}\n\n\t\t\t.past-order-list .pagination-numbers {\n\t\t\t\tdisplay: flex !important;\n\t\t\t\tgap: 0.25rem !important;\n\t\t\t}\n\n\t\t\t/* Loading, empty, and error states */\n\t\t\t.past-order-list .loading-state,\n\t\t\t.past-order-list .empty-state,\n\t\t\t.past-order-list .error-state {\n\t\t\t\tpadding: 3rem 2rem !important;\n\t\t\t\ttext-align: center !important;\n\t\t\t\tcolor: #8d99a6 !important;\n\t\t\t\tdisplay: flex !important;\n\t\t\t\tflex-direction: column !important;\n\t\t\t\talign-items: center !important;\n\t\t\t\tjustify-content: center !important;\n\t\t\t\tmin-height: 200px !important;\n\t\t\t}\n\n\t\t\t.past-order-list .loading-state {\n\t\t\t\tposition: relative !important;\n\t\t\t}\n\n\t\t\t.past-order-list .loading-state::before {\n\t\t\t\tcontent: '' !important;\n\t\t\t\twidth: 40px !important;\n\t\t\t\theight: 40px !important;\n\t\t\t\tborder: 3px solid #f3f3f3 !important;\n\t\t\t\tborder-top: 3px solid #667eea !important;\n\t\t\t\tborder-radius: 50% !important;\n\t\t\t\tanimation: spin 1s linear infinite !important;\n\t\t\t\tmargin-bottom: 1rem !important;\n\t\t\t}\n\n\t\t\t@keyframes spin {\n\t\t\t\t0% { transform: rotate(0deg) !important; }\n\t\t\t\t100% { transform: rotate(360deg) !important; }\n\t\t\t}\n\n\t\t\t.past-order-list .error-state {\n\t\t\t\tcolor: #e74c3c !important;\n\t\t\t}\n\n\t\t\t.past-order-list .empty-state {\n\t\t\t\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;\n\t\t\t\tborder-radius: 8px !important;\n\t\t\t\tmargin: 1rem !important;\n\t\t\t}\n\n\t\t\t/* Page transition effects */\n\t\t\t.past-order-list .page-transition-enter {\n\t\t\t\topacity: 0 !important;\n\t\t\t\ttransform: translateX(30px) !important;\n\t\t\t}\n\n\t\t\t.past-order-list .page-transition-enter-active {\n\t\t\t\topacity: 1 !important;\n\t\t\t\ttransform: translateX(0) !important;\n\t\t\t\ttransition: all 0.3s ease !important;\n\t\t\t}\n\n\t\t\t.past-order-list .page-transition-exit {\n\t\t\t\topacity: 1 !important;\n\t\t\t\ttransform: translateX(0) !important;\n\t\t\t}\n\n\t\t\t.past-order-list .page-transition-exit-active {\n\t\t\t\topacity: 0 !important;\n\t\t\t\ttransform: translateX(-30px) !important;\n\t\t\t\ttransition: all 0.3s ease !important;\n\t\t\t}\n\n\t\t\t/* Mobile styles */\n\t\t\t@media (max-width: 768px) {\n\t\t\t\t.past-order-list .pagination-container {\n\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\tgap: 0.75rem !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .pagination-info {\n\t\t\t\t\torder: 2 !important;\n\t\t\t\t\tfont-size: 0.8rem !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .pagination-controls {\n\t\t\t\t\torder: 1 !important;\n\t\t\t\t\tjustify-content: center !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .invoice-wrapper {\n\t\t\t\t\tflex-direction: column !important;\n\t\t\t\t\talign-items: flex-start !important;\n\t\t\t\t\tgap: 0.75rem !important;\n\t\t\t\t\tpadding: 1rem !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .invoice-total-status {\n\t\t\t\t\talign-self: flex-end !important;\n\t\t\t\t\ttext-align: right !important;\n\t\t\t\t\tmargin-left: 0 !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .filter-section,\n\t\t\t\t.past-order-list .pagination-section {\n\t\t\t\t\tpadding: 0.75rem !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Extra small screens */\n\t\t\t@media (max-width: 480px) {\n\t\t\t\t.past-order-list .pagination-section .pagination-btn {\n\t\t\t\t\twidth: 32px !important;\n\t\t\t\t\theight: 32px !important;\n\t\t\t\t\tfont-size: 0.8rem !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .pagination-controls {\n\t\t\t\t\tgap: 0.25rem !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .invoice-wrapper {\n\t\t\t\t\tpadding: 0.75rem !important;\n\t\t\t\t}\n\n\t\t\t\t.past-order-list .filter-section,\n\t\t\t\t.past-order-list .pagination-section {\n\t\t\t\t\tpadding: 0.5rem !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Smooth scrolling and transitions */\n\t\t\t.past-order-list .invoices-container {\n\t\t\t\tscroll-behavior: smooth !important;\n\t\t\t}\n\n\t\t\t.past-order-list .invoice-wrapper {\n\t\t\t\twill-change: transform, opacity !important;\n\t\t\t}\n\n\t\t\t/* Focus styles for accessibility */\n\t\t\t.past-order-list .pagination-section .pagination-btn:focus {\n\t\t\t\toutline: 2px solid #667eea !important;\n\t\t\t\toutline-offset: 2px !important;\n\t\t\t}\n\t\t`;\n\t\tdocument.head.appendChild(style);\n\t\t\n\t\t// Force style recalculation\n\t\tsetTimeout(() => {\n\t\t\tif (this.$pagination_section && this.$pagination_section.length) {\n\t\t\t\tthis.$pagination_section[0].offsetHeight; // Trigger reflow\n\t\t\t}\n\t\t}, 0);\n\t}\n\n\trefresh_pagination_styles() {\n\t\t// Force style reapplication\n\t\tthis.$pagination_section.find('.pagination-btn').each(function() {\n\t\t\t$(this).removeClass('pagination-btn').addClass('pagination-btn');\n\t\t});\n\t\t\n\t\t// Trigger reflow\n\t\tif (this.$pagination_section[0]) {\n\t\t\tthis.$pagination_section[0].offsetHeight;\n\t\t}\n\t}\n\n\tbind_events() {\n\t\tthis.search_field.$input.on(\"input\", (e) => {\n\t\t\tclearTimeout(this.last_search);\n\t\t\tthis.last_search = setTimeout(() => {\n\t\t\t\tconst search_term = e.target.value;\n\t\t\t\tthis.current_filters.search_term = search_term;\n\t\t\t\tthis.pagination.current_page = 1; // Reset to first page\n\t\t\t\tthis.refresh_list(search_term, this.status_field.get_value());\n\t\t\t}, 300);\n\t\t});\n\t\t\n\t\tconst me = this;\n\t\tthis.$invoices_container.on(\"click\", \".invoice-wrapper\", function () {\n\t\t\tconst invoice_name = unescape($(this).attr(\"data-invoice-name\"));\n\t\t\tme.events.open_invoice_data(invoice_name);\n\t\t});\n\n\t\t// Handle window resize for responsive behavior\n\t\t$(window).on('resize', this.debounce(() => {\n\t\t\tthis.handle_resize();\n\t\t}, 250));\n\t}\n\n\tdebounce(func, wait) {\n\t\tlet timeout;\n\t\treturn function executedFunction(...args) {\n\t\t\tconst later = () => {\n\t\t\t\tclearTimeout(timeout);\n\t\t\t\tfunc(...args);\n\t\t\t};\n\t\t\tclearTimeout(timeout);\n\t\t\ttimeout = setTimeout(later, wait);\n\t\t};\n\t}\n\n\thandle_resize() {\n\t\t// Update items per page based on screen size\n\t\tif (window.innerWidth <= 480) {\n\t\t\tthis.pagination.items_per_page = 5;\n\t\t} else if (window.innerWidth <= 768) {\n\t\t\tthis.pagination.items_per_page = 6;\n\t\t} else {\n\t\t\tthis.pagination.items_per_page = 7;\n\t\t}\n\t\t\n\t\t// Recalculate pagination and refresh current page\n\t\tthis.calculate_pagination();\n\t\tthis.render_current_page();\n\t\tthis.render_pagination();\n\t}\n\n\tmake_filter_section() {\n\t\tconst me = this;\n\t\t\n\t\tthis.search_field = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Search\"),\n\t\t\t\tfieldtype: \"Data\",\n\t\t\t\tplaceholder: __(\"Search by invoice id or customer name\"),\n\t\t\t},\n\t\t\tparent: this.$component.find(\".search-field\"),\n\t\t\trender_input: true,\n\t\t});\n\n\t\tthis.status_field = frappe.ui.form.make_control({\n\t\t\tdf: {\n\t\t\t\tlabel: __(\"Invoice Status\"),\n\t\t\t\tfieldtype: \"Select\",\n\t\t\t\toptions: `Draft\\nPaid\\nConsolidated\\nReturn\\nPartly Paid`,\n\t\t\t\tplaceholder: __(\"Filter by invoice status\"),\n\t\t\t\tonchange: function () {\n\t\t\t\t\tif (me.$component.is(\":visible\")) {\n\t\t\t\t\t\tme.current_filters.status = this.get_value();\n\t\t\t\t\t\tme.pagination.current_page = 1; // Reset to first page\n\t\t\t\t\t\tme.refresh_list();\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t},\n\t\t\tparent: this.$component.find(\".status-field\"),\n\t\t\trender_input: true,\n\t\t});\n\n\t\tthis.search_field.toggle_label(false);\n\t\tthis.status_field.toggle_label(false);\n\t\tthis.status_field.set_value(\"Draft\");\n\t\tthis.current_filters.status = \"Draft\";\n\n\t\tthis.settlement_button = $(`\n\t\t\t<button class=\"btn btn-primary btn-sm\" style=\"margin-left: 10px;\">\n\t\t\t\t<i class=\"fa fa-money\"></i> ${__('Make Settlement')}\n\t\t\t</button>\n\t\t`);\n\t\t\n\t\tthis.$component.find(\".filter-section\").append(this.settlement_button);\n\t\t\n\t\tthis.settlement_button.on('click', () => {\n\t\t\tthis.show_settlement_dialog();\n\t\t});\n\t}\n\n\tshow_settlement_dialog() {\n\t\tlet dialog = new frappe.ui.Dialog({\n\t\t\ttitle: __('Make Settlement'),\n\t\t\tfields: [\n\t\t\t\t{\n\t\t\t\t\tlabel: __('Customer'),\n\t\t\t\t\tfieldname: 'customer',\n\t\t\t\t\tfieldtype: 'Link',\n\t\t\t\t\toptions: 'Customer',\n\t\t\t\t\treqd: 1,\n\t\t\t\t\tget_query: function() {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tfilters: {\n\t\t\t\t\t\t\t\t'disabled': 0\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t],\n\t\t\tprimary_action_label: __('Settle'),\n\t\t\tprimary_action: (values) => {\n\t\t\t\tif (!values.customer) {\n\t\t\t\t\tfrappe.msgprint(__('Please select customer'));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.create_payment_entry(values.customer);\n\t\t\t\tdialog.hide();\n\t\t\t}\n\t\t});\n\t\t\n\t\tdialog.show();\n\t}\n\n\tcreate_payment_entry(customer) {\n\t\tfrappe.call({\n\t\t\tmethod: 'erpnext.accounts.utils.get_balance_on',\n\t\t\targs: {\n\t\t\t\tparty_type: 'Customer',\n\t\t\t\tparty: customer,\n\t\t\t\tcompany: frappe.defaults.get_user_default('Company')\n\t\t\t},\n\t\t\tcallback: (r) => {\n\t\t\t\tif (typeof r.message === 'number') {\n\t\t\t\t\tlet outstanding_amount = Math.abs(r.message);\n\n\t\t\t\t\tfrappe.new_doc('Payment Entry');\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tif (cur_frm && cur_frm.doc.doctype === 'Payment Entry') {\n\t\t\t\t\t\t\tcur_frm.set_value('party_type', 'Customer');\n\t\t\t\t\t\t\tcur_frm.set_value('party', customer);\n\t\t\t\t\t\t\tcur_frm.set_value('payment_type', 'Receive');\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 500);\n\t\t\t\t} else {\n\t\t\t\t\tfrappe.msgprint(__('Could not fetch outstanding amount for customer {0}', [customer]));\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tset_filter_and_refresh(status, search_term = \"\") {\n\t\tconsole.log(`Setting filter: status=${status}, search_term=${search_term}`);\n\t\t\n\t\tthis.clear_container();\n\t\t\n\t\tthis.current_filters.status = status;\n\t\tthis.current_filters.search_term = search_term;\n\t\tthis.pagination.current_page = 1; // Reset pagination\n\t\t\n\t\tthis.status_field.set_value(status);\n\t\tif (search_term) {\n\t\t\tthis.search_field.set_value(search_term);\n\t\t} else {\n\t\t\tthis.search_field.set_value(\"\");\n\t\t}\n\t\t\n\t\tthis.refresh_list(search_term, status);\n\t}\n\n\tclear_container() {\n\t\tthis.$invoices_container.html(`\n\t\t\t<div class=\"loading-state\">\n\t\t\t\t<div>${__(\"Loading orders...\")}</div>\n\t\t\t</div>\n\t\t`);\n\t\tthis.$pagination_section.html(\"\");\n\t}\n\n\trefresh_list(search_term = null, status = null) {\n\t\tconst final_search_term = search_term !== null ? search_term : this.current_filters.search_term;\n\t\tconst final_status = status !== null ? status : this.current_filters.status;\n\n\t\tconsole.log(`Refreshing list with: search_term=\"${final_search_term}\", status=\"${final_status}\"`);\n\n\t\tthis.current_filters.search_term = final_search_term;\n\t\tthis.current_filters.status = final_status;\n\n\t\tif (this.$invoices_container.find('.loading-state').length === 0) {\n\t\t\tthis.clear_container();\n\t\t}\n\n\t\tthis.events.reset_summary();\n\n\t\treturn frappe.call({\n\t\t\tmethod: \"moonstar.moonstar.page.sales_invoice_ui.sales_invoice_ui.get_past_order_list\",\n\t\t\tfreeze: true,\n\t\t\targs: { \n\t\t\t\tsearch_term: final_search_term, \n\t\t\t\tstatus: final_status \n\t\t\t},\n\t\t\tcallback: (response) => {\n\t\t\t\tthis.$invoices_container.html(\"\");\n\t\t\t\t\n\t\t\t\tif (response.message && response.message.length > 0) {\n\t\t\t\t\tthis.all_invoices = response.message;\n\t\t\t\t\tthis.calculate_pagination();\n\t\t\t\t\tthis.render_current_page();\n\t\t\t\t\tthis.render_pagination();\n\t\t\t\t\t\n\t\t\t\t\tthis.update_filter_label(final_status, response.message.length);\n\t\t\t\t} else {\n\t\t\t\t\tthis.all_invoices = [];\n\t\t\t\t\tthis.pagination.total_pages = 1;\n\t\t\t\t\tthis.pagination.total_items = 0;\n\t\t\t\t\t\n\t\t\t\t\tthis.$invoices_container.append(\n\t\t\t\t\t\t`<div class=\"empty-state\">\n\t\t\t\t\t\t\t<div style=\"font-size: 1.2rem; margin-bottom: 0.5rem;\">📋</div>\n\t\t\t\t\t\t\t<div>${__(\"No orders found for the selected criteria\")}</div>\n\t\t\t\t\t\t\t<div style=\"margin-top: 0.5rem; font-size: 0.9em; opacity: 0.8;\">${__(\"Status\")}: ${final_status}</div>\n\t\t\t\t\t\t</div>`\n\t\t\t\t\t);\n\t\t\t\t\tthis.$pagination_section.html(\"\");\n\t\t\t\t}\n\t\t\t},\n\t\t\terror: (error) => {\n\t\t\t\tconsole.error(\"Error fetching past orders:\", error);\n\t\t\t\tthis.$invoices_container.html(`\n\t\t\t\t\t<div class=\"error-state\">\n\t\t\t\t\t\t<div style=\"font-size: 1.2rem; margin-bottom: 0.5rem;\">⚠️</div>\n\t\t\t\t\t\t<div>${__(\"Error loading past orders\")}</div>\n\t\t\t\t\t\t<div style=\"margin-top: 0.5rem; font-size: 0.9em;\">${__(\"Please try again\")}</div>\n\t\t\t\t\t</div>\n\t\t\t\t`);\n\t\t\t\tthis.$pagination_section.html(\"\");\n\t\t\t\tfrappe.msgprint(__(\"Error loading past orders. Please try again.\"));\n\t\t\t}\n\t\t});\n\t}\n\n\tcalculate_pagination() {\n\t\tthis.pagination.total_items = this.all_invoices.length;\n\t\tthis.pagination.total_pages = Math.ceil(this.pagination.total_items / this.pagination.items_per_page);\n\t\t\n\t\t// Ensure current page is valid\n\t\tif (this.pagination.current_page > this.pagination.total_pages) {\n\t\t\tthis.pagination.current_page = Math.max(1, this.pagination.total_pages);\n\t\t}\n\t}\n\n\trender_current_page() {\n\t\tconst start_index = (this.pagination.current_page - 1) * this.pagination.items_per_page;\n\t\tconst end_index = start_index + this.pagination.items_per_page;\n\t\tconst current_page_invoices = this.all_invoices.slice(start_index, end_index);\n\n\t\tthis.$invoices_container.html(\"\");\n\n\t\tif (current_page_invoices.length > 0) {\n\t\t\tcurrent_page_invoices.forEach((invoice, index) => {\n\t\t\t\tconst invoice_html = this.get_invoice_html(invoice);\n\t\t\t\tconst $invoice = $(invoice_html);\n\t\t\t\t\n\t\t\t\t// Add staggered animation delay\n\t\t\t\t$invoice.css('animation-delay', `${0.1 + (index * 0.05)}s`);\n\t\t\t\t\n\t\t\t\tthis.$invoices_container.append($invoice);\n\t\t\t});\n\t\t}\n\t}\n\n\trender_pagination() {\n\t\tif (this.pagination.total_pages <= 1) {\n\t\t\tthis.$pagination_section.html(\"\");\n\t\t\treturn;\n\t\t}\n\n\t\tconst start_item = (this.pagination.current_page - 1) * this.pagination.items_per_page + 1;\n\t\tconst end_item = Math.min(start_item + this.pagination.items_per_page - 1, this.pagination.total_items);\n\n\t\tlet pagination_html = `\n\t\t\t<div class=\"pagination-container\">\n\t\t\t\t<div class=\"pagination-info\">\n\t\t\t\t\t${__(\"Showing\")} ${start_item}-${end_item} ${__(\"of\")} ${this.pagination.total_items} ${__(\"orders\")}\n\t\t\t\t</div>\n\t\t\t\t<div class=\"pagination-controls\">\n\t\t\t\t\t<button class=\"pagination-btn\" data-action=\"prev\" ${this.pagination.current_page === 1 ? 'disabled' : ''}>\n\t\t\t\t\t\t<i class=\"fa fa-angle-left\"></i>\n\t\t\t\t\t</button>\n\t\t\t\t\t<div class=\"pagination-numbers\">\n\t\t`;\n\n\t\t// Generate page numbers\n\t\tconst max_visible_pages = 5;\n\t\tlet start_page = Math.max(1, this.pagination.current_page - Math.floor(max_visible_pages / 2));\n\t\tlet end_page = Math.min(this.pagination.total_pages, start_page + max_visible_pages - 1);\n\n\t\t// Adjust start_page if we're near the end\n\t\tif (end_page - start_page + 1 < max_visible_pages) {\n\t\t\tstart_page = Math.max(1, end_page - max_visible_pages + 1);\n\t\t}\n\n\t\tfor (let i = start_page; i <= end_page; i++) {\n\t\t\tpagination_html += `\n\t\t\t\t<button class=\"pagination-btn ${i === this.pagination.current_page ? 'active' : ''}\" data-action=\"page\" data-page=\"${i}\">\n\t\t\t\t\t${i}\n\t\t\t\t</button>\n\t\t\t`;\n\t\t}\n\n\t\tpagination_html += `\n\t\t\t\t\t</div>\n\t\t\t\t\t<button class=\"pagination-btn\" data-action=\"next\" ${this.pagination.current_page === this.pagination.total_pages ? 'disabled' : ''}>\n\t\t\t\t\t\t<i class=\"fa fa-angle-right\"></i>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t`;\n\n\t\tthis.$pagination_section.html(pagination_html);\n\n\t\tsetTimeout(() => {\n\t\t\tthis.refresh_pagination_styles();\n\t\t}, 100);\n\n\t\t// Bind pagination events\n\t\tthis.$pagination_section.find('.pagination-btn').on('click', (e) => {\n\t\t\tconst $btn = $(e.currentTarget);\n\t\t\tif ($btn.prop('disabled')) return;\n\n\t\t\tconst action = $btn.data('action');\n\t\t\tlet new_page = this.pagination.current_page;\n\n\t\t\tswitch (action) {\n\t\t\t\tcase 'first':\n\t\t\t\t\tnew_page = 1;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'prev':\n\t\t\t\t\tnew_page = Math.max(1, this.pagination.current_page - 1);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'next':\n\t\t\t\t\tnew_page = Math.min(this.pagination.total_pages, this.pagination.current_page + 1);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'last':\n\t\t\t\t\tnew_page = this.pagination.total_pages;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'page':\n\t\t\t\t\tnew_page = parseInt($btn.data('page'));\n\t\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (new_page !== this.pagination.current_page) {\n\t\t\t\tthis.goto_page(new_page);\n\t\t\t}\n\t\t});\n\t}\n\n\tgoto_page(page) {\n\t\tif (page < 1 || page > this.pagination.total_pages || page === this.pagination.current_page) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Add smooth transition effect\n\t\tthis.$invoices_container.addClass('page-transition-exit');\n\t\t\n\t\tsetTimeout(() => {\n\t\t\tthis.pagination.current_page = page;\n\t\t\tthis.render_current_page();\n\t\t\tthis.render_pagination();\n\t\t\t\n\t\t\tthis.$invoices_container.removeClass('page-transition-exit');\n\t\t\tthis.$invoices_container.addClass('page-transition-enter');\n\t\t\t\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.$invoices_container.removeClass('page-transition-enter');\n\t\t\t}, 300);\n\t\t}, 150);\n\t}\n\n\tupdate_filter_label(status, count) {\n\t\tconst label = this.$component.find(\".label\");\n\t\tlabel.html(`${__(\"Recent Orders\")} - ${status} (${count})`);\n\t}\n\n\tget_invoice_html(invoice) {\n\t\tconst posting_datetime = frappe.datetime.str_to_user(\n\t\t\tinvoice.posting_date + \" \" + invoice.posting_time\n\t\t);\n\t\t\n\t\treturn `<div class=\"invoice-wrapper\" data-invoice-name=\"${escape(invoice.name)}\">\n\t\t\t\t<div class=\"invoice-name-date\">\n\t\t\t\t\t<div class=\"invoice-name\">${invoice.name}</div>\n\t\t\t\t\t<div class=\"invoice-customer\">\n\t\t\t\t\t\t<svg class=\"customer-icon\" width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" fill=\"none\">\n\t\t\t\t\t\t\t<path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n\t\t\t\t\t\t\t<circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t${frappe.ellipsis(invoice.customer, 20)}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"invoice-total-status\">\n\t\t\t\t\t<div class=\"invoice-total\">${format_currency(invoice.grand_total, invoice.currency) || 0}</div>\n\t\t\t\t\t<div class=\"invoice-datetime\">${posting_datetime}</div>\n\t\t\t\t</div>\n\t\t\t</div>`;\n\t}\n\n\ttoggle_component(show) {\n\t\tif (show) {\n\t\t\tthis.$component.css(\"display\", \"flex\");\n\t\t\tif (!this.current_filters.status) {\n\t\t\t\tthis.current_filters.status = \"Draft\";\n\t\t\t\tthis.status_field.set_value(\"Draft\");\n\t\t\t}\n\t\t\tthis.pagination.current_page = 1;\n\t\t\tthis.refresh_list();\n\t\t\tthis.handle_resize();\n\t\t} else {\n\t\t\tthis.$component.css(\"display\", \"none\");\n\t\t\tthis.clear_container();\n\t\t}\n\t}\n\n\treset_filters() {\n\t\tthis.current_filters = { search_term: \"\", status: \"Draft\" };\n\t\tthis.pagination.current_page = 1;\n\t\tthis.search_field.set_value(\"\");\n\t\tthis.status_field.set_value(\"Draft\");\n\t\tthis.$component.find(\".label\").html(__(\"Recent Orders\"));\n\t}\n\n\tscroll_to_invoice(invoice_name) {\n\t\t// Find which page contains this invoice\n\t\tconst invoice_index = this.all_invoices.findIndex(inv => inv.name === invoice_name);\n\t\tif (invoice_index !== -1) {\n\t\t\tconst target_page = Math.ceil((invoice_index + 1) / this.pagination.items_per_page);\n\t\t\t\n\t\t\tif (target_page !== this.pagination.current_page) {\n\t\t\t\tthis.goto_page(target_page);\n\t\t\t}\n\t\t\t\n\t\t\t// Highlight the invoice after page load\n\t\t\tsetTimeout(() => {\n\t\t\t\tconst invoice_element = this.$invoices_container.find(`[data-invoice-name=\"${escape(invoice_name)}\"]`);\n\t\t\t\tif (invoice_element.length) {\n\t\t\t\t\t// Add temporary highlight effect\n\t\t\t\t\tinvoice_element.css({\n\t\t\t\t\t\t'background': 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n\t\t\t\t\t\t'transform': 'scale(1.02)',\n\t\t\t\t\t\t'box-shadow': '0 4px 20px rgba(102, 126, 234, 0.2)'\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// Remove highlight after 2 seconds\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tinvoice_element.css({\n\t\t\t\t\t\t\t'background': '',\n\t\t\t\t\t\t\t'transform': '',\n\t\t\t\t\t\t\t'box-shadow': ''\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 2000);\n\t\t\t\t}\n\t\t\t}, 400);\n\t\t}\n\t}\n\n\tget_scroll_position() {\n\t\treturn {\n\t\t\tpage: this.pagination.current_page,\n\t\t\tscroll_top: this.$invoices_container.scrollTop()\n\t\t};\n\t}\n\n\tset_scroll_position(position) {\n\t\tif (position.page && position.page !== this.pagination.current_page) {\n\t\t\tthis.goto_page(position.page);\n\t\t}\n\t\tif (position.scroll_top) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.$invoices_container.scrollTop(position.scroll_top);\n\t\t\t}, 100);\n\t\t}\n\t}\n\n\t// Additional utility methods for better UX\n\tget_current_page_info() {\n\t\treturn {\n\t\t\tcurrent_page: this.pagination.current_page,\n\t\t\ttotal_pages: this.pagination.total_pages,\n\t\t\titems_per_page: this.pagination.items_per_page,\n\t\t\ttotal_items: this.pagination.total_items,\n\t\t\tstart_item: (this.pagination.current_page - 1) * this.pagination.items_per_page + 1,\n\t\t\tend_item: Math.min(this.pagination.current_page * this.pagination.items_per_page, this.pagination.total_items)\n\t\t};\n\t}\n\n\trefresh_pagination_only() {\n\t\tthis.calculate_pagination();\n\t\tthis.render_pagination();\n\t}\n\n\t// Keyboard navigation support\n\tsetup_keyboard_navigation() {\n\t\t$(document).on('keydown.pagination', (e) => {\n\t\t\tif (!this.$component.is(':visible')) return;\n\t\t\t\n\t\t\t// Only handle if no input is focused\n\t\t\tif ($(e.target).is('input, textarea, select')) return;\n\t\t\t\n\t\t\tswitch(e.key) {\n\t\t\t\tcase 'ArrowLeft':\n\t\t\t\t\tif (this.pagination.current_page > 1) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tthis.goto_page(this.pagination.current_page - 1);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'ArrowRight':\n\t\t\t\t\tif (this.pagination.current_page < this.pagination.total_pages) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tthis.goto_page(this.pagination.current_page + 1);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'Home':\n\t\t\t\t\tif (this.pagination.current_page !== 1) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tthis.goto_page(1);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'End':\n\t\t\t\t\tif (this.pagination.current_page !== this.pagination.total_pages) {\n\t\t\t\t\t\te.preventDefault();\n\t\t\t\t\t\tthis.goto_page(this.pagination.total_pages);\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t});\n\t}\n\n\tdestroy() {\n\t\t// Clean up event listeners\n\t\t$(document).off('keydown.pagination');\n\t\t$(window).off('resize');\n\t\t\n\t\t// Clear timers\n\t\tif (this.last_search) {\n\t\t\tclearTimeout(this.last_search);\n\t\t}\n\t}\n};", "erpnext.SalesInvoiceUI.PastOrderSummary = class {\n\tconstructor({ wrapper, settings, events }) {\n\t\tthis.wrapper = wrapper;\n\t\tthis.events = events;\n\t\tthis.print_receipt_on_order_complete = settings.print_receipt_on_order_complete;\n\n\t\tthis.init_component();\n\t}\n\n\tinit_component() {\n\t\tthis.prepare_dom();\n\t\tthis.init_email_print_dialog();\n\t\tthis.init_signature_dialog();\n\t\tthis.bind_events();\n\t\tthis.attach_shortcuts();\n\t}\n\n\tprepare_dom() {\n\t\tthis.wrapper.append(\n\t\t\t`<section class=\"past-order-summary\">\n\t\t\t\t<div class=\"no-summary-placeholder\">\n\t\t\t\t\t${__(\"Select an invoice to load summary data\")}\n\t\t\t\t</div>\n\t\t\t\t<div class=\"invoice-summary-wrapper\">\n\t\t\t\t\t<div class=\"abs-container\">\n\t\t\t\t\t\t<div class=\"upper-section\"></div>\n\t\t\t\t\t\t<div class=\"label\">${__(\"Items\")}</div>\n\t\t\t\t\t\t<div class=\"items-container summary-container\"></div>\n\t\t\t\t\t\t<div class=\"label\">${__(\"Totals\")}</div>\n\t\t\t\t\t\t<div class=\"totals-container summary-container\"></div>\n\t\t\t\t\t\t<div class=\"label\">${__(\"Payments\")}</div>\n\t\t\t\t\t\t<div class=\"payments-container summary-container\"></div>\n\t\t\t\t\t\t<div class=\"signature-section\" style=\"display: none;\">\n\t\t\t\t\t\t\t<div class=\"label\">${__(\"Customer Signature\")}</div>\n\t\t\t\t\t\t\t<div class=\"signature-container\">\n\t\t\t\t\t\t\t\t<div class=\"signature-display\" style=\"min-height: 100px; border: 1px solid #ccc; background: white; text-align: center; padding: 20px;\">\n\t\t\t\t\t\t\t\t\t<img class=\"signature-image\" style=\"max-width: 300px; max-height: 100px; display: none;\" />\n\t\t\t\t\t\t\t\t\t<div class=\"no-signature-text\">${__(\"No signature available\")}</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"signature-controls\" style=\"margin-top: 10px;\">\n\t\t\t\t\t\t\t\t\t<button class=\"btn btn-sm btn-primary add-signature-btn\">${__(\"Add Signature\")}</button>\n\t\t\t\t\t\t\t\t\t<button class=\"btn btn-sm btn-default clear-signature-btn\" style=\"display: none;\">${__(\"Clear Signature\")}</button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"summary-btns\"></div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</section>`\n\t\t);\n\n\t\tthis.$component = this.wrapper.find(\".past-order-summary\");\n\t\tthis.$summary_wrapper = this.$component.find(\".invoice-summary-wrapper\");\n\t\tthis.$summary_container = this.$component.find(\".abs-container\");\n\t\tthis.$upper_section = this.$summary_container.find(\".upper-section\");\n\t\tthis.$items_container = this.$summary_container.find(\".items-container\");\n\t\tthis.$totals_container = this.$summary_container.find(\".totals-container\");\n\t\tthis.$payment_container = this.$summary_container.find(\".payments-container\");\n\t\tthis.$summary_btns = this.$summary_container.find(\".summary-btns\");\n\t\tthis.$signature_display_section = this.$summary_container.find(\".signature-section\");\n\t\tthis.$signature_image = this.$summary_container.find(\".signature-image\");\n\t\tthis.$no_signature_text = this.$summary_container.find(\".no-signature-text\");\n\t}\n\n\tinit_signature_dialog() {\n\t\t// Create a dialog with Frappe's signature field\n\t\tthis.signature_dialog = new frappe.ui.Dialog({\n\t\t\ttitle: __(\"Customer Signature\"),\n\t\t\tfields: [\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"signature\",\n\t\t\t\t\tfieldtype: \"Signature\",\n\t\t\t\t\tlabel: __(\"Signature\"),\n\t\t\t\t\treqd: 1\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"section_break\",\n\t\t\t\t\tfieldtype: \"Section Break\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"customer_name\",\n\t\t\t\t\tfieldtype: \"Data\",\n\t\t\t\t\tlabel: __(\"Customer Name\"),\n\t\t\t\t\tread_only: 1\n\t\t\t\t}\n\t\t\t],\n\t\t\tprimary_action: (values) => {\n\t\t\t\tthis.save_signature(values.signature);\n\t\t\t},\n\t\t\tprimary_action_label: __(\"Save Signature\")\n\t\t});\n\t}\n\n\tinit_email_print_dialog() {\n\t\tconst email_dialog = new frappe.ui.Dialog({\n\t\t\ttitle: __(\"Email Receipt\"),\n\t\t\tfields: [\n\t\t\t\t{ fieldname: \"email_id\", fieldtype: \"Data\", options: \"Email\", label: \"Email ID\", reqd: 1 },\n\t\t\t\t{ fieldname: \"content\", fieldtype: \"Small Text\", label: \"Message (if any)\" },\n\t\t\t],\n\t\t\tprimary_action: () => {\n\t\t\t\tthis.send_email();\n\t\t\t},\n\t\t\tprimary_action_label: __(\"Send\"),\n\t\t});\n\t\tthis.email_dialog = email_dialog;\n\n\t\tconst print_dialog = new frappe.ui.Dialog({\n\t\t\ttitle: __(\"Print Receipt\"),\n\t\t\tfields: [{ fieldname: \"print\", fieldtype: \"Data\", label: \"Print Preview\" }],\n\t\t\tprimary_action: () => {\n\t\t\t\tthis.print_receipt();\n\t\t\t},\n\t\t\tprimary_action_label: __(\"Print\"),\n\t\t});\n\t\tthis.print_dialog = print_dialog;\n\t}\n\n\tget_upper_section_html(doc) {\n\t\tconst { status } = doc;\n\t\tlet indicator_color = \"\";\n\n\t\t[\"Paid\", \"Consolidated\"].includes(status) && (indicator_color = \"green\");\n\t\tstatus === \"Draft\" && (indicator_color = \"red\");\n\t\tstatus === \"Return\" && (indicator_color = \"grey\");\n\t\tstatus === \"Partly Paid\" && (indicator_color = \"orange\");\n\n\t\tconst paid_amount = doc.paid_amount || (doc.grand_total - (doc.outstanding_amount || 0));\n\t\tconst outstanding_amount = doc.outstanding_amount || 0;\n\n\t\tif (status === \"Partly Paid\") {\n\t\t\treturn `<div class=\"left-section\">\n\t\t\t\t\t\t<div class=\"customer-name\">${doc.customer}</div>\n\t\t\t\t\t\t<div class=\"customer-email\">${this.customer_email}</div>\n\t\t\t\t\t\t<div class=\"cashier\">${__(\"Sold by\")}: ${doc.owner}</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"right-section\">\n\t\t\t\t\t\t<div class=\"payment-info\">\n\t\t\t\t\t\t\t<div class=\"paid-amount\">${__(\"Paid\")}: ${format_currency(paid_amount, doc.currency)}</div>\n\t\t\t\t\t\t\t<div class=\"outstanding-amount text-danger\">${__(\"Outstanding\")}: ${format_currency(outstanding_amount, doc.currency)}</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"invoice-name\">${doc.name}</div>\n\t\t\t\t\t\t<span class=\"indicator-pill whitespace-nowrap ${indicator_color}\"><span>${__(doc.status)}</span></span>\n\t\t\t\t\t</div>`;\n\t\t}\n\n\t\treturn `<div class=\"left-section\">\n\t\t\t\t\t<div class=\"customer-name\">${doc.customer}</div>\n\t\t\t\t\t<div class=\"customer-email\">${this.customer_email}</div>\n\t\t\t\t\t<div class=\"cashier\">${__(\"Sold by\")}: ${doc.owner}</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"right-section\">\n\t\t\t\t\t<div class=\"paid-amount\">${format_currency(paid_amount, doc.currency)}</div>\n\t\t\t\t\t<div class=\"invoice-name\">${doc.name}</div>\n\t\t\t\t\t<span class=\"indicator-pill whitespace-nowrap ${indicator_color}\"><span>${__(doc.status)}</span></span>\n\t\t\t\t</div>`;\n\t}\n\n\tget_item_html(doc, item_data) {\n\t\treturn `<div class=\"item-row-wrapper\">\n\t\t\t\t\t<div class=\"item-name\">${item_data.item_name}</div>\n\t\t\t\t\t<div class=\"item-qty\">${item_data.qty || 0} ${item_data.uom}</div>\n\t\t\t\t\t<div class=\"item-rate-disc\">${get_rate_discount_html()}</div>\n\t\t\t\t</div>`;\n\n\t\tfunction get_rate_discount_html() {\n\t\t\tif (item_data.rate && item_data.price_list_rate && item_data.rate !== item_data.price_list_rate) {\n\t\t\t\treturn `<span class=\"item-disc\">(${item_data.discount_percentage}% off)</span>\n\t\t\t\t\t\t<div class=\"item-rate\">${format_currency(item_data.rate, doc.currency)}</div>`;\n\t\t\t} else {\n\t\t\t\treturn `<div class=\"item-rate\">${format_currency(\n\t\t\t\t\titem_data.price_list_rate || item_data.rate,\n\t\t\t\t\tdoc.currency\n\t\t\t\t)}</div>`;\n\t\t\t}\n\t\t}\n\t}\n\n\tget_discount_html(doc) {\n\t\tif (doc.discount_amount) {\n\t\t\treturn `<div class=\"summary-row-wrapper\">\n\t\t\t\t\t\t<div>${__(\"Discount\")} (${doc.additional_discount_percentage} %)</div>\n\t\t\t\t\t\t<div>${format_currency(doc.discount_amount, doc.currency)}</div>\n\t\t\t\t\t</div>`;\n\t\t} else {\n\t\t\treturn ``;\n\t\t}\n\t}\n\n\tget_net_total_html(doc) {\n\t\treturn `<div class=\"summary-row-wrapper\">\n\t\t\t\t\t<div>${__(\"Net Total\")}</div>\n\t\t\t\t\t<div>${format_currency(doc.net_total, doc.currency)}</div>\n\t\t\t\t</div>`;\n\t}\n\n\tget_taxes_html(doc) {\n\t\tif (!doc.taxes.length) return \"\";\n\n\t\tlet taxes_html = doc.taxes\n\t\t\t.map((t) => {\n\t\t\t\t// if tax rate is 0, don't print it.\n\t\t\t\tconst description = /[0-9]+/.test(t.description)\n\t\t\t\t\t? t.description\n\t\t\t\t\t: t.rate != 0\n\t\t\t\t\t? `${t.description} @ ${t.rate}%`\n\t\t\t\t\t: t.description;\n\t\t\t\treturn `\n\t\t\t\t<div class=\"tax-row\">\n\t\t\t\t\t<div class=\"tax-label\">${description}</div>\n\t\t\t\t\t<div class=\"tax-value\">${format_currency(t.tax_amount_after_discount_amount, doc.currency)}</div>\n\t\t\t\t</div>\n\t\t\t`;\n\t\t\t})\n\t\t\t.join(\"\");\n\n\t\treturn `<div class=\"taxes-wrapper\">${taxes_html}</div>`;\n\t}\n\n\tget_grand_total_html(doc) {\n\t\treturn `<div class=\"summary-row-wrapper grand-total\">\n\t\t\t\t\t<div>${__(\"Grand Total\")}</div>\n\t\t\t\t\t<div>${format_currency(doc.grand_total, doc.currency)}</div>\n\t\t\t\t</div>`;\n\t}\n\n\tget_payment_html(doc, payment) {\n\t\treturn `<div class=\"summary-row-wrapper payments\">\n\t\t\t\t\t<div>${__(payment.mode_of_payment)}</div>\n\t\t\t\t\t<div>${format_currency(payment.amount, doc.currency)}</div>\n\t\t\t\t</div>`;\n\t}\n\n\tbind_events() {\n\t\t// Existing event handlers...\n\t\tthis.$summary_container.on(\"click\", \".return-btn\", () => {\n\t\t\tthis.events.process_return(this.doc.name);\n\t\t\tthis.toggle_component(false);\n\t\t\tthis.$component.find(\".no-summary-placeholder\").css(\"display\", \"flex\");\n\t\t\tthis.$summary_wrapper.css(\"display\", \"none\");\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".edit-btn\", () => {\n\t\t\tthis.events.edit_order(this.doc.name);\n\t\t\tthis.toggle_component(false);\n\t\t\tthis.$component.find(\".no-summary-placeholder\").css(\"display\", \"flex\");\n\t\t\tthis.$summary_wrapper.css(\"display\", \"none\");\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".delete-btn\", () => {\n\t\t\tthis.events.delete_order(this.doc.name);\n\t\t\tthis.show_summary_placeholder();\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".new-btn\", () => {\n\t\t\tthis.events.new_order();\n\t\t\tthis.toggle_component(false);\n\t\t\tthis.$component.find(\".no-summary-placeholder\").css(\"display\", \"flex\");\n\t\t\tthis.$summary_wrapper.css(\"display\", \"none\");\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".email-btn\", () => {\n\t\t\tthis.email_dialog.fields_dict.email_id.set_value(this.customer_email);\n\t\t\tthis.email_dialog.show();\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".print-btn\", () => {\n\t\t\tthis.print_receipt();\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".make-btn\", () => {\n\t\t\tthis.make_payment_entry();\n\t\t});\n\n\t\t// Signature event handlers\n\t\tthis.$summary_container.on(\"click\", \".add-btn\", () => {\n\t\t\tthis.show_signature_dialog();\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".add-signature-btn\", () => {\n\t\t\tthis.show_signature_dialog();\n\t\t});\n\n\t\tthis.$summary_container.on(\"click\", \".clear-signature-btn\", () => {\n\t\t\tthis.clear_signature();\n\t\t});\n\t}\n\n\tshow_signature_dialog() {\n\t\t// Set customer name in the dialog\n\t\tthis.signature_dialog.fields_dict.customer_name.set_value(this.doc.customer);\n\t\t\n\t\t// If signature already exists, show it\n\t\tif (this.doc.customer_signature) {\n\t\t\tthis.signature_dialog.fields_dict.signature.set_value(this.doc.customer_signature);\n\t\t}\n\t\t\n\t\tthis.signature_dialog.show();\n\t}\n\n\tsave_signature(signature_data) {\n\t\tif (!signature_data) {\n\t\t\tfrappe.msgprint(__(\"Please provide a signature before saving\"));\n\t\t\treturn;\n\t\t}\n\n\t\t// Save signature to the Sales Invoice document\n\t\tfrappe.call({\n\t\t\tmethod: \"frappe.client.set_value\",\n\t\t\targs: {\n\t\t\t\tdoctype: \"Sales Invoice\",\n\t\t\t\tname: this.doc.name,\n\t\t\t\tfieldname: \"customer_signature\",\n\t\t\t\tvalue: signature_data\n\t\t\t},\n\t\t\tcallback: (r) => {\n\t\t\t\tif (!r.exc) {\n\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\tmessage: __(\"Signature saved successfully\"),\n\t\t\t\t\t\tindicator: \"green\"\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// Hide the signature dialog\n\t\t\t\t\tthis.signature_dialog.hide();\n\t\t\t\t\t\n\t\t\t\t\t// Update the document with signature data\n\t\t\t\t\tthis.doc.customer_signature = signature_data;\n\t\t\t\t\t\n\t\t\t\t\t// Show the signature in the summary\n\t\t\t\t\tthis.show_signature_display();\n\t\t\t\t\t\n\t\t\t\t\t// Refresh the list to update any cached data\n\t\t\t\t\tif (this.events.refresh_list) {\n\t\t\t\t\t\tthis.events.refresh_list();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tshow_signature_display() {\n\t\t// Always show signature section for submitted invoices\n\t\tif (this.doc.docstatus === 1) {\n\t\t\tthis.$signature_display_section.show();\n\t\t\t\n\t\t\t// Show signature if it exists\n\t\t\tif (this.doc.customer_signature) {\n\t\t\t\tthis.$signature_image.attr('src', this.doc.customer_signature).show();\n\t\t\t\tthis.$no_signature_text.hide();\n\t\t\t\tthis.$summary_container.find(\".clear-signature-btn\").show();\n\t\t\t} else {\n\t\t\t\tthis.$signature_image.hide();\n\t\t\t\tthis.$no_signature_text.show();\n\t\t\t\tthis.$summary_container.find(\".clear-signature-btn\").hide();\n\t\t\t}\n\t\t}\n\t}\n\n\tclear_signature() {\n\t\tfrappe.confirm(__(\"Are you sure you want to clear the signature?\"), () => {\n\t\t\tfrappe.call({\n\t\t\t\tmethod: \"frappe.client.set_value\",\n\t\t\t\targs: {\n\t\t\t\t\tdoctype: \"Sales Invoice\",\n\t\t\t\t\tname: this.doc.name,\n\t\t\t\t\tfieldname: \"customer_signature\",\n\t\t\t\t\tvalue: \"\"\n\t\t\t\t},\n\t\t\t\tcallback: (r) => {\n\t\t\t\t\tif (!r.exc) {\n\t\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\t\tmessage: __(\"Signature cleared successfully\"),\n\t\t\t\t\t\t\tindicator: \"green\"\n\t\t\t\t\t\t});\n\t\t\t\t\t\t\n\t\t\t\t\t\t// Update the document\n\t\t\t\t\t\tthis.doc.customer_signature = \"\";\n\t\t\t\t\t\t\n\t\t\t\t\t\t// Refresh the signature display\n\t\t\t\t\t\tthis.show_signature_display();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// Refresh the list to update any cached data\n\t\t\t\t\t\tif (this.events.refresh_list) {\n\t\t\t\t\t\t\tthis.events.refresh_list();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\tmake_payment_entry() {\n\t\tconst outstanding_amount = this.doc.outstanding_amount || 0;\n\t\t\n\t\tif (outstanding_amount <= 0) {\n\t\t\tfrappe.msgprint(__(\"No outstanding amount to pay\"));\n\t\t\treturn;\n\t\t}\n\n\t\tconst payment_dialog = new frappe.ui.Dialog({\n\t\t\ttitle: __(\"Make Payment Entry\"),\n\t\t\tfields: [\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"outstanding_amount\",\n\t\t\t\t\tfieldtype: \"Currency\",\n\t\t\t\t\tlabel: __(\"Outstanding Amount\"),\n\t\t\t\t\tdefault: outstanding_amount,\n\t\t\t\t\tread_only: 1,\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"payment_amount\",\n\t\t\t\t\tfieldtype: \"Currency\",\n\t\t\t\t\tlabel: __(\"Payment Amount\"),\n\t\t\t\t\tdefault: outstanding_amount,\n\t\t\t\t\treqd: 1,\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"mode_of_payment\",\n\t\t\t\t\tfieldtype: \"Link\",\n\t\t\t\t\toptions: \"Mode of Payment\",\n\t\t\t\t\tlabel: __(\"Mode of Payment\"),\n\t\t\t\t\treqd: 1,\n\t\t\t\t\tget_query: () => {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tfilters: { enabled: 1 }\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"reference_no\",\n\t\t\t\t\tfieldtype: \"Data\",\n\t\t\t\t\tlabel: __(\"Reference No./Remarks\"),\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tfieldname: \"reference_date\",\n\t\t\t\t\tfieldtype: \"Date\",\n\t\t\t\t\tlabel: __(\"Reference Date\"),\n\t\t\t\t\tdefault: frappe.datetime.get_today(),\n\t\t\t\t},\n\t\t\t],\n\t\t\tprimary_action: (values) => {\n\t\t\t\tthis.create_payment_entry(values);\n\t\t\t\tpayment_dialog.hide();\n\t\t\t},\n\t\t\tprimary_action_label: __(\"Create Payment Entry\"),\n\t\t});\n\n\t\tpayment_dialog.show();\n\t}\n\n\tcreate_payment_entry(values) {\n\t\tfrappe.call({\n\t\t\tmethod: \"erpnext.accounts.doctype.payment_entry.payment_entry.get_payment_entry\",\n\t\t\targs: {\n\t\t\t\tdt: this.doc.doctype,\n\t\t\t\tdn: this.doc.name,\n\t\t\t},\n\t\t\tcallback: (r) => {\n\t\t\t\tif (r.message) {\n\t\t\t\t\tconst payment_entry = r.message;\n\t\t\t\t\t\n\t\t\t\t\tpayment_entry.paid_amount = values.payment_amount;\n\t\t\t\t\tpayment_entry.received_amount = values.payment_amount;\n\t\t\t\t\tpayment_entry.mode_of_payment = values.mode_of_payment;\n\t\t\t\t\tpayment_entry.reference_no = values.reference_no;\n\t\t\t\t\tpayment_entry.reference_date = values.reference_date;\n\t\t\t\t\t\n\t\t\t\t\tif (payment_entry.references && payment_entry.references.length > 0) {\n\t\t\t\t\t\tpayment_entry.references[0].allocated_amount = values.payment_amount;\n\t\t\t\t\t}\n\n\t\t\t\t\tfrappe.call({\n\t\t\t\t\t\tmethod: \"frappe.client.save\",\n\t\t\t\t\t\targs: {\n\t\t\t\t\t\t\tdoc: payment_entry,\n\t\t\t\t\t\t},\n\t\t\t\t\t\tcallback: (save_r) => {\n\t\t\t\t\t\t\tif (save_r.message) {\n\t\t\t\t\t\t\t\tfrappe.call({\n\t\t\t\t\t\t\t\t\tmethod: \"frappe.client.submit\",\n\t\t\t\t\t\t\t\t\targs: {\n\t\t\t\t\t\t\t\t\t\tdoc: save_r.message,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\tcallback: (submit_r) => {\n\t\t\t\t\t\t\t\t\t\tif (submit_r.message) {\n\t\t\t\t\t\t\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\t\t\t\t\t\t\tmessage: __(\"Payment Entry {0} created and submitted successfully\", [submit_r.message.name]),\n\t\t\t\t\t\t\t\t\t\t\t\tindicator: \"green\",\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\tthis.events.refresh_list();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t}\n\n\tprint_receipt() {\n\t\tconst print_format = \"Sales Invoice Print\";\n\t\t\n\t\tfrappe.utils.print(\n\t\t\tthis.doc.doctype,\n\t\t\tthis.doc.name,\n\t\t\tprint_format,\n\t\t\tthis.doc.letter_head,\n\t\t\tthis.doc.language || frappe.boot.lang\n\t\t);\n\t}\n\n\n\tattach_shortcuts() {\n\t\tconst ctrl_label = frappe.utils.is_mac() ? \"⌘\" : \"Ctrl\";\n\t\tthis.$summary_container.find(\".print-btn\").attr(\"title\", `${ctrl_label}+P`);\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"ctrl+p\",\n\t\t\taction: () => this.$summary_container.find(\".print-btn\").click(),\n\t\t\tcondition: () =>\n\t\t\t\tthis.$component.is(\":visible\") && this.$summary_container.find(\".print-btn\").is(\":visible\"),\n\t\t\tdescription: __(\"Print Receipt\"),\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\t\tthis.$summary_container.find(\".new-btn\").attr(\"title\", `${ctrl_label}+Enter`);\n\t\tfrappe.ui.keys.on(\"ctrl+enter\", () => {\n\t\t\tconst summary_is_visible = this.$component.is(\":visible\");\n\t\t\tif (summary_is_visible && this.$summary_container.find(\".new-btn\").is(\":visible\")) {\n\t\t\t\tthis.$summary_container.find(\".new-btn\").click();\n\t\t\t}\n\t\t});\n\t\tthis.$summary_container.find(\".edit-btn\").attr(\"title\", `${ctrl_label}+E`);\n\t\tfrappe.ui.keys.add_shortcut({\n\t\t\tshortcut: \"ctrl+e\",\n\t\t\taction: () => this.$summary_container.find(\".edit-btn\").click(),\n\t\t\tcondition: () =>\n\t\t\t\tthis.$component.is(\":visible\") && this.$summary_container.find(\".edit-btn\").is(\":visible\"),\n\t\t\tdescription: __(\"Edit Receipt\"),\n\t\t\tpage: cur_page.page.page,\n\t\t});\n\t}\n\n\tsend_email() {\n\t\tconst frm = this.events.get_frm();\n\t\tconst recipients = this.email_dialog.get_values().email_id;\n\t\tconst content = this.email_dialog.get_values().content;\n\t\tconst doc = this.doc || frm.doc;\n\t\tconst print_format = \"Sales Invoice Print\"; // Changed from frm.pos_print_format\n\n\t\tfrappe.call({\n\t\t\tmethod: \"frappe.core.doctype.communication.email.make\",\n\t\t\targs: {\n\t\t\t\trecipients: recipients,\n\t\t\t\tsubject: __(frm.meta.name) + \": \" + doc.name,\n\t\t\t\tcontent: content ? content : __(frm.meta.name) + \": \" + doc.name,\n\t\t\t\tdoctype: doc.doctype,\n\t\t\t\tname: doc.name,\n\t\t\t\tsend_email: 1,\n\t\t\t\tprint_format,\n\t\t\t\tsender_full_name: frappe.user.full_name(),\n\t\t\t\t_lang: doc.language,\n\t\t\t},\n\t\t\tcallback: (r) => {\n\t\t\t\tif (!r.exc) {\n\t\t\t\t\tfrappe.utils.play_sound(\"email\");\n\t\t\t\t\tif (r.message[\"emails_not_sent_to\"]) {\n\t\t\t\t\t\tfrappe.msgprint(\n\t\t\t\t\t\t\t__(\"Email not sent to {0} (unsubscribed / disabled)\", [\n\t\t\t\t\t\t\t\tfrappe.utils.escape_html(r.message[\"emails_not_sent_to\"]),\n\t\t\t\t\t\t\t])\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\t\tmessage: __(\"Email sent successfully.\"),\n\t\t\t\t\t\t\tindicator: \"green\",\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tthis.email_dialog.hide();\n\t\t\t\t} else {\n\t\t\t\t\tfrappe.msgprint(__(\"There were errors while sending email. Please try again.\"));\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t}\n\n\tadd_summary_btns(map) {\n\t\tthis.$summary_btns.html(\"\");\n\t\tmap.forEach((m) => {\n\t\t\tif (m.condition) {\n\t\t\t\tm.visible_btns.forEach((b) => {\n\t\t\t\t\tconst class_name = b.split(\" \")[0].toLowerCase();\n\t\t\t\t\tconst btn = __(b);\n\t\t\t\t\tthis.$summary_btns.append(\n\t\t\t\t\t\t`<div class=\"summary-btn btn btn-default ${class_name}-btn\">${btn}</div>`\n\t\t\t\t\t);\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\tthis.$summary_btns.children().last().removeClass(\"mr-4\");\n\t\t\n\t\t// Debug: Log the buttons being added\n\t\tconsole.log(\"Buttons added:\", map);\n\t\tconsole.log(\"Buttons HTML:\", this.$summary_btns.html());\n\t}\n\n\ttoggle_summary_placeholder(show) {\n\t\tif (show) {\n\t\t\tthis.$summary_wrapper.css(\"display\", \"none\");\n\t\t\tthis.$component.find(\".no-summary-placeholder\").css(\"display\", \"flex\");\n\t\t} else {\n\t\t\tthis.$summary_wrapper.css(\"display\", \"flex\");\n\t\t\tthis.$component.find(\".no-summary-placeholder\").css(\"display\", \"none\");\n\t\t}\n\t}\n\n\tget_condition_btn_map(after_submission) {\n\t\tif (after_submission) {\n\t\t\treturn [{ \n\t\t\t\tcondition: true, \n\t\t\t\tvisible_btns: [\"Print Receipt\", \"Email Receipt\", \"Add Signature\", \"New Order\"] \n\t\t\t}];\n\t\t}\n\n\t\treturn [\n\t\t\t{\n\t\t\t\tcondition: this.doc.docstatus === 0,\n\t\t\t\tvisible_btns: [\"Edit Order\", \"Delete Order\"]\n\t\t\t},\n\t\t\t{\n\t\t\t\tcondition: !this.doc.is_return && this.doc.docstatus === 1 && [\"Partly Paid\", \"Unpaid\"].includes(this.doc.status),\n\t\t\t\tvisible_btns: [\"Make Payment\", \"Print Receipt\", \"Email Receipt\", \"Add Signature\"]\n\t\t\t},\n\t\t\t{\n\t\t\t\tcondition: !this.doc.is_return && this.doc.docstatus === 1 && ![\"Partly Paid\", \"Unpaid\"].includes(this.doc.status),\n\t\t\t\tvisible_btns: [\"Print Receipt\", \"Email Receipt\", \"Return\", \"Add Signature\"]\n\t\t\t},\n\t\t\t{\n\t\t\t\tcondition: this.doc.is_return && this.doc.docstatus === 1,\n\t\t\t\tvisible_btns: [\"Print Receipt\", \"Email Receipt\", \"Add Signature\"]\n\t\t\t}\n\t\t];\n\t}\n\n\tload_summary_of(doc, after_submission = false) {\n\t\tafter_submission\n\t\t\t? this.$component.css(\"grid-column\", \"span 10 / span 10\")\n\t\t\t: this.$component.css(\"grid-column\", \"span 6 / span 6\");\n\n\t\tthis.toggle_summary_placeholder(false);\n\n\t\tthis.doc = doc;\n\t\t\n\t\t// Debug: Log document info\n\t\tconsole.log(\"Loading summary for doc:\", doc.name, \"Status:\", doc.docstatus, \"After submission:\", after_submission);\n\n\t\tthis.attach_document_info(doc);\n\t\tthis.attach_items_info(doc);\n\t\tthis.attach_totals_info(doc);\n\t\tthis.attach_payments_info(doc);\n\n\t\tconst condition_btns_map = this.get_condition_btn_map(after_submission);\n\t\tthis.add_summary_btns(condition_btns_map);\n\n\t\t// Load signature data from database and show signature display\n\t\tthis.load_and_show_signature();\n\n\t\tif (after_submission && this.print_receipt_on_order_complete) {\n\t\t\tthis.print_receipt();\n\t\t}\n\t}\n\n\tload_and_show_signature() {\n\t// Always fetch the latest signature data from database\n\tfrappe.call({\n\t\tmethod: \"frappe.client.get_value\",\n\t\targs: {\n\t\t\tdoctype: \"Sales Invoice\",\n\t\t\tname: this.doc.name,\n\t\t\tfieldname: \"customer_signature\"\n\t\t},\n\t\tcallback: (r) => {\n\t\t\tif (r.message && r.message.customer_signature) {\n\t\t\t\t// Update the doc object with the signature\n\t\t\t\tthis.doc.customer_signature = r.message.customer_signature;\n\t\t\t}\n\t\t\t// Show signature display regardless (will show \"no signature\" if empty)\n\t\t\tthis.show_signature_display();\n\t\t}\n\t});\n\t}\n\n\tattach_document_info(doc) {\n\t\tfrappe.db.get_value(\"Customer\", this.doc.customer, \"email_id\").then(({ message }) => {\n\t\t\tthis.customer_email = message.email_id || \"\";\n\t\t\tconst upper_section_dom = this.get_upper_section_html(doc);\n\t\t\tthis.$upper_section.html(upper_section_dom);\n\t\t});\n\t}\n\n\tattach_items_info(doc) {\n\t\tthis.$items_container.html(\"\");\n\t\tdoc.items.forEach((item) => {\n\t\t\tconst item_dom = this.get_item_html(doc, item);\n\t\t\tthis.$items_container.append(item_dom);\n\t\t\tthis.set_dynamic_rate_header_width();\n\t\t});\n\t}\n\n\tset_dynamic_rate_header_width() {\n\t\tconst rate_cols = Array.from(this.$items_container.find(\".item-rate-disc\"));\n\t\tthis.$items_container.find(\".item-rate-disc\").css(\"width\", \"\");\n\t\tlet max_width = rate_cols.reduce((max_width, elm) => {\n\t\t\tif ($(elm).width() > max_width) max_width = $(elm).width();\n\t\t\treturn max_width;\n\t\t}, 0);\n\n\t\tmax_width += 1;\n\t\tif (max_width == 1) max_width = \"\";\n\n\t\tthis.$items_container.find(\".item-rate-disc\").css(\"width\", max_width);\n\t}\n\n\tattach_payments_info(doc) {\n\t\tthis.$payment_container.html(\"\");\n\t\t\n\t\tif (doc.payments && doc.payments.length) {\n\t\t\tdoc.payments.forEach((p) => {\n\t\t\t\tif (p.amount) {\n\t\t\t\t\tconst payment_dom = this.get_payment_html(doc, p);\n\t\t\t\t\tthis.$payment_container.append(payment_dom);\n\t\t\t\t}\n\t\t\t});\n\t\t} else if (doc.advances && doc.advances.length) {\n\t\t\tdoc.advances.forEach((advance) => {\n\t\t\t\tif (advance.allocated_amount) {\n\t\t\t\t\tconst payment_dom = this.get_payment_html(doc, {\n\t\t\t\t\t\tmode_of_payment: advance.reference_type + \" - \" + advance.reference_name,\n\t\t\t\t\t\tamount: advance.allocated_amount,\n\t\t\t\t\t});\n\t\t\t\t\tthis.$payment_container.append(payment_dom);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\t\n\t\tif (doc.redeem_loyalty_points && doc.loyalty_amount) {\n\t\t\tconst payment_dom = this.get_payment_html(doc, {\n\t\t\t\tmode_of_payment: \"Loyalty Points\",\n\t\t\t\tamount: doc.loyalty_amount,\n\t\t\t});\n\t\t\tthis.$payment_container.append(payment_dom);\n\t\t}\n\n\t\tif (doc.status === \"Partly Paid\" || doc.status === \"Unpaid\" && doc.outstanding_amount > 0) {\n\t\t\tconst outstanding_dom = `<div class=\"summary-row-wrapper payments outstanding\">\n\t\t\t\t<div class=\"text-danger\">${__(\"Outstanding Amount\")}</div>\n\t\t\t\t<div class=\"text-danger\">${format_currency(doc.outstanding_amount, doc.currency)}</div>\n\t\t\t</div>`;\n\t\t\tthis.$payment_container.append(outstanding_dom);\n\t\t}\n\t}\n\n\tattach_totals_info(doc) {\n\t\tthis.$totals_container.html(\"\");\n\n\t\tconst net_total_dom = this.get_net_total_html(doc);\n\t\tconst taxes_dom = this.get_taxes_html(doc);\n\t\tconst discount_dom = this.get_discount_html(doc);\n\t\tconst grand_total_dom = this.get_grand_total_html(doc);\n\t\tthis.$totals_container.append(net_total_dom);\n\t\tthis.$totals_container.append(taxes_dom);\n\t\tthis.$totals_container.append(discount_dom);\n\t\tthis.$totals_container.append(grand_total_dom);\n\t}\n\n\tshow_summary_placeholder() {\n\t\tthis.toggle_summary_placeholder(true);\n\t}\n\n\ttoggle_component(show) {\n\t\tshow ? this.$component.css(\"display\", \"flex\") : this.$component.css(\"display\", \"none\");\n\t}\n};", "erpnext.SalesInvoiceUI = erpnext.SalesInvoiceUI || {};\n\nerpnext.SalesInvoiceUI.Controller = class {\n\tconstructor(wrapper) {\n\t\tthis.wrapper = $(wrapper).find(\".layout-main-section\");\n\t\tthis.page = wrapper.page;\n\t\tthis.check_opening_entry();\n\t}\n\n\n\tfetch_opening_entry() {\n\t\treturn frappe.call(\"erpnext.selling.page.point_of_sale.point_of_sale.check_opening_entry\", {\n\t\t\tuser: frappe.session.user,\n\t\t});\n\t}\n\n\tcheck_opening_entry() {\n\t\tthis.fetch_opening_entry().then((r) => {\n\t\t\tif (r.message.length) {\n\t\t\t\tthis.prepare_app_defaults(r.message[0]);\n\t\t\t} else {\n\t\t\t\tthis.create_opening_voucher();\n\t\t\t}\n\t\t});\n\t}\n\n\tasync create_opening_voucher() {\n\tconst me = this;\n\t\n\tconst check_existing_opening = async () => {\n\t\ttry {\n\t\t\tconst result = await frappe.call({\n\t\t\t\tmethod: \"frappe.client.get_list\",\n\t\t\t\targs: {\n\t\t\t\t\tdoctype: \"POS Opening Entry\",\n\t\t\t\t\tfilters: {\n\t\t\t\t\t\tuser: frappe.session.user,\n\t\t\t\t\t\tdocstatus: 1,\n\t\t\t\t\t\tstatus: \"Open\"\n\t\t\t\t\t},\n\t\t\t\t\tfields: [\"name\", \"pos_profile\", \"company\"],\n\t\t\t\t\tlimit: 1,\n\t\t\t\t\torder_by: \"creation desc\"\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\treturn result.message && result.message.length > 0 ? result.message[0] : null;\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Error checking existing POS Opening Entry:\", error);\n\t\t\treturn null;\n\t\t}\n\t};\n\n\tconst existing_opening = await check_existing_opening();\n\t\n\tif (existing_opening) {\n\t\tfrappe.show_alert({\n\t\t\tmessage: __(\"Using existing POS Opening Entry: {0}\", [existing_opening.name]),\n\t\t\tindicator: \"green\"\n\t\t});\n\t\t\n\t\tme.prepare_app_defaults(existing_opening);\n\t\treturn;\n\t}\n\n\tconst table_fields = [\n\t\t{\n\t\t\tfieldname: \"mode_of_payment\",\n\t\t\tfieldtype: \"Link\",\n\t\t\tin_list_view: 1,\n\t\t\tlabel: __(\"Mode of Payment\"),\n\t\t\toptions: \"Mode of Payment\",\n\t\t\treqd: 1,\n\t\t},\n\t\t{\n\t\t\tfieldname: \"opening_amount\",\n\t\t\tfieldtype: \"Currency\",\n\t\t\tin_list_view: 1,\n\t\t\tlabel: __(\"Opening Amount\"),\n\t\t\toptions: \"company:company_currency\",\n\t\t\tonchange: function () {\n\t\t\t\tdialog.fields_dict.balance_details.df.data.some((d) => {\n\t\t\t\t\tif (d.idx == this.doc.idx) {\n\t\t\t\t\t\td.opening_amount = this.value;\n\t\t\t\t\t\tdialog.fields_dict.balance_details.grid.refresh();\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t},\n\t];\n\n\n\n\tconst fetch_pos_payment_methods = () => {\n\t\tconst pos_profile = dialog.fields_dict.pos_profile.get_value();\n\t\tif (!pos_profile) return;\n\t\tfrappe.db.get_doc(\"POS Profile\", pos_profile).then(({ payments }) => {\n\t\t\tdialog.fields_dict.balance_details.df.data = [];\n\t\t\tpayments.forEach((pay) => {\n\t\t\t\tconst { mode_of_payment } = pay;\n\t\t\t\tdialog.fields_dict.balance_details.df.data.push({ mode_of_payment, opening_amount: \"0\" });\n\t\t\t});\n\t\t\tdialog.fields_dict.balance_details.grid.refresh();\n\t\t});\n\t};\n\n\tconst dialog = new frappe.ui.Dialog({\n\t\ttitle: __(\"Create POS Opening Entry\"),\n\t\tstatic: true,\n\t\tfields: [\n\t\t\t{\n\t\t\t\tfieldtype: \"Link\",\n\t\t\t\tlabel: __(\"Company\"),\n\t\t\t\tdefault: frappe.defaults.get_default(\"company\"),\n\t\t\t\toptions: \"Company\",\n\t\t\t\tfieldname: \"company\",\n\t\t\t\treqd: 1,\n\t\t\t},\n\t\t\t{\n\t\t\t\tfieldtype: \"Link\",\n\t\t\t\tlabel: __(\"POS Profile\"),\n\t\t\t\toptions: \"POS Profile\",\n\t\t\t\tfieldname: \"pos_profile\",\n\t\t\t\treqd: 1,\n\t\t\t\tget_query: () => pos_profile_query(),\n\t\t\t\tonchange: () => fetch_pos_payment_methods(),\n\t\t\t},\n\t\t\t{\n\t\t\t\tfieldname: \"balance_details\",\n\t\t\t\tfieldtype: \"Table\",\n\t\t\t\tlabel: __(\"Opening Balance Details\"),\n\t\t\t\tcannot_add_rows: false,\n\t\t\t\tin_place_edit: true,\n\t\t\t\treqd: 1,\n\t\t\t\tdata: [],\n\t\t\t\tfields: table_fields,\n\t\t\t},\n\t\t],\n\t\tprimary_action: async function ({ company, pos_profile, balance_details }) {\n\t\t\tif (!balance_details.length) {\n\t\t\t\tfrappe.show_alert({\n\t\t\t\t\tmessage: __(\"Please add Mode of payments and opening balance details.\"),\n\t\t\t\t\tindicator: \"red\",\n\t\t\t\t});\n\t\t\t\treturn frappe.utils.play_sound(\"error\");\n\t\t\t}\n\n\t\t\tbalance_details = balance_details.filter((d) => d.mode_of_payment);\n\n\t\t\tconst method = \"erpnext.selling.page.point_of_sale.point_of_sale.create_opening_voucher\";\n\t\t\tconst res = await frappe.call({\n\t\t\t\tmethod,\n\t\t\t\targs: { pos_profile, company, balance_details },\n\t\t\t\tfreeze: true,\n\t\t\t});\n\t\t\t!res.exc && me.prepare_app_defaults(res.message);\n\t\t\tdialog.hide();\n\t\t},\n\t\tprimary_action_label: __(\"Submit\"),\n\t});\n\n\tdialog.show();\n\n\tconst pos_profile_query = () => {\n\t\treturn {\n\t\t\tquery: \"erpnext.accounts.doctype.pos_profile.pos_profile.pos_profile_query\",\n\t\t\tfilters: { company: dialog.fields_dict.company.get_value() },\n\t\t};\n\t};\n}\n\n\tasync prepare_app_defaults(data) {\n\t\tthis.pos_opening = data.name;\n\t\tthis.company = data.company;\n\t\tthis.pos_profile = data.pos_profile;\n\t\tthis.pos_opening_time = data.period_start_date;\n\t\tthis.item_stock_map = {};\n\t\tthis.settings = {};\n\n\t\tfrappe.db.get_value(\"Stock Settings\", undefined, \"allow_negative_stock\").then(({ message }) => {\n\t\t\tthis.allow_negative_stock = flt(message.allow_negative_stock) || false;\n\t\t});\n\n\t\tfrappe.call({\n\t\t\tmethod: \"erpnext.selling.page.point_of_sale.point_of_sale.get_pos_profile_data\",\n\t\t\targs: { pos_profile: this.pos_profile },\n\t\t\tcallback: (res) => {\n\t\t\t\tconst profile = res.message;\n\t\t\t\tObject.assign(this.settings, profile);\n\t\t\t\tthis.settings.customer_groups = profile.customer_groups.map((group) => group.name);\n\t\t\t\tthis.make_app();\n\t\t\t},\n\t\t});\n\t}\n\n\tset_opening_entry_status() {\n\t\tthis.page.set_title_sub(\n\t\t\t`<span class=\"indicator orange\">\n\t\t\t\t<a class=\"text-muted\" href=\"#Form/POS%20Opening%20Entry/${this.pos_opening}\">\n\t\t\t\t\tOpened at ${frappe.datetime.str_to_user(this.pos_opening_time)}\n\t\t\t\t</a>\n\t\t\t</span>`\n\t\t);\n\t}\n\n\tmake_app() {\n\t\tthis.prepare_dom();\n\t\tthis.prepare_components();\n\t\tthis.prepare_menu();\n\t\tthis.prepare_fullscreen_btn();\n\t\tthis.make_new_invoice();\n\t}\n\n\tprepare_dom() {\n\t\tthis.wrapper.append(`<div class=\"point-of-sale-app\"></div>`);\n\t\tthis.$components_wrapper = this.wrapper.find(\".point-of-sale-app\");\n\t}\n\n\tprepare_components() {\n\t\tthis.init_item_selector();\n\t\tthis.init_item_details();\n\t\tthis.init_item_cart();\n\t\tthis.init_payments();\n\t\tthis.init_recent_order_list();\n\t\tthis.init_order_summary();\n\t}\n\n\tprepare_menu() {\n\t\tthis.page.clear_menu();\n\n\t\tthis.page.add_menu_item(__(\"Open Form View\"), this.open_form_view.bind(this), false, \"Ctrl+F\");\n\n\t\tthis.page.add_menu_item(\n\t\t\t__(\"Toggle Recent Orders\"),\n\t\t\tthis.toggle_recent_order.bind(this),\n\t\t\tfalse,\n\t\t\t\"Ctrl+O\"\n\t\t);\n\n\t\tthis.page.add_menu_item(__(\"Save as Draft\"), this.save_draft_invoice.bind(this), false, \"Ctrl+S\");\n\n\t\tthis.page.add_menu_item(__(\"Close the POS\"), this.close_pos.bind(this), false, \"Shift+Ctrl+C\");\n\t}\n\n\tprepare_fullscreen_btn() {\n\t\tthis.page.page_actions.find(\".custom-actions\").empty();\n\t\tthis.create_custom_button_container();\n\t\tthis.create_top_bar_buttons();\n\n\t\tthis.page.add_button(__(\"Full Screen\"), null, {\n\t\t\tbtn_class: \"btn-default fullscreen-btn\"\n\t\t});\n\n\t\tthis.bind_fullscreen_events();\n\t}\n\n\tcreate_top_bar_buttons() {\n\t\tthis.page.wrapper.find('.pos-top-buttons').remove();\n\t\t\n\t\tconst topButtonsHtml = `\n\t\t\t<div class=\"pos-top-buttons\" style=\"display: inline-flex; gap: 8px; margin-left: 10px;\">\n\t\t\t\t<button class=\"btn btn-default btn-sm stock-ledger-btn\" title=\"Stock Ledger\">\n\t\t\t\t\t<i class=\"fa fa-list\"></i> Stock Ledger\n\t\t\t\t</button>\n\t\t\t\t<button class=\"btn btn-default btn-sm add-location-btn\" title=\"Add Location\">\n\t\t\t\t\t<i class=\"fa fa-plus\"></i> Add Location\n\t\t\t\t</button>\n\t\t\t\t<button class=\"btn btn-default btn-sm return-sales-btn\" title=\"Add Sales\">\n\t\t\t\t\tReturn Sales\n\t\t\t\t</button>\n\t\t\t</div>\n\t\t`;\n\t\t\n\t\tconst refreshBtn = this.page.wrapper.find('.new-order-btn');\n\t\tif (refreshBtn.length) {\n\t\t\trefreshBtn.after(topButtonsHtml);\n\t\t} else {\n\t\t\tthis.page.page_actions.append(topButtonsHtml);\n\t\t}\n\n\t\t$(\".stock-ledger-btn\").on(\"click\", () => {\n\t\t\tthis.store_navigation_state();\n\t\t\twindow.location.href = \"/app/query-report/Stock Ledger\";\n\n\t\t\t//we are not using frappe.set_route here because it messes with page on load\n\t\t\t// Contact Ayush for more clarification on this.\n\t\t\t// frappe.set_route(\"query-report\", \"Stock Ledger\");\n\t\t});\n\n\t\t$(\".add-location-btn\").on(\"click\", () => {\n\t\t\tthis.store_navigation_state();\n\n\t\t\twindow.location.href = \"/app/location/new-location\";\n\t\t\t// frappe.new_doc(\"Location\");\n\t\t});\n\n\t\t$(\".return-sales-btn\").on(\"click\", () => {\n\t\t\tthis.store_navigation_state();\n\n\t\t\twindow.location.href = \"/app/sales-invoice/new-sales-invoice\";\n\t\t\t// frappe.new_doc(\"Location\");\n\t\t});\n\t\t\n\t\tthis.add_top_button_styles();\n\t}\n\n\tstore_navigation_state() {\n\t\tsessionStorage.setItem('pos_navigated_away', 'true');\n\t\tsessionStorage.setItem('pos_return_time', Date.now().toString());\n\t}\n\n\tadd_top_button_styles() {\n\t\tconst topStyles = `\n\t\t\t<style>\n\t\t\t\t.pos-top-buttons {\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tgap: 8px;\n\t\t\t\t\tmargin-left: 10px;\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t}\n\n\t\t\t\t.pos-top-buttons .btn {\n\t\t\t\t\tpadding: 4px 8px;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\theight: 30px;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tgap: 4px;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\t\tborder: 1px solid #dee2e6;\n\t\t\t\t\tcolor: #495057;\n\t\t\t\t\ttransition: all 0.2s ease;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t}\n\n\t\t\t\t.pos-top-buttons .btn:hover {\n\t\t\t\t\tbackground-color: #e9ecef;\n\t\t\t\t\ttransform: translateY(-1px);\n\t\t\t\t}\n\n\t\t\t\t.pos-top-buttons .btn i {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t}\n\n\t\t\t\t/* Responsive adjustments */\n\t\t\t\t@media (max-width: 768px) {\n\t\t\t\t\t.pos-top-buttons .btn {\n\t\t\t\t\t\tpadding: 3px 6px;\n\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t\theight: 28px;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.pos-top-buttons .btn i {\n\t\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t@media (max-width: 480px) {\n\t\t\t\t\t.pos-top-buttons {\n\t\t\t\t\t\tgap: 4px;\n\t\t\t\t\t\tmargin-left: 5px;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.pos-top-buttons .btn {\n\t\t\t\t\t\tpadding: 2px 4px;\n\t\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\t\theight: 26px;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.pos-top-buttons .btn i {\n\t\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t</style>\n\t\t`;\n\t\t\n\t\t$('#pos-top-button-styles').remove();\n\t\t\n\t\t$('head').append(`<div id=\"pos-top-button-styles\">${topStyles}</div>`);\n\t}\n\n\tcreate_custom_button_container() {\n\tthis.page.wrapper.find('.pos-custom-buttons').remove();\n\t\n\tconst buttonContainerHtml = `\n\t\t<div class=\"pos-custom-buttons\" style=\"\n\t\t\tposition: relative;\n\t\t\ttop: 10%;\n\t\t\tleft: 50%;\n\t\t\ttransform: translateX(-50%);\n\t\t\tz-index: 1000;\n\t\t\tdisplay: flex;\n\t\t\tgap: 20px;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\t\">\t\n\n\t\t\t<div>\n\t\t\t\t<button class=\"fa fa-refresh btn-secondary new-order-btn\">\n\t\t\t\t</button>\n\t\t\t</div>\n\n\t\t\t<div class=\"pos-filter-buttons\" style=\"display: flex; gap: 20px;\">\n\t\t\t\t<button class=\"btn btn-default partly-paid-btn pos-filter-btn\" data-filter=\"Partly Paid\">\n\t\t\t\t\t${__(\"Outstanding\")}\n\t\t\t\t</button>\n\t\t\t\t<button class=\"btn btn-default paid-btn pos-filter-btn\" data-filter=\"Paid\">\n\t\t\t\t\t${__(\"Paid\")}\n\t\t\t\t</button>\n\t\t\t</div>\n\n\n\t\t</div>\n\t`;\n\t\n\tthis.page.wrapper.prepend(buttonContainerHtml);\n\n\t$(\".new-order-btn\").on(\"click\", function () {\n\t\tsessionStorage.removeItem('pos_navigated_away');\n\t\tsessionStorage.removeItem('pos_return_time');\n\t\twindow.location.reload();\n\t});\n\n\t\n\tthis.add_responsive_styles();\n\t\n\tthis.active_filter_btn = null;\n\t\n\tthis.page.wrapper.on('click', '.pos-filter-btn', (e) => {\n\t\tconst button = e.target;\n\t\tconst status = $(button).data('filter');\n\t\tthis.handle_filter_button_click(button, status);\n\t});\n\n\t}\n\n\tadd_responsive_styles() {\n\t// Add responsive CSS styles\n\tconst styles = `\n\t\t<style>\n\t\t\t.new-order-btn {\n\t\t\t\tposition: fixed;\n\t\t\t\tleft:10px;\n\t\t\t\ttop:13%;\n\t\t\t\tz-index: 1000;\n\t\t\t\tbackground-color: #ccc;\n\t\t\t\tcolor: #333;\n\t\t\t\tpadding: 7px 10px;\n\t\t\t\tfont-size: 13px;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 4px;\n\t\t\t\tfont-weight: 500;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tgap: 4px;\n\t\t\t\ttransition: background-color 0.2s ease;\n\t\t\t}\n\n\t\t\t.new-order-btn:hover {\n\t\t\t\tbackground-color: #bbb;\n\t\t\t}\n\t\t\t/* Base styles for desktop */\n\t\t\t.pos-custom-buttons {\n\t\t\t\tposition: fixed;\n\t\t\t\ttop: 70px;\n\t\t\t\tleft: 50%;\n\t\t\t\ttransform: translateX(-50%);\n\t\t\t\tz-index: 1000;\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 25px;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\t\t\tpadding: 12px 20px;\n\t\t\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\t\t\t\tbackdrop-filter: blur(10px);\n\t\t\t\tborder: 1px solid rgba(0, 0, 0, 0.1);\n\t\t\t\tmax-width: auto;\n\t\t\t}\t\t\n\n\t\t\t.pos-custom-buttons .btn {\n\t\t\t\tpadding: 8px 16px;\n\t\t\t\tfont-size: 14px;\n\t\t\t\tmin-width: 100px;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tborder-radius: 6px;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t/* Ensure main content doesn't overlap */\n\t\t\t.layout-main-section {\n\t\t\t\tmargin-top: 5px !important;\n\t\t\t}\n\n\t\t\t/* Large tablets and small laptops (1024px and below) */\n\t\t\t@media (max-width: 1024px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\ttop: 65px;\n\t\t\t\t\tgap: 14px;\n\t\t\t\t\tpadding: 10px 18px;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 7px 14px;\n\t\t\t\t\tfont-size: 13px;\n\t\t\t\t\tmin-width: 90px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Tablets (768px and below) */\n\t\t\t@media (max-width: 768px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\ttop: 80px;\n\t\t\t\t\tgap: 12px;\n\t\t\t\t\tpadding: 8px 16px;\n\t\t\t\t\tmax-width: 95%;\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 6px 12px;\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\tmin-width: 80px;\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 5px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Small tablets and large phones (640px and below) */\n\t\t\t@media (max-width: 640px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\ttop: 65px;\n\t\t\t\t\tgap: 10px;\n\t\t\t\t\tpadding: 6px 12px;\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 5px 10px;\n\t\t\t\t\tfont-size: 11px;\n\t\t\t\t\tmin-width: 70px;\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 5px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Mobile phones (480px and below) */\n\t\t\t@media (max-width: 480px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\ttop: 15%;\n\t\t\t\t\tgap: 8px;\n\t\t\t\t\tpadding: 6px 10px;\n\t\t\t\t\tflex-wrap: nowrap; /* Keep buttons in one row */\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 4px 8px;\n\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\tmin-width: 60px;\n\t\t\t\t\tflex: 1; /* Equal width distribution */\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 5px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Small mobile phones (400px and below) */\n\t\t\t@media (max-width: 400px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\ttop: 15%;\n\t\t\t\t\tgap: 6px;\n\t\t\t\t\tpadding: 4px 8px;\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 3px 6px;\n\t\t\t\t\tfont-size: 9px;\n\t\t\t\t\tmin-width: 50px;\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 5px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Very small screens (360px and below) */\n\t\t\t@media (max-width: 360px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\ttop: auto;\n\t\t\t\t\tleft: auto;\n\t\t\t\t\ttransform: none;\n\t\t\t\t\tmargin: 8px auto;\n\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\tgap: 4px;\n\t\t\t\t\tpadding: 4px 6px;\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 3px 5px;\n\t\t\t\t\tfont-size: 8px;\n\t\t\t\t\tmin-width: 45px;\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 5px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Extra small screens (320px and below) */\n\t\t\t@media (max-width: 320px) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tgap: 3px;\n\t\t\t\t\tpadding: 3px 5px;\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 2px 4px;\n\t\t\t\t\tfont-size: 8px;\n\t\t\t\t\tmin-width: 40px;\n\t\t\t\t\tmargin: 1px;\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 25px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Landscape orientation adjustments for mobile */\n\t\t\t@media (max-height: 500px) and (orientation: landscape) {\n\t\t\t\t.pos-custom-buttons {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\ttop: auto;\n\t\t\t\t\tmargin: 5px auto;\n\t\t\t\t\tpadding: 3px 8px;\n\t\t\t\t}\n\n\t\t\t\t.pos-custom-buttons .btn {\n\t\t\t\t\tpadding: 2px 6px;\n\t\t\t\t\tfont-size: 9px;\n\t\t\t\t}\n\n\t\t\t\t.layout-main-section {\n\t\t\t\t\tmargin-top: 10px !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Hover effects for larger screens */\n\t\t\t@media (min-width: 769px) {\n\t\t\t\t.pos-custom-buttons .btn:hover {\n\t\t\t\t\ttransform: translateY(-1px);\n\t\t\t\t\tbox-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n\t\t\t\t\ttransition: all 0.2s ease;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t/* Active button styling */\n\t\t\t.pos-custom-buttons .btn-primary {\n\t\t\t\tbackground-color: #007bff;\n\t\t\t\tborder-color: #007bff;\n\t\t\t\tcolor: white;\n\t\t\t}\n\n\t\t\t.pos-custom-buttons .btn-default {\n\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\tborder-color: #dee2e6;\n\t\t\t\tcolor: #495057;\n\t\t\t}\n\t\t</style>\n\t`;\n\n\t\n\t// Remove existing styles if any\n\t$('#pos-custom-styles').remove();\n\t\n\t// Add styles to head\n\t$('head').append(`<div id=\"pos-custom-styles\">${styles}</div>`);\n\t}\n\n\n\tbind_fullscreen_events() {\n\t\tthis.$fullscreen_btn = this.page.page_actions.find(\".fullscreen-btn\");\n\n\t\tthis.$fullscreen_btn.on(\"click\", function () {\n\t\t\tif (!document.fullscreenElement) {\n\t\t\t\tdocument.documentElement.requestFullscreen();\n\t\t\t} else if (document.exitFullscreen) {\n\t\t\t\tdocument.exitFullscreen();\n\t\t\t}\n\t\t});\n\n\t\t$(document).on(\"fullscreenchange\", this.handle_fullscreen_change_event.bind(this));\n\t}\n\n\thandle_fullscreen_change_event() {\n\t\tlet enable_fullscreen_label = __(\"Full Screen\");\n\t\tlet exit_fullscreen_label = __(\"Exit Full Screen\");\n\n\t\tif (document.fullscreenElement) {\n\t\t\tthis.$fullscreen_btn[0].innerText = exit_fullscreen_label;\n\t\t} else {\n\t\t\tthis.$fullscreen_btn[0].innerText = enable_fullscreen_label;\n\t\t}\n\t}\n\n\topen_form_view() {\n\t\tfrappe.model.sync(this.frm.doc);\n\t\tfrappe.set_route(\"Form\", this.frm.doc.doctype, this.frm.doc.name);\n\t}\n\n\thandle_filter_button_click(button, status) {\n\t\tconsole.log(`Filter button clicked: ${status}`);\n\t\t\n\t\t// Update button states\n\t\tthis.update_button_states(button);\n\t\t\n\t\t// If clicking the same button twice, toggle off\n\t\tif (this.active_filter_btn === button) {\n\t\t\tthis.toggle_recent_order_list(false);\n\t\t\tthis.active_filter_btn = null;\n\t\t\tthis.remove_button_active_states();\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// Set the new active button\n\t\tthis.active_filter_btn = button;\n\t\t\n\t\t// Show the recent order list with the filter\n\t\tthis.toggle_recent_order_with_filter(status);\n\t}\n\n\n\tupdate_button_states(activeButton) {\n\t\t// Remove active state from all filter buttons\n\t\tthis.page.wrapper.find(\".pos-filter-btn\")\n\t\t\t.removeClass(\"btn-primary\")\n\t\t\t.addClass(\"btn-default\");\n\t\t\n\t\t// Add active state to clicked button\n\t\t$(activeButton).removeClass(\"btn-default\").addClass(\"btn-primary\");\n\t}\n\n\n\ttoggle_recent_order_with_filter(status) {\n\t\tconsole.log(`Toggling recent order with filter: ${status}`);\n\t\t\n\t\t// Ensure components are properly initialized\n\t\tif (!this.recent_order_list) {\n\t\t\tconsole.error(\"Recent order list not initialized\");\n\t\t\treturn;\n\t\t}\n\t\t\n\t\t// First, ensure other components are hidden\n\t\tthis.toggle_components(false);\n\t\t\n\t\t// Show the recent order list\n\t\tthis.recent_order_list.toggle_component(true);\n\t\tthis.order_summary.toggle_component(true);\n\t\t\n\t\t// Wait for component to be fully visible before setting filter\n\t\tsetTimeout(() => {\n\t\t\tif (this.recent_order_list.$component.is(\":visible\")) {\n\t\t\t\tthis.recent_order_list.set_filter_and_refresh(status);\n\t\t\t} else {\n\t\t\t\tconsole.error(\"Recent order list component not visible after toggle\");\n\t\t\t}\n\t\t}, 100);\n\t}\n\n\ttoggle_recent_order() {\n\t\tconst show = this.recent_order_list.$component.is(\":hidden\");\n\t\t\n\t\tif (!show) {\n\t\t\t// If hiding, also reset button states\n\t\t\tthis.active_filter_btn = null;\n\t\t\tthis.remove_button_active_states();\n\t\t\t// Reset filters when hiding\n\t\t\tthis.recent_order_list.reset_filters();\n\t\t}\n\t\t\n\t\tthis.toggle_recent_order_list(show);\n\t}\n\n\tsave_draft_invoice() {\n\t\tif (!this.$components_wrapper.is(\":visible\")) return;\n\n\t\tif (this.frm.doc.items.length == 0) {\n\t\t\tfrappe.show_alert({\n\t\t\t\tmessage: __(\"You must add atleast one item to save it as draft.\"),\n\t\t\t\tindicator: \"red\",\n\t\t\t});\n\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.frm\n\t\t\t.save(undefined, undefined, undefined, () => {\n\t\t\t\tfrappe.show_alert({\n\t\t\t\t\tmessage: __(\"There was an error saving the document.\"),\n\t\t\t\t\tindicator: \"red\",\n\t\t\t\t});\n\t\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\t})\n\t\t\t.then(() => {\n\t\t\t\tfrappe.run_serially([\n\t\t\t\t\t() => frappe.dom.freeze(),\n\t\t\t\t\t() => this.make_new_invoice(),\n\t\t\t\t\t() => frappe.dom.unfreeze(),\n\t\t\t\t]);\n\t\t\t});\n\t}\n\n\tclose_pos() {\n\t\tif (!this.$components_wrapper.is(\":visible\")) return;\n\n\t\tlet voucher = frappe.model.get_new_doc(\"POS Closing Entry\");\n\t\tvoucher.pos_profile = this.frm.doc.pos_profile;\n\t\tvoucher.user = frappe.session.user;\n\t\tvoucher.company = this.frm.doc.company;\n\t\tvoucher.pos_opening_entry = this.pos_opening;\n\t\tvoucher.period_end_date = frappe.datetime.now_datetime();\n\t\tvoucher.posting_date = frappe.datetime.now_date();\n\t\tvoucher.posting_time = frappe.datetime.now_time();\n\t\tfrappe.set_route(\"Form\", \"POS Closing Entry\", voucher.name);\n\t}\n\n\tinit_item_selector() {\n\t\t// Ensure ItemSelector class exists\n\t\tif (!erpnext.SalesInvoiceUI.ItemSelector) {\n\t\t\tconsole.error(\"ItemSelector class not found\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.item_selector = new erpnext.SalesInvoiceUI.ItemSelector({\n\t\t\twrapper: this.$components_wrapper,\n\t\t\tpos_profile: this.pos_profile,\n\t\t\tsettings: this.settings,\n\t\t\tevents: {\n\t\t\t\titem_selected: (args) => this.on_cart_update(args),\n\t\t\t\tget_frm: () => this.frm || {},\n\t\t\t},\n\t\t});\n\t}\n\n\tinit_item_cart() {\n\t\t// Ensure ItemCart class exists\n\t\tif (!erpnext.SalesInvoiceUI.ItemCart) {\n\t\t\tconsole.error(\"ItemCart class not found\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.cart = new erpnext.SalesInvoiceUI.ItemCart({\n\t\t\twrapper: this.$components_wrapper,\n\t\t\tsettings: this.settings,\n\t\t\tevents: {\n\t\t\t\tget_frm: () => this.frm,\n\n\t\t\t\tcart_item_clicked: (item) => {\n\t\t\t\t\tconst item_row = this.get_item_from_frm(item);\n\t\t\t\t\tthis.item_details.toggle_item_details_section(item_row);\n\t\t\t\t},\n\n\t\t\t\tnumpad_event: (value, action) => this.update_item_field(value, action),\n\n\t\t\t\tcheckout: () => this.save_and_checkout(),\n\n\t\t\t\tedit_cart: () => this.payment.edit_cart(),\n\n\t\t\t\tcustomer_details_updated: (details) => {\n\t\t\t\t\tthis.item_selector.load_items_data();\n\t\t\t\t\tthis.customer_details = details;\n\t\t\t\t\tthis.payment.render_loyalty_points_payment_mode();\n\t\t\t\t},\n\t\t\t},\n\t\t});\n\t}\n\n\tinit_item_details() {\n\t\tif (!erpnext.SalesInvoiceUI.ItemDetails) {\n\t\t\tconsole.error(\"ItemDetails class not found\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.item_details = new erpnext.SalesInvoiceUI.ItemDetails({\n\t\t\twrapper: this.$components_wrapper,\n\t\t\tsettings: this.settings,\n\t\t\tevents: {\n\t\t\t\tget_frm: () => this.frm,\n\n\t\t\t\ttoggle_item_selector: (minimize) => {\n\t\t\t\t\tthis.item_selector.toggle_component(!minimize);\n\t\t\t\t\tthis.cart.toggle_numpad(minimize);\n\t\t\t\t},\n\n\t\t\t\tform_updated: (item, field, value) => {\n\t\t\t\t\tconst item_row = frappe.model.get_doc(item.doctype, item.name);\n\t\t\t\t\tif (item_row && item_row[field] != value) {\n\t\t\t\t\t\tconst args = {\n\t\t\t\t\t\t\tfield,\n\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\titem: this.item_details.current_item,\n\t\t\t\t\t\t};\n\t\t\t\t\t\treturn this.on_cart_update(args);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t},\n\n\t\t\t\thighlight_cart_item: (item) => {\n\t\t\t\t\tconst cart_item = this.cart.get_cart_item(item);\n\t\t\t\t\tthis.cart.toggle_item_highlight(cart_item);\n\t\t\t\t},\n\n\t\t\t\titem_field_focused: (fieldname) => {\n\t\t\t\t\tthis.cart.toggle_numpad_field_edit(fieldname);\n\t\t\t\t},\n\t\t\t\tset_value_in_current_cart_item: (selector, value) => {\n\t\t\t\t\tthis.cart.update_selector_value_in_cart_item(\n\t\t\t\t\t\tselector,\n\t\t\t\t\t\tvalue,\n\t\t\t\t\t\tthis.item_details.current_item\n\t\t\t\t\t);\n\t\t\t\t},\n\t\t\t\tclone_new_batch_item_in_frm: (batch_serial_map, item) => {\n\t\t\t\t\t// called if serial nos are 'auto_selected' and if those serial nos belongs to multiple batches\n\t\t\t\t\t// for each unique batch new item row is added in the form & cart\n\t\t\t\t\tObject.keys(batch_serial_map).forEach((batch) => {\n\t\t\t\t\t\tconst item_to_clone = this.frm.doc.items.find((i) => i.name == item.name);\n\t\t\t\t\t\tconst new_row = this.frm.add_child(\"items\", { ...item_to_clone });\n\t\t\t\t\t\t// update new serialno and batch\n\t\t\t\t\t\tnew_row.batch_no = batch;\n\t\t\t\t\t\tnew_row.serial_no = batch_serial_map[batch].join(`\\n`);\n\t\t\t\t\t\tnew_row.qty = batch_serial_map[batch].length;\n\t\t\t\t\t\tthis.frm.doc.items.forEach((row) => {\n\t\t\t\t\t\t\tif (item.item_code === row.item_code) {\n\t\t\t\t\t\t\t\tthis.update_cart_html(row);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tremove_item_from_cart: () => this.remove_item_from_cart(),\n\t\t\t\tget_item_stock_map: () => this.item_stock_map,\n\t\t\t\tclose_item_details: () => {\n\t\t\t\t\tthis.item_details.toggle_item_details_section(null);\n\t\t\t\t\tthis.cart.prev_action = null;\n\t\t\t\t\tthis.cart.toggle_item_highlight();\n\t\t\t\t\t// Show the item selector when item details are closed\n\t\t\t\t\tthis.item_selector.toggle_component(true);\n\t\t\t\t},\n\t\t\t\tget_available_stock: (item_code, warehouse) => this.get_available_stock(item_code, warehouse),\n\t\t\t},\n\t\t});\n\t}\n\n\tinit_payments() {\n\t\t// Ensure Payment class exists\n\t\tif (!erpnext.SalesInvoiceUI.Payment) {\n\t\t\tconsole.error(\"Payment class not found\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.payment = new erpnext.SalesInvoiceUI.Payment({\n\t\t\twrapper: this.$components_wrapper,\n\t\t\tevents: {\n\t\t\t\tget_frm: () => this.frm || {},\n\n\t\t\t\tget_customer_details: () => this.customer_details || {},\n\n\t\t\t\ttoggle_other_sections: (show) => {\n\t\t\t\t\tif (show) {\n\t\t\t\t\t\tthis.item_details.$component.is(\":visible\")\n\t\t\t\t\t\t\t? this.item_details.$component.css(\"display\", \"none\")\n\t\t\t\t\t\t\t: \"\";\n\t\t\t\t\t\tthis.item_selector.toggle_component(false);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.item_selector.toggle_component(true);\n\t\t\t\t\t}\n\t\t\t\t},\n\n\t\t\t\tsubmit_invoice: () => {\n\t\t\t\t\tthis.frm.savesubmit().then((r) => {\n\t\t\t\t\t\tthis.toggle_components(false);\n\t\t\t\t\t\tthis.order_summary.toggle_component(true);\n\t\t\t\t\t\tthis.order_summary.load_summary_of(this.frm.doc, true);\n\t\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\t\tindicator: \"green\",\n\t\t\t\t\t\t\tmessage: __(\"Sales Invoice {0} created successfully\", [r.doc.name]),\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t},\n\t\t});\n\t}\n\n\tinit_recent_order_list() {\n\t\tconsole.log(\"comp wrapper:\",this.$components_wrapper);\n\t\tconsole.log(\"wrapper:\",this.wrapper);\n\n\t\t\n\t\tif (!erpnext.SalesInvoiceUI.PastOrderList) {\n\t\t\tconsole.error(\"PastOrderList class not found\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.recent_order_list = new erpnext.SalesInvoiceUI.PastOrderList({\n\t\t\twrapper: this.$components_wrapper,\n\t\t\tevents: {\n\t\t\t\topen_invoice_data: (name) => {\n\t\t\t\t\tfrappe.db.get_doc(\"Sales Invoice\", name).then((doc) => {\n\t\t\t\t\t\tthis.order_summary.load_summary_of(doc);\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\treset_summary: () => this.order_summary.toggle_summary_placeholder(true),\n\t\t\t},\n\t\t});\n\t}\n\n\tinit_order_summary() {\n\t\tif (!erpnext.SalesInvoiceUI.PastOrderSummary) {\n\t\t\tconsole.error(\"PastOrderSummary class not found\");\n\t\t\treturn;\n\t\t}\n\n\t\tthis.order_summary = new erpnext.SalesInvoiceUI.PastOrderSummary({\n\t\t\twrapper: this.$components_wrapper,\n\t\t\tsettings: this.settings,\n\t\t\tevents: {\n\t\t\t\tget_frm: () => this.frm,\n\n\t\t\t\tprocess_return: (name) => {\n\t\t\t\t\tthis.recent_order_list.toggle_component(false);\n\t\t\t\t\tfrappe.db.get_doc(\"Sales Invoice\", name).then((doc) => {\n\t\t\t\t\t\tfrappe.run_serially([\n\t\t\t\t\t\t\t() => this.make_return_invoice(doc),\n\t\t\t\t\t\t\t() => this.cart.load_invoice(),\n\t\t\t\t\t\t\t() => this.item_selector.toggle_component(true),\n\t\t\t\t\t\t]);\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tedit_order: (name) => {\n\t\t\t\t\tthis.recent_order_list.toggle_component(false);\n\t\t\t\t\tfrappe.run_serially([\n\t\t\t\t\t\t() => this.frm.refresh(name),\n\t\t\t\t\t\t() => this.cart.load_invoice(),\n\t\t\t\t\t\t() => this.item_selector.toggle_component(true),\n\t\t\t\t\t]);\n\t\t\t\t},\n\t\t\t\tdelete_order: (name) => {\n\t\t\t\t\tfrappe.model.delete_doc(this.frm.doc.doctype, name, () => {\n\t\t\t\t\t\tthis.recent_order_list.refresh_list();\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tnew_order: () => {\n\t\t\t\t\tfrappe.run_serially([\n\t\t\t\t\t\t() => frappe.dom.freeze(),\n\t\t\t\t\t\t() => this.make_new_invoice(),\n\t\t\t\t\t\t() => this.item_selector.toggle_component(true),\n\t\t\t\t\t\t() => frappe.dom.unfreeze(),\n\t\t\t\t\t]);\n\t\t\t\t},\n\t\t\t},\n\t\t});\n\t}\n\n\tremove_button_active_states() {\n\tthis.page.wrapper.find(\".pos-filter-btn\")\n\t\t.removeClass(\"btn-primary\")\n\t\t.addClass(\"btn-default\");\n\t}\n\n\ttoggle_custom_buttons(show = true) {\n\tif (show) {\n\t\tthis.page.wrapper.find('.pos-custom-buttons').show();\n\t} else {\n\t\tthis.page.wrapper.find('.pos-custom-buttons').hide();\n\t}\n\t}\n\t\n\ttoggle_recent_order_list(show) {\n\t\tthis.toggle_components(!show);\n\t\t\n\t\tif (show) {\n\t\t\tthis.recent_order_list.toggle_component(true);\n\t\t\tthis.order_summary.toggle_component(true);\n\t\t} else {\n\t\t\tthis.recent_order_list.toggle_component(false);\n\t\t\tthis.order_summary.toggle_component(false);\n\t\t\t// Reset button states when hiding\n\t\t\tthis.active_filter_btn = null;\n\t\t\tthis.remove_button_active_states();\n\t\t}\n\t}\n\n\ttoggle_components(show) {\n\t\tthis.cart.toggle_component(show);\n\t\tthis.item_selector.toggle_component(show);\n\n\t\t// do not show item details or payment if recent order is toggled off\n\t\t!show ? this.item_details.toggle_component(false) || this.payment.toggle_component(false) : \"\";\n\t}\n\n\tclose_recent_orders() {\n\tthis.toggle_recent_order_list(false);\n\tthis.active_filter_btn = null;\n\tthis.remove_button_active_states();\n\tif (this.recent_order_list) {\n\t\tthis.recent_order_list.reset_filters();\n\t}\n\t}\n\n\tmake_new_invoice() {\n\t\treturn frappe.run_serially([\n\t\t\t() => frappe.dom.freeze(),\n\t\t\t() => this.make_sales_invoice_frm(),\n\t\t\t() => this.set_pos_profile_data(),\n\t\t\t() => this.set_pos_profile_status(),\n\t\t\t() => this.cart.load_invoice(),\n\t\t\t() => frappe.dom.unfreeze(),\n\t\t]);\n\t}\n\t\n\n\tmake_sales_invoice_frm() {\n\t\tconst doctype = \"Sales Invoice\";\n\t\treturn new Promise((resolve) => {\n\t\t\tif (this.frm) {\n\t\t\t\tthis.frm = this.get_new_frm(this.frm);\n\t\t\t\tthis.frm.doc.items = [];\n\t\t\t\tthis.frm.doc.is_pos = 1;\n\t\t\t\tresolve();\n\t\t\t} else {\n\t\t\t\tfrappe.model.with_doctype(doctype, () => {\n\t\t\t\t\tthis.frm = this.get_new_frm();\n\t\t\t\t\tthis.frm.doc.items = [];\n\t\t\t\t\tthis.frm.doc.is_pos = 1;\n\t\t\t\t\tresolve();\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n\n\tget_new_frm(_frm) {\n\t\tconst doctype = \"Sales Invoice\";\n\t\tconst page = $(\"<div>\");\n\t\tconst frm = _frm || new frappe.ui.form.Form(doctype, page, false);\n\t\tconst name = frappe.model.make_new_doc_and_get_name(doctype, true);\n\t\tfrm.refresh(name);\n\n\t\treturn frm;\n\t}\n\n\tasync make_return_invoice(doc) {\n\t\tfrappe.dom.freeze();\n\t\tthis.frm = this.get_new_frm(this.frm);\n\t\tthis.frm.doc.items = [];\n\t\treturn frappe.call({\n\t\t\tmethod: \"erpnext.accounts.doctype.sales_invoice.sales_invoice.make_sales_return\",\n\t\t\targs: {\n\t\t\t\tsource_name: doc.name,\n\t\t\t\ttarget_doc: this.frm.doc,\n\t\t\t},\n\t\t\tcallback: (r) => {\n\t\t\t\tfrappe.model.sync(r.message);\n\t\t\t\tfrappe.get_doc(r.message.doctype, r.message.name).__run_link_triggers = false;\n\t\t\t\tthis.set_pos_profile_data().then(() => {\n\t\t\t\t\tfrappe.dom.unfreeze();\n\t\t\t\t});\n\t\t\t},\n\t\t});\n\t}\n\n\tset_pos_profile_data() {\n\t\tif (this.company && !this.frm.doc.company) this.frm.doc.company = this.company;\n\t\tif (\n\t\t\t(this.pos_profile && !this.frm.doc.pos_profile) |\n\t\t\t(this.frm.doc.is_return && this.pos_profile != this.frm.doc.pos_profile)\n\t\t) {\n\t\t\tthis.frm.doc.pos_profile = this.pos_profile;\n\t\t}\n\n\t\tif (!this.frm.doc.company) return;\n\n\t\treturn this.frm.trigger(\"set_pos_data\");\n\t}\n\n\tset_pos_profile_status() {\n\t\tthis.page.set_indicator(this.pos_profile, \"blue\");\n\t}\n\n\tasync on_cart_update(args) {\n\t\tfrappe.dom.freeze();\n\t\tif (this.frm.doc.set_warehouse != this.settings.warehouse)\n\t\t\tthis.frm.doc.set_warehouse = this.settings.warehouse;\n\t\tlet item_row = undefined;\n\t\ttry {\n\t\t\tlet { field, value, item } = args;\n\t\t\titem_row = this.get_item_from_frm(item);\n\t\t\tconst item_row_exists = !$.isEmptyObject(item_row);\n\n\t\t\tconst from_selector = field === \"qty\" && value === \"+1\";\n\t\t\tif (from_selector) value = flt(item_row.qty) + flt(value);\n\n\t\t\tif (item_row_exists) {\n\t\t\t\tif (field === \"qty\") value = flt(value);\n\n\t\t\t\tif ([\"qty\", \"conversion_factor\"].includes(field) && value > 0 && !this.allow_negative_stock) {\n\t\t\t\t\tconst qty_needed =\n\t\t\t\t\t\tfield === \"qty\" ? value * item_row.conversion_factor : item_row.qty * value;\n\t\t\t\t\tawait this.check_stock_availability(item_row, qty_needed, this.frm.doc.set_warehouse);\n\t\t\t\t}\n\n\t\t\t\tif (this.is_current_item_being_edited(item_row) || from_selector) {\n\t\t\t\t\tawait frappe.model.set_value(item_row.doctype, item_row.name, field, value);\n\t\t\t\t\tif (item.serial_no && from_selector) {\n\t\t\t\t\t\tawait frappe.model.set_value(\n\t\t\t\t\t\t\titem_row.doctype,\n\t\t\t\t\t\t\titem_row.name,\n\t\t\t\t\t\t\t\"serial_no\",\n\t\t\t\t\t\t\titem_row.serial_no + `\\n${item.serial_no}`\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\tthis.update_cart_html(item_row);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (!this.frm.doc.customer) return this.raise_customer_selection_alert();\n\n\t\t\t\tconst { item_code, batch_no, serial_no, rate, uom, stock_uom } = item;\n\n\t\t\t\tif (!item_code) return;\n\n\t\t\t\tif (rate == undefined || rate == 0) {\n\t\t\t\t\tfrappe.show_alert({\n\t\t\t\t\t\tmessage: __(\"Price is not set for the item.\"),\n\t\t\t\t\t\tindicator: \"orange\",\n\t\t\t\t\t});\n\t\t\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst new_item = { item_code, batch_no, rate, uom, [field]: value, stock_uom };\n\n\t\t\t\tif (serial_no) {\n\t\t\t\t\tawait this.check_serial_no_availablilty(item_code, this.frm.doc.set_warehouse, serial_no);\n\t\t\t\t\tnew_item[\"serial_no\"] = serial_no;\n\t\t\t\t}\n\n\t\t\t\tnew_item[\"use_serial_batch_fields\"] = 1;\n\t\t\t\tif (field === \"serial_no\") new_item[\"qty\"] = value.split(`\\n`).length || 0;\n\n\t\t\t\titem_row = this.frm.add_child(\"items\", new_item);\n\n\t\t\t\tif (field === \"qty\" && value !== 0 && !this.allow_negative_stock) {\n\t\t\t\t\tconst qty_needed = value * item_row.conversion_factor;\n\t\t\t\t\tawait this.check_stock_availability(item_row, qty_needed, this.frm.doc.set_warehouse);\n\t\t\t\t}\n\n\t\t\t\tawait this.trigger_new_item_events(item_row);\n\n\t\t\t\tthis.update_cart_html(item_row);\n\n\t\t\t\tif (this.item_details.$component.is(\":visible\")) this.edit_item_details_of(item_row);\n\n\t\t\t\tif (\n\t\t\t\t\tthis.check_serial_batch_selection_needed(item_row) &&\n\t\t\t\t\t!this.item_details.$component.is(\":visible\")\n\t\t\t\t)\n\t\t\t\t\tthis.edit_item_details_of(item_row);\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\t\t} finally {\n\t\t\tfrappe.dom.unfreeze();\n\t\t\treturn item_row; // eslint-disable-line no-unsafe-finally\n\t\t}\n\t}\n\n\traise_customer_selection_alert() {\n\t\tfrappe.dom.unfreeze();\n\t\tfrappe.show_alert({\n\t\t\tmessage: __(\"You must select a customer before adding an item.\"),\n\t\t\tindicator: \"orange\",\n\t\t});\n\t\tfrappe.utils.play_sound(\"error\");\n\t}\n\n\tget_item_from_frm({ name, item_code, batch_no, uom, rate }) {\n\t\tlet item_row = null;\n\t\tif (name) {\n\t\t\titem_row = this.frm.doc.items.find((i) => i.name == name);\n\t\t} else {\n\t\t\t// if item is clicked twice from item selector\n\t\t\t// then \"item_code, batch_no, uom, rate\" will help in getting the exact item\n\t\t\t// to increase the qty by one\n\t\t\tconst has_batch_no = batch_no !== \"null\" && batch_no !== null;\n\t\t\titem_row = this.frm.doc.items.find(\n\t\t\t\t(i) =>\n\t\t\t\t\ti.item_code === item_code &&\n\t\t\t\t\t(!has_batch_no || (has_batch_no && i.batch_no === batch_no)) &&\n\t\t\t\t\ti.uom === uom &&\n\t\t\t\t\ti.price_list_rate === flt(rate)\n\t\t\t);\n\t\t}\n\n\t\treturn item_row || {};\n\t}\n\n\tedit_item_details_of(item_row) {\n\t\tthis.item_details.toggle_item_details_section(item_row);\n\t}\n\n\tis_current_item_being_edited(item_row) {\n\t\treturn item_row.name == this.item_details.current_item.name;\n\t}\n\n\tupdate_cart_html(item_row, remove_item) {\n\t\tthis.cart.update_item_html(item_row, remove_item);\n\t\tthis.cart.update_totals_section(this.frm);\n\t}\n\n\tcheck_serial_batch_selection_needed(item_row) {\n\t\t// right now item details is shown for every type of item.\n\t\t// if item details is not shown for every item then this fn will be needed\n\t\tconst serialized = item_row.has_serial_no;\n\t\tconst batched = item_row.has_batch_no;\n\t\tconst no_serial_selected = !item_row.serial_no;\n\t\tconst no_batch_selected = !item_row.batch_no;\n\n\t\tif (\n\t\t\t(serialized && no_serial_selected) ||\n\t\t\t(batched && no_batch_selected) ||\n\t\t\t(serialized && batched && (no_batch_selected || no_serial_selected))\n\t\t) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\tasync trigger_new_item_events(item_row) {\n\t\tawait this.frm.script_manager.trigger(\"item_code\", item_row.doctype, item_row.name);\n\t\tawait this.frm.script_manager.trigger(\"qty\", item_row.doctype, item_row.name);\n\t}\n\n\tasync check_stock_availability(item_row, qty_needed, warehouse) {\n\t\tconst resp = (await this.get_available_stock(item_row.item_code, warehouse)).message;\n\t\tconst available_qty = resp[0];\n\t\tconst is_stock_item = resp[1];\n\n\t\tfrappe.dom.unfreeze();\n\t\tconst bold_uom = item_row.stock_uom.bold();\n\t\tconst bold_item_code = item_row.item_code.bold();\n\t\tconst bold_warehouse = warehouse.bold();\n\t\tconst bold_available_qty = available_qty.toString().bold();\n\t\tif (!(available_qty > 0)) {\n\t\t\tif (is_stock_item) {\n\t\t\t\tfrappe.model.clear_doc(item_row.doctype, item_row.name);\n\t\t\t\tfrappe.throw({\n\t\t\t\t\ttitle: __(\"Not Available\"),\n\t\t\t\t\tmessage: __(\"Item Code: {0} is not available under warehouse {1}.\", [\n\t\t\t\t\t\tbold_item_code,\n\t\t\t\t\t\tbold_warehouse,\n\t\t\t\t\t]),\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\treturn;\n\t\t\t}\n\t\t} else if (is_stock_item && available_qty < qty_needed) {\n\t\t\tfrappe.throw({\n\t\t\t\tmessage: __(\n\t\t\t\t\t\"Stock quantity not enough for Item Code: {0} under warehouse {1}. Available quantity {2} {3}.\",\n\t\t\t\t\t[bold_item_code, bold_warehouse, bold_available_qty, bold_uom]\n\t\t\t\t),\n\t\t\t\tindicator: \"orange\",\n\t\t\t});\n\t\t\tfrappe.utils.play_sound(\"error\");\n\t\t}\n\t\tfrappe.dom.freeze();\n\t}\n\n\tasync check_serial_no_availablilty(item_code, warehouse, serial_no) {\n\t\tconst method = \"erpnext.stock.doctype.serial_no.serial_no.get_pos_reserved_serial_nos\";\n\t\tconst args = { filters: { item_code, warehouse } };\n\t\tconst res = await frappe.call({ method, args });\n\n\t\tif (res.message.includes(serial_no)) {\n\t\t\tfrappe.throw({\n\t\t\t\ttitle: __(\"Not Available\"),\n\t\t\t\tmessage: __(\"Serial No: {0} has already been transacted into another Sales Invoice.\", [\n\t\t\t\t\tserial_no.bold(),\n\t\t\t\t]),\n\t\t\t});\n\t\t}\n\t}\n\n\tget_available_stock(item_code, warehouse) {\n\t\tconst me = this;\n\t\treturn frappe.call({\n\t\t\tmethod: \"erpnext.accounts.doctype.pos_invoice.pos_invoice.get_stock_availability\",\n\t\t\targs: {\n\t\t\t\titem_code: item_code,\n\t\t\t\twarehouse: warehouse,\n\t\t\t},\n\t\t\tcallback(res) {\n\t\t\t\tif (!me.item_stock_map[item_code]) me.item_stock_map[item_code] = {};\n\t\t\t\tme.item_stock_map[item_code][warehouse] = res.message;\n\t\t\t},\n\t\t});\n\t}\n\n\tupdate_item_field(value, field_or_action) {\n\t\tif (field_or_action === \"checkout\") {\n\t\t\tthis.item_details.toggle_item_details_section(null);\n\t\t} else if (field_or_action === \"remove\") {\n\t\t\tthis.remove_item_from_cart();\n\t\t} else {\n\t\t\tconst field_control = this.item_details[`${field_or_action}_control`];\n\t\t\tif (!field_control) return;\n\t\t\tfield_control.set_focus();\n\t\t\tvalue != \"\" && field_control.set_value(value);\n\t\t}\n\t}\n\n\tremove_item_from_cart() {\n\t\tfrappe.dom.freeze();\n\t\tconst { doctype, name, current_item } = this.item_details;\n\n\t\treturn frappe.model\n\t\t\t.set_value(doctype, name, \"qty\", 0)\n\t\t\t.then(() => {\n\t\t\t\tfrappe.model.clear_doc(doctype, name);\n\t\t\t\tthis.update_cart_html(current_item, true);\n\t\t\t\tthis.item_details.toggle_item_details_section(null);\n\t\t\t\tfrappe.dom.unfreeze();\n\t\t\t})\n\t\t\t.catch((e) => console.log(e));\n\t}\n\n\tasync save_and_checkout() {\n\t\tif (this.frm.is_dirty()) {\n\t\t\tlet save_error = false;\n\t\t\tawait this.frm.save(null, null, null, () => (save_error = true));\n\t\t\t// only move to payment section if save is successful\n\t\t\t!save_error && this.payment.checkout();\n\t\t\t// show checkout button on error\n\t\t\tsave_error &&\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.cart.toggle_checkout_btn(true);\n\t\t\t\t}, 300); // wait for save to finish\n\t\t} else {\n\t\t\tthis.payment.checkout();\n\t\t}\n\t}\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAGC,OAAC,SAAU,QAAQ,SAAS;AACzB,eAAO,YAAY,YAAY,OAAO,WAAW,cAAc,OAAO,UAAU,QAAQ,IACxF,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,QAAQ,CAAC,IAC7D,OAAO,SAAS,QAAQ;AAAA,MAC5B,GAAE,SAAO,WAAY;AACpB,YAAIA,UAAS;AAAA,UAQZ,UAAU,SAAS,aAAa,UAAU;AAEzC,gBAAG,YAAY,yBAAyB,QAAU;AACjD,oBAAM,IAAI,MAAM,sDAAsD,WAAW;AAAA,YAClF;AAEA,gBAAI,YAAY;AAAA,cACf,QAAQ,SAAS,UAAU,MAAK;AAAA,cAAC;AAAA,cACjC,aAAa,SAAS,QAAO;AAAA,cAAC;AAAA,cAC9B,cAAc,SAAS,OAAO,QAAO;AAAA,cAAC;AAAA,cACtC,aAAa,SAAS,UAAU,QAAO;AAAA,cAAC;AAAA,cACxC,SAAS,SAAS,SAAS,QAAO;AAAA,cAAC;AAAA,cACnC,eAAe,SAAS,QAAQ;AAAC,uBAAOA,QAAO,eAAe,MAAM;AAAA,cAAC;AAAA,cACrE,uBAAuB,WAAU;AAAA,cAAC;AAAA,cAClC,mBAAkB;AAAA,cAClB,yBAAwB;AAAA,cACxB,oBAAmB;AAAA,cACnB,eAAc;AAAA,cACd,WAAU;AAAA,cACV,gBAAe,CAAC,GAAE,EAAE;AAAA,cACpB,gBAAe,CAAC;AAAA,cAChB,iBAAgB;AAAA,cAChB,iBAAgB;AAAA,cAChB,gBAAe;AAAA,cACf,eAAc;AAAA,cACd,gBAAe;AAAA,cACf,cAAa;AAAA,cACb,eAAe;AAAA,YAChB;AAEA,uBAAW,KAAK,cAAc,WAAW,QAAQ;AAGjD,wBAAY,uBAAuB;AAAA,cACjC,SAAS;AAAA,cACT,MAAK;AAAA,gBACJ,eAAe;AAAA,gBACf,cAAc;AAAA,gBACd,mBAAmB;AAAA,gBACnB,WAAW;AAAA,gBACX,oBAAoB;AAAA,gBACpB,aAAa;AAAA,cACd;AAAA,YAEF;AAGA,gBAAI,SAAS,iBAAiB,MAAK;AAClC,0BAAY,iBAAiB,SAAS,KAAK,cAAc,SAAS,aAAa;AAAA,YAChF;AACA,gBAAI,SAAS,sBAAsB,OAAM;AACxC,0BAAY,iBAAiB,SAAS,KAAK,cAAc,SAAS,aAAa;AAAA,YAChF;AACA,gBAAI,SAAS,mBAAmB,QAAQ,SAAS,sBAAsB,OAAM;AAC5E,0BAAY,iBAAiB,WAAW,KAAK,gBAAgB,SAAS,aAAa;AAAA,YACpF;AACA,mBAAO;AAAA,UACR;AAAA,UAOA,YAAY,SAAS,aAAa;AAEjC,gBAAI,YAAY,qBAAqB,QAAQ,cAAa;AACzD,0BAAY,oBAAoB,SAAS,KAAK,YAAY;AAAA,YAC3D;AACA,gBAAI,YAAY,qBAAqB,QAAQ,sBAAsB,OAAM;AACxE,0BAAY,oBAAoB,SAAS,KAAK,YAAY;AAAA,YAC3D;AACA,wBAAY,oBAAoB,WAAW,KAAK,cAAc;AAG9D,wBAAY,uBAAuB;AACnC;AAAA,UACD;AAAA,UAOA,YAAY,SAAS,aAAY;AAChC,mBAAO,YAAY,qBAAqB;AAAA,UACzC;AAAA,UAQA,YAAY,SAAS,aAAa,UAAS;AAE1C,oBAAQ,YAAY,qBAAqB,QAAQ;AAAA,mBAC3C;AACJ,oBAAI,SAAS,iBAAiB,OAAM;AACnC,8BAAY,oBAAoB,SAAS,KAAK,YAAY;AAAA,gBAC3D;AACA;AAAA,mBACI;AACJ,oBAAI,SAAS,iBAAiB,MAAK;AAClC,8BAAY,iBAAiB,SAAS,KAAK,YAAY;AAAA,gBACxD;AACA;AAAA;AAGF,oBAAQ,YAAY,qBAAqB,QAAQ;AAAA,mBAC3C;AACJ,oBAAI,SAAS,sBAAsB,OAAM;AACxC,8BAAY,iBAAiB,SAAS,KAAK,YAAY;AAAA,gBACxD;AACA;AAAA;AAEA,oBAAI,SAAS,sBAAsB,OAAM;AACxC,8BAAY,oBAAoB,SAAS,KAAK,YAAY;AAAA,gBAC3D;AACA;AAAA;AAIF,wBAAY,qBAAqB,UAAU,KAAK,cAAc,YAAY,qBAAqB,SAAS,QAAQ;AAGhH,iBAAK,cAAc,WAAW;AAC9B,mBAAO;AAAA,UACR;AAAA,UAoBA,gBAAiB,SAAU,QAAQ;AAClC,gBAAI,QAAQ,KAAK,qBAAqB,MAAM;AAC5C,oBAAQ;AAAA,oBACF,SAAS,MAAM,SAAS;AAAA,oBACxB,SAAS,OAAO,SAAS;AAC7B,oBAAI,OAAO,QAAQ,UAAa,OAAO,QAAQ,IAAI;AAClD,yBAAO,OAAO;AAAA,gBACf;AAEA,oBAAI,WAAW,OAAO,aAAa,KAAK;AACxC,wBAAQ,OAAO;AAAA,uBACT;AAAO,+BAAW,SAAS,YAAY;AAAG;AAAA,uBAC1C;AAAM,+BAAW,SAAS,YAAY;AAAG;AAAA;AAE/C,uBAAO;AAAA,oBACH,SAAS,MAAM,SAAS;AAC5B,uBAAO,KAAG,QAAM;AAAA;AAElB,mBAAO;AAAA,UACR;AAAA,UAkBA,UAAU,SAAS,aAAa,gBAAe;AAC9C,iBAAK,cAAc,WAAW;AAC9B,gBAAI,MAAM,QAAQ,cAAc,GAAE;AACjC,6BAAe,QAAQ,SAAS,MAAK;AACpC,oBAAI,cAAc,CAAC;AACnB,qBAAK,OAAO,SAAS,YAAY,OAAO,SAAS,eAAgB,SAAS,MAAQ;AACjF,gCAAc;AAAA,gBACf,OAAO;AACN,8BAAY,UAAU,SAAS,IAAI;AAAA,gBACpC;AACA,oBAAI,SAAS,IAAI,cAAc,WAAW,WAAW;AACrD,yBAAS,cAAc,MAAM;AAAA,cAC9B,CAAC;AAAA,YACF,OAAO;AACN,mBAAK,kBAAkB,aAAa,cAAc;AAAA,YACnD;AACA,mBAAO;AAAA,UACR;AAAA,UAOA,eAAe,SAAS,aAAY;AACnC,gBAAI,QAAQ,YAAY,qBAAqB;AAC7C,kBAAM,gBAAgB;AACtB,kBAAM,eAAe;AACrB,kBAAM,oBAAoB;AAC1B;AAAA,UACD;AAAA,UAOA,0BAA0B,SAAS,aAAY;AAE9C,gBAAI,kBAAkB,YAAY,qBAAqB,QAAQ;AAEzD,gBAAG,CAAC,iBAAgB;AACzB,qBAAO;AAAA,YACR;AAEA,gBAAI,WAAW,SAAS;AAGxB,gBAAI,MAAM,QAAQ,eAAe,GAAE;AAClC,uBAAQ,IAAE,GAAG,IAAE,gBAAgB,QAAQ,KAAI;AAC1C,oBAAG,SAAS,QAAQ,gBAAgB,EAAE,MAAM,MAAK;AAChD,yBAAO;AAAA,gBACR;AAAA,cACD;AAAA,YAED,WAAW,SAAS,QAAQ,eAAe,GAAE;AAC5C,qBAAO;AAAA,YACR;AAGG,mBAAO;AAAA,UACR;AAAA,UASH,mBAAmB,SAAS,aAAa,WAAU;AAClD,gBAAI,eAAe,YAAY;AAC/B,gBAAI,WAAW,aAAa;AAC5B,gBAAI,iBAAiB,aAAa,QAAQ;AAC1C,gBAAI,iBAAiB,aAAa,KAAK;AACvC,gBAAI,gBAAgB,aAAa,KAAK;AACtC,gBAAI,aAAa,CAAC;AACZ,gBAAI;AAEV,oBAAO;AAAA,mBAGA,UAAU,SAAS,SAAS;AACjC,6BAAa;AAAA,kBACZ,SAAS;AAAA,gBACV;AACA;AAAA,mBAGM,gBAAgB,iBAAmB,UAAU,SAAS,SAAS;AACrE,6BAAa;AAAA,kBACZ,SAAS;AAAA,gBACV;AACA;AAAA;AAIA,yBAAS,OAAO,KAAK,aAAa,WAAW,cAAc;AAC3D,yBAAS,IAAI;AAAA,kBACZ;AAAA,kBACA;AAAA,oBACC,QAAQ;AAAA,sBACP,UAAU;AAAA,sBACV,KAAK;AAAA,oBACN;AAAA,kBACD;AAAA,gBACD;AACA,4BAAY,cAAc,MAAM;AAChC,gBAAAA,QAAO,cAAc,WAAW;AAChC,uBAAO;AAAA;AAIT,uBAAW,WAAW;AACtB,uBAAW,eAAe,gBAAgB;AAC1C,uBAAW,gBAAgB,SAAS;AACpC,uBAAW,YAAY,SAAS;AAEhC,qBAAS,YAAY,KAAK,aAAa,UAAU;AAEjD,qBAAS,IAAI;AAAA,cACZ;AAAA,cACA,EAAC,QAAQ,WAAU;AAAA,YACpB;AACA,wBAAY,cAAc,MAAM;AAEhC,YAAAA,QAAO,cAAc,WAAW;AAChC,mBAAO;AAAA,UACL;AAAA,UAQH,eAAe,SAAS,WAAW,UAAS;AAC3C,gBAAI,YAAY,CAAC;AACjB,gBAAI;AACJ,iBAAK,QAAQ,WAAU;AACtB,kBAAI,OAAO,UAAU,eAAe,KAAK,WAAW,IAAI,GAAE;AACzD,0BAAU,QAAQ,UAAU;AAAA,cAC7B;AAAA,YACD;AACA,iBAAK,QAAQ,UAAS;AACrB,kBAAI,OAAO,UAAU,eAAe,KAAK,UAAU,IAAI,GAAE;AACxD,0BAAU,QAAQ,SAAS;AAAA,cAC5B;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA,UAQA,sBAAsB,SAAS,GAAE;AAChC,mBAAO,EAAE,SAAS,EAAE;AAAA,UACrB;AAAA,UAQA,gBAAgB,SAAS,GAAE;AAC1B,gBAAI,WAAWA,QAAO,qBAAqB,CAAC;AAC5C,gBAAI,WAAW,KAAK,qBAAqB;AACzC,gBAAI,QAAQ,KAAK,qBAAqB;AACtC,gBAAI,gBAAgB;AAEpB,gBAAI,SAAS,YAAY,KAAK,MAAM,UAAU,CAAC,MAAM,OAAO;AAC3D;AAAA,YACD;AAEA,gBAAIA,QAAO,yBAAyB,IAAI,GAAE;AACzC;AAAA,YACD;AAGG,gBAAG,SAAS,sBAAsB,SAAS,YAAU,SAAS,mBAAmB;AAGnF,kBAAI,CAAC,MAAM,aAAY;AACtB,sBAAM,iBAAiB,WAAY,SAAS,uBAAuB,SAAS,yBAAyB,IAAI;AACzG,sBAAM,cAAc;AAAA,cACrB;AAEA;AAAA,YACK;AAEN,oBAAO;AAAA,oBAEA,MAAM,iBAAiB,SAAS,eAAe,QAAQ,QAAQ,MAAI;AACxE,kBAAE,eAAe;AACjB,kBAAE,yBAAyB;AAC3B,gCAAc;AACd;AAAA,oBAGK,CAAC,MAAM,iBAAiB,SAAS,eAAe,QAAQ,QAAQ,MAAI;AACzE,kBAAE,eAAe;AACjB,kBAAE,yBAAyB;AAC3B,gCAAc;AACd;AAAA;AAIA,oBAAI,YAAY,SAAS,cAAc,KAAK,MAAM,CAAC;AACnD,oBAAI,cAAc,MAAK;AACtB;AAAA,gBACD;AACA,sBAAM,qBAAqB;AAE3B,oBAAI,SAAS,gBAAgB;AAC5B,oBAAE,eAAe;AAAA,gBAClB;AACA,oBAAI,SAAS,iBAAiB;AAC7B,oBAAE,yBAAyB;AAAA,gBAC5B;AAEA,gCAAc;AACd;AAAA;AAGF,gBAAG,CAAC,MAAM,eAAc;AACvB,oBAAM,gBAAc,KAAK,IAAI;AAAA,YAC9B;AAEA,kBAAM,eAAa,KAAK,IAAI;AAE5B,gBAAG,MAAM,WAAU;AAClB,2BAAa,MAAM,SAAS;AAAA,YAC7B;AAEA,gBAAG,eAAc;AAChB,cAAAA,QAAO,kBAAkB,MAAM,MAAM,iBAAiB;AACtD,oBAAM,YAAU;AAAA,YACjB,OAAO;AACN,oBAAM,YAAU,WAAWA,QAAO,mBAAmB,SAAS,oBAAoB,MAAM,MAAM,iBAAiB;AAAA,YAChH;AAEA,qBAAS,aAAa,KAAK,MAAM,WAAW,CAAC;AAC7C;AAAA,UACD;AAAA,UAOA,cAAc,SAAS,GAAE;AAExB,gBAAI,WAAW,KAAK,qBAAqB;AACzC,gBAAI,QAAQ,KAAK,qBAAqB;AACtC,gBAAI,gBAAgB,MAAM,iBAAiB,OAAO,eAAe,QAAQ,MAAM;AAG/E,gBAAIA,QAAO,yBAAyB,IAAI,GAAE;AACzC;AAAA,YACD;AAEA,cAAE,eAAe;AAEjB,gBAAI,SAAS,iBAAiB;AAC7B,gBAAE,yBAAyB;AAAA,YAC5B;AAEA,qBAAS,QAAQ,KAAK,MAAM,cAAc,KAAK;AAE/C,kBAAM,gBAAgB;AACtB,kBAAM,eAAe;AAGrB,YAAAA,QAAO,kBAAkB,MAAM,YAAY;AAC3C;AAAA,UACD;AAAA,UAOA,cAAc,SAAS,GAAE;AAExB,gBAAIA,QAAO,yBAAyB,IAAI,GAAE;AACzC;AAAA,YACD;AAEA,gBAAI,WAAWA,QAAO,qBAAqB,CAAC;AAG5C,gBAAI,YAAY,KAAK,qBAAqB,QAAQ,mBAAkB;AACnE,2BAAa,KAAK,qBAAqB,KAAK,cAAc;AAC1D,mBAAK,qBAAqB,KAAK,cAAc;AAAA,YAC9C;AACA;AAAA,UACD;AAAA,UAQA,qBAAqB,SAAS,aAAa;AAC1C,mBAAO,YAAY,qBAAqB,KAAK,gBAAgB;AAAA,UAC9D;AAAA,UAQA,cAAc,SAAS,aAAa;AACnC,mBAAQ,YAAY,yBAAyB;AAAA,UAC9C;AAAA,QACD;AAEA,eAAOA;AAAA,MACR,CAAE;AAAA;AAAA;;;AC7gBF,UAAQ,eAAe,WAAW,MAAM;AAAA,IACvC,YAAY,EAAE,SAAS,QAAQ,SAAS,GAAG;AAC1C,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,gBAAgB;AACrB,WAAK,cAAc,SAAS;AAC5B,WAAK,0BAA0B,SAAS;AACxC,WAAK,oBAAoB,SAAS;AAClC,WAAK,wBAAwB,SAAS;AACtC,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,sBAAsB;AAC3B,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ,OAAO,qDAAqD;AAEzE,WAAK,aAAa,KAAK,QAAQ,KAAK,0BAA0B;AAAA,IAC/D;AAAA,IAEA,wBAAwB;AACvB,WAAK,uBAAuB;AAC5B,WAAK,qBAAqB;AAAA,IAC3B;AAAA,IAEA,yBAAyB;AACxB,WAAK,WAAW,OAAO,sCAAsC;AAC7D,WAAK,oBAAoB,KAAK,WAAW,KAAK,mBAAmB;AACjE,WAAK,uBAAuB;AAAA,IAC7B;AAAA,IAEA,0BAA0B;AACzB,YAAM,MAAM,KAAK,OAAO,QAAQ;AAChC,UAAI,UAAU,YAAY,EAAE;AAC5B,WAAK,uBAAuB;AAC5B,WAAK,eAAe,UAAU;AAAA,IAC/B;AAAA,IAEA,uBAAuB;AACtB,WAAK,WAAW;AAAA,QACf;AAAA;AAAA,+BAE4B,GAAG,WAAW;AAAA;AAAA,iCAEZ,GAAG,MAAM;AAAA,gCACV,GAAG,UAAU;AAAA,wCACL,GAAG,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjD;AACA,WAAK,kBAAkB,KAAK,WAAW,KAAK,iBAAiB;AAE7D,WAAK,yBAAyB;AAC9B,WAAK,wBAAwB;AAC7B,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,0BAA0B;AACzB,WAAK,eAAe,KAAK,WAAW,KAAK,cAAc;AACvD,WAAK,sBAAsB,KAAK,WAAW,KAAK,qBAAqB;AAErE,WAAK,0BAA0B;AAAA,IAChC;AAAA,IAEA,4BAA4B;AAC3B,WAAK,aAAa,IAAI,WAAW,MAAM;AACvC,WAAK,oBAAoB,KAAK,gCAAgC,GAAG,kBAAkB,SAAS;AAAA,IAC7F;AAAA,IAEA,oBAAoB;AACnB,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMR;AAAA,IAEA,2BAA2B;AAC1B,WAAK,kBAAkB,KAAK,WAAW,KAAK,sBAAsB;AAElE,WAAK,gBAAgB;AAAA,QACpB;AAAA,MACG,KAAK,kBAAkB,KAAK,GAAG,cAAc;AAAA;AAAA;AAAA,wCAGX,GAAG,aAAa;AAAA;AAAA;AAAA;AAAA,mCAIrB,GAAG,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,WAKtC,GAAG,aAAa;AAAA;AAAA;AAAA,+BAGI,GAAG,UAAU;AAAA,gCACZ,GAAG,WAAW;AAAA,MAC5C;AAEA,WAAK,qBAAqB,KAAK,WAAW,KAAK,uBAAuB;AAAA,IACvE;AAAA,IAEA,mBAAmB;AAClB,WAAK,kBAAkB,KAAK,WAAW,KAAK,iBAAiB;AAE7D,WAAK,aAAa,IAAI,QAAQ,eAAe,UAAU;AAAA,QACtD,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,UACP,cAAc,KAAK,gBAAgB,KAAK,IAAI;AAAA,QAC7C;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,UACL,CAAC,GAAG,GAAG,GAAG,UAAU;AAAA,UACpB,CAAC,GAAG,GAAG,GAAG,UAAU;AAAA,UACpB,CAAC,GAAG,GAAG,GAAG,MAAM;AAAA,UAChB,CAAC,KAAK,GAAG,UAAU,QAAQ;AAAA,QAC5B;AAAA,QACA,aAAa;AAAA,UACZ,CAAC,IAAI,IAAI,IAAI,YAAY;AAAA,UACzB,CAAC,IAAI,IAAI,IAAI,YAAY;AAAA,UACzB,CAAC,IAAI,IAAI,IAAI,YAAY;AAAA,UACzB,CAAC,IAAI,IAAI,IAAI,uBAAuB;AAAA,QACrC;AAAA,QACA,gBAAgB,EAAE,UAAU,OAAO,UAAU,sBAAsB;AAAA,MACpE,CAAC;AAED,WAAK,gBAAgB;AAAA,QACpB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKD;AAEA,WAAK,gBAAgB;AAAA,QACpB,qEAAqE,GAAG,UAAU;AAAA,MACnF;AAAA,IACD;AAAA,IAEA,cAAc;AACb,YAAM,KAAK;AACX,WAAK,kBAAkB,GAAG,SAAS,uBAAuB,WAAY;AACrE,WAAG,wBAAwB;AAAA,MAC5B,CAAC;AAED,WAAK,kBAAkB,GAAG,SAAS,sBAAsB,WAAY;AACpE,WAAG,qBAAqB,KAAK;AAAA,MAC9B,CAAC;AAED,WAAK,kBAAkB,GAAG,SAAS,qBAAqB,SAAU,GAAG;AACpE,YAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,qBAAqB,EAAE;AAAQ;AAEvD,cAAM,OAAO,GAAG,gBAAgB,GAAG,UAAU;AAC7C,WAAG,qBAAqB,IAAI;AAAA,MAC7B,CAAC;AAED,WAAK,oBAAoB,GAAG,SAAS,sBAAsB,WAAY;AACtE,cAAM,aAAa,EAAE,IAAI;AAEzB,WAAG,sBAAsB,IAAI;AAE7B,cAAM,yBAAyB,CAAC,GAAG,gBAAgB,KAAK,gBAAgB,EAAE,GAAG,UAAU;AACvF,YAAI,CAAC,wBAAwB;AAG5B,aAAG,gBAAgB,KAAK,gBAAgB,EAAE,MAAM;AAAA,QACjD;AAEA,cAAM,gBAAgB,SAAS,WAAW,KAAK,eAAe,CAAC;AAC/D,WAAG,OAAO,kBAAkB,EAAE,MAAM,cAAc,CAAC;AACnD,aAAK,eAAe;AAAA,MACrB,CAAC;AAED,WAAK,WAAW,GAAG,SAAS,iBAAiB,iBAAkB;AAC9D,YAAI,EAAE,IAAI,EAAE,KAAK,OAAO,EAAE,QAAQ,YAAY,KAAK;AAAI;AAEvD,cAAM,GAAG,OAAO,SAAS;AACzB,WAAG,oBAAoB,KAAK;AAE5B,WAAG,yBAAyB,GAAG,mBAAmB,YAAY,QAAQ;AAAA,MACvE,CAAC;AAED,WAAK,gBAAgB,GAAG,SAAS,kBAAkB,MAAM;AACxD,aAAK,OAAO,UAAU;AACtB,aAAK,oBAAoB,IAAI;AAAA,MAC9B,CAAC;AAED,WAAK,WAAW,GAAG,SAAS,yBAAyB,MAAM;AAC1D,cAAM,oBAAoB,KAAK,mBAAmB,KAAK,oBAAoB,EAAE;AAE7E,YAAI,CAAC,KAAK,kBAAkB;AAAmB,eAAK,sBAAsB;AAAA,MAC3E,CAAC;AAED,aAAO,GAAG,KAAK,GAAG,eAAe,eAAe,CAAC,QAAQ;AAExD,aAAK,sBAAsB,GAAG;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA,IAEA,mBAAmB;AAClB,eAAS,OAAO,KAAK,WAAW,MAAM;AACrC,iBAAS,OAAO,KAAK;AACpB,cAAI,OAAO,QAAQ;AAAU;AAE7B,cAAI,eAAe,QAAQ,OAAO,MAAM,OAAO,GAAG,CAAC,EAAE;AACrD,cAAI,QAAQ;AAAU,2BAAe;AACrC,cAAI,QAAQ;AAAU,2BAAe;AACrC,cAAI,QAAQ;AAAK,2BAAe;AAGhC,gBAAM,YAAY,KAAK,WAAW,WAAW,OAC1C,KAAK,WAAW,WAAW,OAC3B,OAAO,QAAQ,WACf,OAAO,MAAM,GAAG,IAChB;AAEH,cAAI,iBAAiB,aAAa,MAAM,GAAG,EAAE,IAAI,OAAO,MAAM,aAAa,EAAE,KAAK,GAAG;AACrF,2BAAiB,OAAO,MAAM,OAAO,IAAI,eAAe,QAAQ,QAAQ,QAAG,IAAI;AAC/E,eAAK,gBACH,KAAK,kCAAkC,aAAa,EACpD,KAAK,SAAS,cAAc;AAE9B,iBAAO,GAAG,KAAK,GAAG,GAAG,gBAAgB,MAAM;AAC1C,kBAAM,kBAAkB,KAAK,WAAW,GAAG,UAAU;AACrD,gBAAI,mBAAmB,KAAK,oBAAoB,KAAK,gBAAgB,GAAG,UAAU,GAAG;AACpF,mBAAK,gBAAgB,KAAK,kCAAkC,aAAa,EAAE,MAAM;AAAA,YAClF;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AACA,YAAM,aAAa,OAAO,MAAM,OAAO,IAAI,WAAM;AACjD,WAAK,WAAW,KAAK,eAAe,EAAE,KAAK,SAAS,GAAG,kBAAkB;AACzE,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM,KAAK,WAAW,KAAK,eAAe,EAAE,MAAM;AAAA,QAC1D,WAAW,MACV,KAAK,WAAW,GAAG,UAAU,KAAK,CAAC,KAAK,gBAAgB,KAAK,gBAAgB,EAAE,GAAG,UAAU;AAAA,QAC7F,aAAa,GAAG,2CAA2C;AAAA,QAC3D,eAAe;AAAA,QACf,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AACD,WAAK,WAAW,KAAK,gBAAgB,EAAE,KAAK,SAAS,GAAG,cAAc;AACtE,aAAO,GAAG,KAAK,GAAG,UAAU,MAAM;AACjC,cAAM,oBAAoB,KAAK,WAAW,GAAG,UAAU;AACvD,cAAM,yBAAyB,CAAC,KAAK,gBAAgB,KAAK,eAAe,EAAE,GAAG,SAAS;AACvF,YAAI,qBAAqB,wBAAwB;AAChD,eAAK,WAAW,KAAK,gBAAgB,EAAE,MAAM;AAAA,QAC9C;AAAA,MACD,CAAC;AACD,WAAK,WAAW,KAAK,uBAAuB,EAAE,KAAK,SAAS,GAAG,cAAc;AAC7E,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM,KAAK,WAAW,KAAK,uBAAuB,EAAE,MAAM;AAAA,QAClE,WAAW,MAAM,KAAK,mBAAmB,GAAG,UAAU;AAAA,QACtD,aAAa,GAAG,oBAAoB;AAAA,QACpC,eAAe;AAAA,QACf,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AACD,aAAO,GAAG,KAAK,GAAG,UAAU,MAAM;AACjC,cAAM,oBAAoB,KAAK,WAAW,GAAG,UAAU;AACvD,YAAI,qBAAqB,KAAK,kBAAkB,KAAK,eAAe,OAAO,GAAG,UAAU,GAAG;AAC1F,eAAK,eAAe,UAAU,CAAC;AAAA,QAChC;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,sBAAsB,MAAM;AAC3B,YAAM,aAAa,EAAE,IAAI;AACzB,YAAM,sBAAsB,WAAW,KAAK,OAAO,KAAK;AAExD,UAAI,CAAC,QAAQ,qBAAqB;AACjC,aAAK,mBAAmB;AACxB,aAAK,gBAAgB,KAAK,oBAAoB,EAAE,IAAI,oBAAoB,EAAE;AAAA,MAC3E,OAAO;AACN,mBAAW,IAAI,oBAAoB,mBAAmB;AACtD,aAAK,mBAAmB;AACxB,aAAK,gBAAgB,KAAK,oBAAoB,EAAE,IAAI,IAAI,EAAE,IAAI,oBAAoB,EAAE;AAAA,MACrF;AAAA,IACD;AAAA,IAEA,yBAAyB;AACxB,WAAK,kBAAkB,KAAK;AAAA;AAAA,GAE3B;AACD,YAAM,KAAK;AACX,YAAM,yBAAyB,KAAK,2BAA2B,CAAC;AAChE,UAAI,UAAU,CAAC;AACf,UAAI,uBAAuB,QAAQ;AAClC,kBAAU;AAAA,UACT,gBAAgB,CAAC,MAAM,sBAAsB;AAAA,QAC9C;AAAA,MACD;AACA,WAAK,iBAAiB,OAAO,GAAG,KAAK,aAAa;AAAA,QACjD,IAAI;AAAA,UACH,OAAO,GAAG,UAAU;AAAA,UACpB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,aAAa,GAAG,wCAAwC;AAAA,UACxD,WAAW,WAAY;AACtB,mBAAO;AAAA,cACN;AAAA,YACD;AAAA,UACD;AAAA,UACA,UAAU,WAAY;AACrB,gBAAI,KAAK,OAAO;AACf,oBAAM,MAAM,GAAG,OAAO,QAAQ;AAC9B,qBAAO,IAAI,OAAO;AAClB,qBAAO,MAAM,UAAU,IAAI,IAAI,SAAS,IAAI,IAAI,MAAM,YAAY,KAAK,KAAK;AAC5E,kBAAI,eAAe,QAAQ,YAAY,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,EAAE,KAAK,MAAM;AAChF,uBAAO,aAAa;AAAA,kBACnB,MAAM,GAAG,uBAAuB,KAAK,KAAK;AAAA,kBAC1C,MAAM,GAAG,OAAO,yBAAyB,GAAG,aAAa;AAAA,kBACzD,MAAM,GAAG,wBAAwB;AAAA,kBACjC,MAAM,GAAG,sBAAsB;AAAA,kBAC/B,MAAM,OAAO,IAAI,SAAS;AAAA,gBAC3B,CAAC;AAAA,cACF,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ,KAAK,kBAAkB,KAAK,iBAAiB;AAAA,QACrD,cAAc;AAAA,MACf,CAAC;AACD,WAAK,eAAe,aAAa,KAAK;AAAA,IACvC;AAAA,IAEA,uBAAuB,UAAU;AAChC,UAAI,UAAU;AACb,eAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,iBAAO,GACL,UAAU,YAAY,UAAU,CAAC,YAAY,aAAa,SAAS,iBAAiB,CAAC,EACrF,KAAK,CAAC,EAAE,QAAQ,MAAM;AACtB,kBAAM,EAAE,gBAAgB,IAAI;AAE5B,gBAAI,iBAAiB;AACpB,qBAAO,KAAK;AAAA,gBACX,QAAQ;AAAA,gBACR,MAAM,EAAE,UAAU,iBAAiB,QAAQ,KAAK;AAAA,gBAChD,UAAU,CAAC,MAAM;AAChB,wBAAM,EAAE,gBAAgB,kBAAkB,IAAI,EAAE;AAChD,sBAAI,CAAC,EAAE,KAAK;AACX,yBAAK,gBAAgB,iCACjB,UADiB;AAAA,sBAEpB;AAAA,sBACA;AAAA,sBACA;AAAA,oBACD;AACA,4BAAQ;AAAA,kBACT;AAAA,gBACD;AAAA,cACD,CAAC;AAAA,YACF,OAAO;AACN,mBAAK,gBAAgB,iCAAK,UAAL,EAAc,SAAS;AAC5C,sBAAQ;AAAA,YACT;AAAA,UACD,CAAC;AAAA,QACH,CAAC;AAAA,MACF,OAAO;AACN,eAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,eAAK,gBAAgB,CAAC;AACtB,kBAAQ;AAAA,QACT,CAAC;AAAA,MACF;AAAA,IACD;AAAA,IAEA,wBAAwB;AACvB,WAAK,mBAAmB,IAAI,EAAE,SAAS,OAAO,QAAQ,OAAO,CAAC;AAC9D,WAAK,mBAAmB,KAAK,wCAAwC;AACrE,YAAM,KAAK;AACX,YAAM,MAAM,GAAG,OAAO,QAAQ;AAC9B,UAAI,WAAW,IAAI,IAAI;AAEvB,WAAK,iBAAiB,OAAO,GAAG,KAAK,aAAa;AAAA,QACjD,IAAI;AAAA,UACH,OAAO,GAAG,UAAU;AAAA,UACpB,WAAW;AAAA,UACX,aAAa,WAAW,WAAW,MAAM,GAAG,4BAA4B;AAAA,UACxE,aAAa;AAAA,UACb,UAAU,WAAY;AACrB,iBAAK,QAAQ,IAAI,KAAK,KAAK;AAC3B,gBAAI,KAAK,QAAQ,KAAK;AACrB,qBAAO,SAAS;AAAA,gBACf,OAAO,GAAG,kBAAkB;AAAA,gBAC5B,WAAW;AAAA,gBACX,SAAS,GAAG,uCAAuC;AAAA,cACpD,CAAC;AACD,mBAAK,QAAQ;AAAA,YACd;AACA,mBAAO,MAAM;AAAA,cACZ,IAAI,IAAI;AAAA,cACR,IAAI,IAAI;AAAA,cACR;AAAA,cACA,IAAI,KAAK,KAAK;AAAA,YACf;AACA,eAAG,sBAAsB,KAAK,KAAK;AAAA,UACpC;AAAA,QACD;AAAA,QACA,QAAQ,KAAK,mBAAmB,KAAK,qBAAqB;AAAA,QAC1D,cAAc;AAAA,MACf,CAAC;AACD,WAAK,eAAe,aAAa,KAAK;AACtC,WAAK,eAAe,UAAU;AAAA,IAC/B;AAAA,IAEA,sBAAsB,UAAU;AAC/B,UAAI,CAAC,IAAI,QAAQ,GAAG;AACnB,aAAK,mBAAmB,IAAI;AAAA,UAC3B,QAAQ;AAAA,UACR,SAAS;AAAA,QACV,CAAC;AACD,aAAK,mBAAmB,KAAK,GAAG,KAAK,kBAAkB,KAAK,GAAG,cAAc,GAAG;AAChF,aAAK,iBAAiB;AAAA,MACvB,OAAO;AACN,aAAK,mBAAmB,IAAI;AAAA,UAC3B,QAAQ;AAAA,UACR,SAAS;AAAA,QACV,CAAC;AACD,aAAK,mBAAmB;AAAA,UACvB;AAAA,OACG,KAAK,kBAAkB,KAAK,GAAG,YAAY,UAAU,OAAO,QAAQ,EAAE,KAAK,MAAM,GAAG,kBAAkB;AAAA;AAAA,QAE1G;AAAA,MACD;AAAA,IACD;AAAA,IAEA,0BAA0B;AACzB,YAAM,KAAK;AACX,YAAM,EAAE,UAAU,WAAW,IAAI,YAAY,IAAI,MAAM,IAAI,KAAK,iBAAiB,CAAC;AAElF,UAAI,UAAU;AACb,aAAK,kBAAkB;AAAA,UACtB;AAAA;AAAA,QAEI,KAAK,mBAAmB;AAAA;AAAA,oCAEI;AAAA,SAC3B,yBAAyB;AAAA;AAAA,uDAEqB,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOnE;AAAA,MACD,OAAO;AAEN,aAAK,wBAAwB;AAAA,MAC9B;AAEA,eAAS,2BAA2B;AACnC,YAAI,CAAC,YAAY,CAAC,WAAW;AAC5B,iBAAO,8BAA8B,GAAG,4BAA4B;AAAA,QACrE,WAAW,YAAY,CAAC,WAAW;AAClC,iBAAO,8BAA8B;AAAA,QACtC,WAAW,aAAa,CAAC,UAAU;AAClC,iBAAO,8BAA8B;AAAA,QACtC,OAAO;AACN,iBAAO,8BAA8B,cAAc;AAAA,QACpD;AAAA,MACD;AAAA,IACD;AAAA,IAEA,qBAAqB;AACpB,YAAM,EAAE,UAAU,MAAM,IAAI,KAAK,iBAAiB,CAAC;AACnD,UAAI,OAAO;AACV,eAAO,yCAAyC,eAAe;AAAA,MAChE,OAAO;AACN,eAAO,6CAA6C,OAAO,SAAS,QAAQ;AAAA,MAC7E;AAAA,IACD;AAAA,IAEA,sBAAsB,KAAK;AAC1B,UAAI,CAAC;AAAK,cAAM,KAAK,OAAO,QAAQ;AAEpC,WAAK,iBAAiB,IAAI,IAAI,SAAS;AACvC,WAAK,sBAAsB,IAAI,IAAI,KAAK;AACxC,YAAM,cAAc,KAAK,OAAO,aAAa,qBAAqB,IAC/D,IAAI,IAAI,cACR,IAAI,IAAI;AACX,WAAK,mBAAmB,WAAW;AAEnC,WAAK,aAAa,IAAI,IAAI,KAAK;AAAA,IAChC;AAAA,IAEA,iBAAiB,OAAO;AACvB,YAAM,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC3C,WAAK,gBACH,KAAK,sBAAsB,EAC3B,KAAK,QAAQ,GAAG,WAAW,eAAe,gBAAgB,OAAO,QAAQ,SAAS;AAEpF,WAAK,gBACH,KAAK,mBAAmB,EACxB,KAAK,QAAQ,GAAG,WAAW,YAAY,gBAAgB,OAAO,QAAQ,gBAAgB;AAAA,IACzF;AAAA,IAEA,sBAAsB,OAAO;AAC5B,UAAI,iBAAiB;AACrB,YAAM,IAAI,CAAC,SAAS;AACnB,yBAAiB,iBAAiB,KAAK;AAAA,MACxC,CAAC;AAED,WAAK,gBACH,KAAK,2BAA2B,EAChC,KAAK,QAAQ,GAAG,gBAAgB,eAAe,sBAAsB;AAEvE,WAAK,gBACH,KAAK,wBAAwB,EAC7B,KAAK,QAAQ,GAAG,gBAAgB,YAAY,6BAA6B;AAAA,IAC5E;AAAA,IAEA,mBAAmB,OAAO;AACzB,YAAM,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC3C,WAAK,gBACH,KAAK,wBAAwB,EAC7B,KAAK,QAAQ,GAAG,aAAa,eAAe,gBAAgB,OAAO,QAAQ,SAAS;AAEtF,WAAK,gBACH,KAAK,qBAAqB,EAC1B,KAAK,QAAQ,GAAG,aAAa,YAAY,gBAAgB,OAAO,QAAQ,gBAAgB;AAAA,IAC3F;AAAA,IAEA,aAAa,OAAO;AACnB,UAAI,SAAS,MAAM,QAAQ;AAC1B,cAAM,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC3C,cAAM,aAAa,MACjB,IAAI,CAAC,MAAM;AACX,cAAI,EAAE,oCAAoC;AAAK;AAE/C,gBAAM,cAAc,SAAS,KAAK,EAAE,WAAW,IAC5C,EAAE,cACF,EAAE,QAAQ,IACV,GAAG,EAAE,iBAAiB,EAAE,UACxB,EAAE;AACL,iBAAO;AAAA,8BACkB;AAAA,8BACA,gBAAgB,EAAE,kCAAkC,QAAQ;AAAA;AAAA,QAEtF,CAAC,EACA,KAAK,EAAE;AACT,aAAK,gBAAgB,KAAK,kBAAkB,EAAE,IAAI,WAAW,MAAM,EAAE,KAAK,UAAU;AAAA,MACrF,OAAO;AACN,aAAK,gBAAgB,KAAK,kBAAkB,EAAE,IAAI,WAAW,MAAM,EAAE,KAAK,EAAE;AAAA,MAC7E;AAAA,IACD;AAAA,IAEA,cAAc,EAAE,KAAK,GAAG;AACvB,YAAM,gBAAgB,qCAAqC,OAAO,IAAI;AACtE,aAAO,KAAK,oBAAoB,KAAK,aAAa;AAAA,IACnD;AAAA,IAEA,kBAAkB,MAAM;AACvB,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,aAAO,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,QAAQ,KAAK,IAAI;AAAA,IACjD;AAAA,IAEA,iBAAiB,MAAM,aAAa;AACnC,YAAM,QAAQ,KAAK,cAAc,IAAI;AAErC,UAAI,aAAa;AAChB,iBAAS,MAAM,KAAK,EAAE,OAAO,KAAK,MAAM,OAAO;AAAA,MAChD,OAAO;AACN,cAAM,WAAW,KAAK,kBAAkB,IAAI;AAC5C,aAAK,iBAAiB,UAAU,KAAK;AAAA,MACtC;AAEA,YAAM,mBAAmB,KAAK,oBAAoB,KAAK,oBAAoB,EAAE;AAC7E,WAAK,uBAAuB,mBAAmB,CAAC;AAEhD,WAAK,0BAA0B,gBAAgB;AAAA,IAChD;AAAA,IAEA,iBAAiB,WAAW,iBAAiB;AAC5C,YAAM,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC3C,YAAM,KAAK;AAEX,UAAI,CAAC,gBAAgB,QAAQ;AAC5B,aAAK,oBAAoB;AAAA,UACxB,iDAAiD,OAAO,UAAU,IAAI;AAAA;AAAA,QAEvE;AACA,0BAAkB,KAAK,cAAc,SAAS;AAAA,MAC/C;AAEA,sBAAgB;AAAA,QACf,GAAG,oBAAoB;AAAA;AAAA;AAAA,OAGnB,UAAU;AAAA;AAAA,MAEX,qBAAqB;AAAA;AAAA,KAEtB,uBAAuB;AAAA,MAC1B;AAEA,oCAA8B;AAE9B,eAAS,gCAAgC;AACxC,cAAM,YAAY,MAAM,KAAK,GAAG,oBAAoB,KAAK,mBAAmB,CAAC;AAC7E,WAAG,aAAa,KAAK,qBAAqB,EAAE,IAAI,SAAS,EAAE;AAC3D,WAAG,oBAAoB,KAAK,mBAAmB,EAAE,IAAI,SAAS,EAAE;AAChE,YAAI,YAAY,UAAU,OAAO,CAACC,YAAW,QAAQ;AACpD,cAAI,EAAE,GAAG,EAAE,MAAM,IAAIA;AAAW,YAAAA,aAAY,EAAE,GAAG,EAAE,MAAM;AACzD,iBAAOA;AAAA,QACR,GAAG,CAAC;AAEJ,qBAAa;AACb,YAAI,aAAa;AAAG,sBAAY;AAEhC,WAAG,aAAa,KAAK,qBAAqB,EAAE,IAAI,SAAS,SAAS;AAClE,WAAG,oBAAoB,KAAK,mBAAmB,EAAE,IAAI,SAAS,SAAS;AAAA,MACxE;AAEA,eAAS,yBAAyB;AACjC,YAAI,UAAU,QAAQ,UAAU,UAAU,UAAU,SAAS,UAAU,QAAQ;AAC9E,iBAAO;AAAA;AAAA,oCAEyB,UAAU,OAAO,KAAK,UAAU;AAAA;AAAA,gCAEpC,gBAAgB,UAAU,QAAQ,QAAQ;AAAA,kCACxC,gBAAgB,UAAU,MAAM,QAAQ;AAAA;AAAA;AAAA,QAGvE,OAAO;AACN,iBAAO;AAAA;AAAA,oCAEyB,UAAU,OAAO,KAAK,UAAU;AAAA;AAAA,gCAEpC,gBAAgB,UAAU,MAAM,QAAQ;AAAA;AAAA;AAAA,QAGrE;AAAA,MACD;AAEA,eAAS,uBAAuB;AAC/B,YAAI,UAAU,aAAa;AAC1B,cAAI,UAAU,YAAY,QAAQ,OAAO,KAAK,IAAI;AACjD,gBAAI;AACH,wBAAU,cAAc,EAAE,UAAU,WAAW,EAAE,KAAK;AAAA,YACvD,SAAS,OAAP;AACD,wBAAU,cAAc,UAAU,YAChC,QAAQ,UAAU,GAAG,EACrB,QAAQ,YAAY,GAAG,EACvB,QAAQ,OAAO,GAAG;AAAA,YACrB;AAAA,UACD;AACA,oBAAU,cAAc,OAAO,SAAS,UAAU,aAAa,EAAE;AACjE,iBAAO,0BAA0B,UAAU;AAAA,QAC5C;AACA,eAAO;AAAA,MACR;AAEA,eAAS,sBAAsB;AAC9B,cAAM,EAAE,OAAO,UAAU,IAAI;AAC7B,YAAI,CAAC,GAAG,eAAe,OAAO;AAC7B,iBAAO;AAAA;AAAA;AAAA;AAAA,cAIG,eAAe,OAAO,SAAS,SAAS;AAAA;AAAA,QAEnD,OAAO;AACN,iBAAO,qCAAqC,OAAO,SAAS,SAAS;AAAA,QACtE;AAAA,MACD;AAAA,IACD;AAAA,IAEA,oBAAoB,MAAM;AACzB,YAAM,YAAY,EAAE,IAAI,EAAE,KAAK,KAAK;AACpC,QAAE,IAAI,EAAE,OAAO,EAAE,YAAY,qCAAqC,iBAAiB;AAAA,IACpF;AAAA,IAEA,mCAAmC,UAAU,OAAO,MAAM;AACzD,YAAM,kBAAkB,KAAK,cAAc,IAAI;AAC/C,sBAAgB,KAAK,QAAQ,YAAY,OAAO,KAAK,CAAC;AAAA,IACvD;AAAA,IAEA,oBAAoB,eAAe;AAClC,UAAI,eAAe;AAClB,aAAK,gBAAgB,KAAK,eAAe,EAAE,IAAI,WAAW,MAAM;AAChE,aAAK,gBAAgB,KAAK,gBAAgB,EAAE,IAAI,WAAW,MAAM;AAAA,MAClE,OAAO;AACN,aAAK,gBAAgB,KAAK,eAAe,EAAE,IAAI,WAAW,MAAM;AAChE,aAAK,gBAAgB,KAAK,gBAAgB,EAAE,IAAI,WAAW,MAAM;AAAA,MAClE;AAAA,IACD;AAAA,IAEA,uBAAuB,QAAQ;AAC9B,UAAI,QAAQ;AACX,aAAK,mBAAmB,IAAI,WAAW,MAAM;AAC7C,aAAK,gBAAgB,KAAK,eAAe,EAAE,IAAI;AAAA,UAC9C,oBAAoB;AAAA,QACrB,CAAC;AAAA,MACF,OAAO;AACN,aAAK,mBAAmB,IAAI,WAAW,MAAM;AAC7C,aAAK,gBAAgB,KAAK,eAAe,EAAE,IAAI;AAAA,UAC9C,oBAAoB;AAAA,QACrB,CAAC;AAAA,MACF;AAAA,IACD;AAAA,IAEA,0BAA0B,kBAAkB;AAC3C,YAAM,mBAAmB,KAAK,oBAAoB,KAAK,kBAAkB;AAGzE,yBAAmB,KAClB,oBACA,iBAAiB,OAAO,KACxB,KAAK,aAAa,IAAI,WAAW,MAAM;AAExC,2BAAqB,KAAK,CAAC,iBAAiB,UAAU,KAAK,0BAA0B;AAAA,IACtF;AAAA,IAEA,gBAAgB,MAAM;AACrB,YAAM,iBAAiB,KAAK,KAAK,mBAAmB;AACpD,YAAM,uBAAuB,CAAC,OAAO,uBAAuB,MAAM,EAAE,SAAS,cAAc;AAC3F,YAAM,oBAAoB,uBACtB,kBAAkB,UAAU,KAAK,qBACjC,kBAAkB,yBAAyB,KAAK,yBACjD,kBAAkB,QAClB;AAEH,YAAM,0BAA0B,KAAK,gBAAgB;AACrD,YAAM,oBAAoB,CAAC,KAAK;AAChC,YAAM,wBAAwB,KAAK,eAAe,KAAK,eAAe;AAEtE,UAAI,sBAAsB;AACzB,YAAI,CAAC,mBAAmB;AACvB,gBAAM,QAAQ,kBAAkB,SAAS,OAAO,KAAK,IAAI,WAAW,KAAK;AACzE,gBAAM,UAAU,GAAG,0DAA0D,CAAC,KAAK,CAAC;AACpF,iBAAO,WAAW;AAAA,YACjB,WAAW;AAAA,YACX;AAAA,UACD,CAAC;AACD,iBAAO,MAAM,WAAW,OAAO;AAC/B;AAAA,QACD;AACA,aAAK,qBAAqB,MAAM,cAAc;AAE9C,YAAI,qBAAqB,uBAAuB;AAC/C,eAAK,cAAc;AAAA,QACpB,WAAW,yBAAyB;AACnC,eAAK,cAAc;AAAA,QACpB;AACA,aAAK,eAAe;AAAA,MACrB,WAAW,mBAAmB,YAAY;AACzC,aAAK,cAAc;AACnB,aAAK,sBAAsB;AAC3B,aAAK,OAAO,aAAa,QAAW,cAAc;AAClD;AAAA,MACD,WAAW,mBAAmB,UAAU;AACvC,aAAK,cAAc;AACnB,aAAK,sBAAsB;AAC3B,aAAK,OAAO,aAAa,QAAW,cAAc;AAClD;AAAA,MACD,OAAO;AACN,aAAK,eACJ,mBAAmB,WAChB,KAAK,aAAa,MAAM,GAAG,EAAE,IAC7B,KAAK,eAAe;AACxB,aAAK,eAAe,KAAK,gBAAgB;AAAA,MAC1C;AAEA,YAAM,sCAAsC,CAAC,wBAAwB;AAErE,UAAI,qCAAqC;AACxC,eAAO,WAAW;AAAA,UACjB,WAAW;AAAA,UACX,SAAS,GAAG,2CAA2C;AAAA,QACxD,CAAC;AACD,eAAO,MAAM,WAAW,OAAO;AAC/B;AAAA,MACD;AAEA,UAAI,IAAI,KAAK,YAAY,IAAI,OAAO,KAAK,gBAAgB,uBAAuB;AAC/E,eAAO,WAAW;AAAA,UACjB,SAAS,GAAG,sCAAsC;AAAA,UAClD,WAAW;AAAA,QACZ,CAAC;AACD,eAAO,MAAM,WAAW,OAAO;AAC/B,aAAK,eAAe;AAAA,MACrB;AAEA,WAAK,OAAO,aAAa,KAAK,cAAc,KAAK,WAAW;AAAA,IAC7D;AAAA,IAEA,qBAAqB,MAAM,aAAa;AACvC,YAAM,6BAA6B,KAAK,SAAS,wBAAwB;AACzE,YAAM,wBAAwB,CAAC,OAAO,uBAAuB,QAAQ,MAAM,EAAE,SAAS,WAAW;AAEjG,UAAI,CAAC,4BAA4B;AAChC,aAAK,SAAS,wBAAwB;AAAA,MACvC;AACA,UAAI,KAAK,gBAAgB,eAAe,4BAA4B;AAEnE,aAAK,YAAY,wBAAwB;AAAA,MAC1C;AACA,UAAI,KAAK,eAAe,KAAK,gBAAgB,eAAe,uBAAuB;AAElF,cAAM,WAAW,EAAE,uBAAuB,KAAK,eAAe;AAC9D,iBAAS,YAAY,wBAAwB;AAAA,MAC9C;AACA,UAAI,CAAC,yBAAyB,gBAAgB,QAAQ;AAErD,mBAAW,MAAM;AAChB,eAAK,YAAY,wBAAwB;AAAA,QAC1C,GAAG,GAAG;AAAA,MACP;AAAA,IACD;AAAA,IAEA,cAAc,MAAM;AACnB,UAAI,MAAM;AACT,aAAK,gBAAgB,IAAI,WAAW,MAAM;AAC1C,aAAK,gBAAgB,IAAI,WAAW,MAAM;AAAA,MAC3C,OAAO;AACN,aAAK,gBAAgB,IAAI,WAAW,MAAM;AAC1C,aAAK,gBAAgB,IAAI,WAAW,MAAM;AAAA,MAC3C;AACA,WAAK,aAAa;AAAA,IACnB;AAAA,IAEA,eAAe;AACd,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,gBAAgB,KAAK,yBAAyB,EAAE,YAAY,wBAAwB;AAAA,IAC1F;AAAA,IAEA,yBAAyB,WAAW;AACnC,UAAI,CAAC,OAAO,uBAAuB,MAAM,EAAE,SAAS,SAAS,GAAG;AAC/D,aAAK,gBAAgB,KAAK,uBAAuB,aAAa,EAAE,MAAM;AAAA,MACvE;AAAA,IACD;AAAA,IAEA,qBAAqB,MAAM;AAC1B,UAAI,MAAM;AACT,cAAM,EAAE,SAAS,IAAI,KAAK,iBAAiB,CAAC;AAE5C,aAAK,gBAAgB,IAAI,WAAW,MAAM;AAC1C,aAAK,kBAAkB,IAAI;AAAA,UAC1B,QAAQ;AAAA,UACR,eAAe;AAAA,QAChB,CAAC;AACD,aAAK,kBAAkB,KAAK,mBAAmB,EAAE;AAAA,UAChD;AAAA,0BACsB,GAAG,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAQvC,KAAK,mBAAmB;AAAA;AAAA,mCAEI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sCAUG,GAAG,qBAAqB;AAAA,QAC3D;AAEA,aAAK,kBAAkB,OAAO,2CAA2C;AAEzE,aAAK,uBAAuB;AAC5B,aAAK,4BAA4B;AAAA,MAClC,OAAO;AACN,aAAK,gBAAgB,IAAI,WAAW,MAAM;AAC1C,aAAK,kBAAkB,IAAI;AAAA,UAC1B,QAAQ;AAAA,UACR,eAAe;AAAA,QAChB,CAAC;AAED,aAAK,wBAAwB;AAAA,MAC9B;AAAA,IACD;AAAA,IAEA,yBAAyB;AACxB,YAAM,iBAAiB,KAAK,kBAAkB,KAAK,4BAA4B;AAE/E,YAAM,MAAM;AAAA,QACX;AAAA,UACC,WAAW;AAAA,UACX,OAAO,GAAG,OAAO;AAAA,UACjB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,aAAa,GAAG,wBAAwB;AAAA,QACzC;AAAA,QACA;AAAA,UACC,WAAW;AAAA,UACX,OAAO,GAAG,cAAc;AAAA,UACxB,WAAW;AAAA,UACX,aAAa,GAAG,+BAA+B;AAAA,QAChD;AAAA,QACA;AAAA,UACC,WAAW;AAAA,UACX,OAAO,GAAG,iBAAiB;AAAA,UAC3B,WAAW;AAAA,UACX,SAAS;AAAA,UACT,aAAa,GAAG,wBAAwB;AAAA,QACzC;AAAA,QACA;AAAA,UACC,WAAW;AAAA,UACX,OAAO,GAAG,gBAAgB;AAAA,UAC1B,WAAW;AAAA,UACX,WAAW;AAAA,QACZ;AAAA,MACD;AAEA,YAAM,KAAK;AACX,UAAI,QAAQ,CAAC,OAAO;AAh6BtB;AAi6BG,aAAK,YAAY,GAAG,qBAAqB,OAAO,GAAG,KAAK,aAAa;AAAA,UACpE;AAAA,UACA,QAAQ,eAAe,KAAK,IAAI,GAAG,iBAAiB;AAAA,UACpD,cAAc;AAAA,QACf,CAAC;AACD,mBAAK,YAAY,GAAG,mBAAmB,WAAvC,mBAA+C,GAAG,QAAQ,MAAM;AAC/D,uCAA6B,MAAM,KAAK,YAAY,GAAG,kBAAkB;AAAA,QAC1E;AACA,aAAK,YAAY,GAAG,mBAAmB,UAAU,KAAK,cAAc,GAAG,UAAU;AAAA,MAClF,CAAC;AAED,eAAS,+BAA+B;AACvC,cAAM,gBAAgB,GAAG,cAAc,KAAK,GAAG;AAC/C,cAAM,mBAAmB,GAAG,cAAc;AAE1C,YAAI,KAAK,SAAS,iBAAiB,KAAK,SAAS,KAAK,GAAG,aAAa,kBAAkB;AACvF,iBAAO,KAAK;AAAA,YACX,QAAQ;AAAA,YACR,MAAM;AAAA,cACL,WAAW,KAAK,GAAG;AAAA,cACnB,UAAU;AAAA,cACV,OAAO,KAAK;AAAA,YACb;AAAA,YACA,UAAU,CAAC,MAAM;AAChB,kBAAI,CAAC,EAAE,KAAK;AACX,mBAAG,cAAc,KAAK,GAAG,aAAa,KAAK;AAC3C,uBAAO,WAAW;AAAA,kBACjB,SAAS,GAAG,wCAAwC;AAAA,kBACpD,WAAW;AAAA,gBACZ,CAAC;AACD,uBAAO,MAAM,WAAW,QAAQ;AAAA,cACjC;AAAA,YACD;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AAAA,IACD;AAAA,IAEA,8BAA8B;AAC7B,aAAO,GACL,SAAS,eAAe;AAAA,QACxB,SAAS,EAAE,UAAU,KAAK,cAAc,UAAU,WAAW,EAAE;AAAA,QAC/D,QAAQ,CAAC,QAAQ,eAAe,UAAU,gBAAgB,gBAAgB,UAAU;AAAA,QACpF,OAAO;AAAA,MACR,CAAC,EACA,KAAK,CAAC,QAAQ;AACd,cAAM,wBAAwB,KAAK,kBAAkB,KAAK,wBAAwB;AAElF,YAAI,CAAC,IAAI,QAAQ;AAChB,gCAAsB;AAAA,YACrB,4CAA4C,GAAG,8BAA8B;AAAA,UAC9E;AACA;AAAA,QACD;AAEA,cAAM,eAAe,OAAO,IAAI,GAAG,eAAe,MAAM,IAAI,GAAG,YAAY,EAAE,QAAQ;AACrF,aAAK,kBACH,KAAK,gBAAgB,EACrB,KAAK,GAAG,GAAG,iBAAiB,KAAK,GAAG,YAAY,GAAG;AAErD,YAAI,QAAQ,CAAC,YAAY;AACxB,gBAAM,mBAAmB,OAAO,SAAS;AAAA,YACxC,QAAQ,eAAe,MAAM,QAAQ;AAAA,UACtC;AACA,cAAI,kBAAkB;AAAA,YACrB,MAAM;AAAA,YACN,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,cAAc;AAAA,UACf;AAEA,gCAAsB;AAAA,YACrB,mDAAmD,OAAO,QAAQ,IAAI;AAAA;AAAA,mCAEzC,QAAQ;AAAA,mCACR;AAAA;AAAA;AAAA;AAAA,UAIzB,gBAAgB,QAAQ,aAAa,QAAQ,UAAU,CAAC,KAAK;AAAA;AAAA;AAAA,wDAGf,gBAAgB,QAAQ;AAAA,iBAC/D,GAAG,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAM7B;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,2BAA2B,KAAK;AAC/B,QAAE,IAAI,OAAO,EAAE,IAAI,gBAAgB;AACnC,QAAE,IAAI,OAAO,EAAE,GAAG,kBAAkB,MAAM;AACzC,YAAI,IAAI,IAAI,MAAM,QAAQ;AACzB,eAAK,oBAAoB,KAAK,EAAE;AAChC,cAAI,IAAI,MAAM,QAAQ,CAAC,SAAS;AAC/B,iBAAK,iBAAiB,IAAI;AAAA,UAC3B,CAAC;AAAA,QACF;AACA,aAAK,sBAAsB,GAAG;AAAA,MAC/B,CAAC;AAAA,IACF;AAAA,IAEA,eAAe;AACd,YAAM,MAAM,KAAK,OAAO,QAAQ;AAEhC,WAAK,2BAA2B,GAAG;AAEnC,WAAK,uBAAuB,IAAI,IAAI,QAAQ,EAAE,KAAK,MAAM;AACxD,aAAK,OAAO,yBAAyB,KAAK,aAAa;AACvD,aAAK,wBAAwB;AAAA,MAC9B,CAAC;AAED,WAAK,oBAAoB,KAAK,EAAE;AAChC,UAAI,IAAI,IAAI,MAAM,QAAQ;AACzB,YAAI,IAAI,MAAM,QAAQ,CAAC,SAAS;AAC/B,eAAK,iBAAiB,IAAI;AAAA,QAC3B,CAAC;AAAA,MACF,OAAO;AACN,aAAK,0BAA0B;AAC/B,aAAK,uBAAuB,KAAK;AAAA,MAClC;AAEA,WAAK,sBAAsB,IAAI,IAAI,8BAA8B;AACjE,WAAK,sBAAsB,GAAG;AAE9B,UAAI,IAAI,IAAI,cAAc,GAAG;AAC5B,aAAK,gBAAgB,KAAK,eAAe,EAAE,IAAI,WAAW,MAAM;AAChE,aAAK,gBAAgB,KAAK,gBAAgB,EAAE,IAAI,WAAW,MAAM;AAAA,MAClE,OAAO;AACN,aAAK,gBAAgB,KAAK,eAAe,EAAE,IAAI,WAAW,MAAM;AAChE,aAAK,gBAAgB,KAAK,gBAAgB,EAAE,IAAI,WAAW,MAAM;AAAA,MAClE;AAEA,WAAK,iBAAiB,IAAI;AAAA,IAC3B;AAAA,IAEA,iBAAiB,MAAM;AACtB,aAAO,KAAK,WAAW,IAAI,WAAW,MAAM,IAAI,KAAK,WAAW,IAAI,WAAW,MAAM;AAAA,IACtF;AAAA,EACD;;;ACjjCA,sBAAmB;AAEnB,UAAQ,eAAe,eAAe,MAAM;AAAA,IAE3C,YAAY,EAAE,KAAK,SAAS,QAAQ,aAAa,SAAS,GAAG;AAkB9D;AAjBE,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,cAAc;AACnB,WAAK,cAAc,SAAS;AAC5B,WAAK,gBAAgB,SAAS;AAE9B,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,YAAY;AACjB,WAAK,iBAAiB;AACtB,WAAK,wBAAwB;AAAA,IAC9B;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ;AAAA,QACZ;AAAA;AAAA,0BAEuB,GAAG,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMtC;AAEA,WAAK,aAAa,KAAK,QAAQ,KAAK,iBAAiB;AACrD,WAAK,mBAAmB,KAAK,WAAW,KAAK,kBAAkB;AAG/D,WAAK,WAAW,SAAS,gBAAgB;AACzC,WAAK,iBAAiB,SAAS,uBAAuB;AAAA,IACvD;AAAA,IAEA,0BAA0B;AAEzB,UAAI,CAAC,SAAS,eAAe,uBAAuB,GAAG;AACtD,cAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,cAAM,KAAK;AACX,cAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2QpB,iBAAS,KAAK,YAAY,KAAK;AAAA,MAChC;AAGA,WAAK,sBAAsB;AAAA,IAC5B;AAAA,IAEA,wBAAwB;AACvB,UAAI,OAAO,gBAAgB;AAC1B,aAAK,iBAAiB,IAAI,eAAe,CAAC,YAAY;AACrD,mBAAS,SAAS,SAAS;AAC1B,iBAAK,wBAAwB,MAAM,WAAW;AAAA,UAC/C;AAAA,QACD,CAAC;AAED,aAAK,eAAe,QAAQ,KAAK,WAAW,EAAE;AAAA,MAC/C,OAAO;AAEN,UAAE,MAAM,EAAE,GAAG,uBAAuB,OAAO,MAAM,SAAS,MAAM;AAC/D,eAAK,qBAAqB;AAAA,QAC3B,GAAG,GAAG,CAAC;AAAA,MACR;AAAA,IACD;AAAA,IAEA,wBAAwB,MAAM;AAC7B,YAAM,QAAQ,KAAK;AAGnB,UAAI,QAAQ,KAAK;AAChB,aAAK,iBAAiB,IAAI,yBAAyB,gBAAgB;AAAA,MACpE,WAAW,QAAQ,KAAK;AACvB,aAAK,iBAAiB,IAAI,yBAAyB,gBAAgB;AAAA,MACpE,WAAW,QAAQ,KAAK;AACvB,aAAK,iBAAiB,IAAI,yBAAyB,gBAAgB;AAAA,MACpE,WAAW,QAAQ,KAAK;AACvB,aAAK,iBAAiB,IAAI,yBAAyB,gBAAgB;AAAA,MACpE,WAAW,QAAQ,MAAM;AACxB,aAAK,iBAAiB,IAAI,yBAAyB,gBAAgB;AAAA,MACpE;AAAA,IAED;AAAA,IAEA,uBAAuB;AAEtB,WAAK,WAAW,KAAK,EAAE,KAAK;AAAA,IAC7B;AAAA,IAEA,MAAM,kBAAkB;AACvB,UAAI,CAAC,KAAK,YAAY;AACrB,eAAO,KAAK;AAAA,UACX,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,UAAU,CAAC,MAAM;AAChB,gBAAI,EAAE;AAAS,mBAAK,oBAAoB,EAAE;AAAA,UAC3C;AAAA,QACD,CAAC;AAAA,MACF;AACA,UAAI,CAAC,KAAK,YAAY;AACrB,cAAM,MAAM,MAAM,OAAO,GAAG,UAAU,eAAe,KAAK,aAAa,oBAAoB;AAC3F,aAAK,aAAa,IAAI,QAAQ;AAAA,MAC/B;AAEA,WAAK,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM;AACxC,aAAK,iBAAiB,QAAQ,KAAK;AAAA,MACpC,CAAC;AAAA,IACF;AAAA,IAEA,UAAU,EAAE,QAAQ,GAAG,cAAc,IAAI,cAAc,GAAG,GAAG;AAC5D,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,YAAM,aAAc,OAAO,IAAI,sBAAuB,KAAK;AAC3D,UAAI,EAAE,YAAY,YAAY,IAAI;AAElC,OAAC,eAAe,aAAa,KAAK;AAElC,aAAO,OAAO,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM,EAAE,OAAO,aAAa,YAAY,YAAY,aAAa,YAAY;AAAA,MAC9E,CAAC;AAAA,IACF;AAAA,IAEA,iBAAiB,OAAO;AACvB,WAAK,iBAAiB,KAAK,EAAE;AAE7B,YAAM,QAAQ,CAAC,SAAS;AACvB,cAAM,YAAY,KAAK,cAAc,IAAI;AACzC,aAAK,iBAAiB,OAAO,SAAS;AAAA,MACvC,CAAC;AAAA,IACF;AAAA,IAEA,cAAc,MAAM;AACnB,YAAM,KAAK;AAEX,YAAM,EAAE,YAAY,WAAW,UAAU,SAAS,YAAY,KAAK,gBAAgB,IAAI;AACvF,YAAMC,aAAY,IAAI,iBAAiB,CAAC,IAAI,KAAK,IAAI,IAAI;AACzD,UAAI;AACJ,UAAI,iBAAiB;AAErB,UAAI,KAAK,eAAe;AACvB,0BAAkB,aAAa,KAAK,UAAU,cAAc,IAAI,QAAQ;AAExE,YAAI,KAAK,MAAM,cAAc,IAAI,KAAK;AACrC,2BAAiB,KAAK,MAAM,cAAc,IAAI;AAC9C,2BAAiB,eAAe,QAAQ,CAAC,IAAI;AAAA,QAC9C;AAAA,MACD,OAAO;AACN,0BAAkB;AAClB,yBAAiB;AAAA,MAClB;AAEA,eAAS,sBAAsB;AAC9B,YAAI,CAAC,GAAG,eAAe,YAAY;AAClC,iBAAO;AAAA,uDAC4C,0CAA0C;AAAA;AAAA;AAAA;AAAA;AAAA,2DAKtC;AAAA,eAC5C,OAAO,SAAS,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA,QAI1C,OAAO;AACN,iBAAO;AAAA,uDAC4C,0CAA0C;AAAA;AAAA,uCAE1D,OAAO,SAAS,KAAK,SAAS;AAAA,QAClE;AAAA,MACD;AAGA,aAAO;AAAA,sBACa,OAAO,KAAK,SAAS,sBAAsB,OAAO,SAAS;AAAA,qBAC5D,OAAO,QAAQ,gBAAgB,OAAO,GAAG;AAAA,iBAC7C,OAAO,mBAAmB,CAAC;AAAA,sBACtB,OAAO,KAAK,SAAS;AAAA,aAC9B,KAAK;AAAA;AAAA,MAEZ,oBAAoB;AAAA;AAAA;AAAA;AAAA,QAIlB,OAAO,SAAS,KAAK,WAAW,EAAE;AAAA;AAAA,8BAEZ,gBAAgB,iBAAiB,KAAK,UAAUA,UAAS,KAAK,OAAO;AAAA;AAAA;AAAA,IAGlG;AAAA,IAEA,oBAAoB,MAAM;AACzB,YAAM,YAAY,EAAE,IAAI,EAAE,KAAK,KAAK;AACpC,QAAE,IAAI,EAAE,OAAO,EAAE,YAAY,kCAAkC,iBAAiB;AAAA,IACjF;AAAA,IAEA,kBAAkB;AACjB,YAAM,KAAK;AACX,WAAK,WAAW,KAAK,eAAe,EAAE,KAAK,EAAE;AAC7C,WAAK,WAAW,KAAK,mBAAmB,EAAE,KAAK,EAAE;AAEjD,WAAK,eAAe,OAAO,GAAG,KAAK,aAAa;AAAA,QAC/C,IAAI;AAAA,UACH,OAAO,GAAG,QAAQ;AAAA,UAClB,WAAW;AAAA,UACX,aAAa,GAAG,+CAA+C;AAAA,QAChE;AAAA,QACA,QAAQ,KAAK,WAAW,KAAK,eAAe;AAAA,QAC5C,cAAc;AAAA,MACf,CAAC;AACD,WAAK,mBAAmB,OAAO,GAAG,KAAK,aAAa;AAAA,QACnD,IAAI;AAAA,UACH,OAAO,GAAG,YAAY;AAAA,UACtB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,aAAa,GAAG,mBAAmB;AAAA,UACnC,UAAU,WAAY;AACrB,eAAG,aAAa,KAAK;AACrB,aAAC,GAAG,eAAe,GAAG,aAAa,GAAG;AACtC,eAAG,aAAa;AAAA,UACjB;AAAA,UACA,WAAW,WAAY;AACtB,kBAAM,MAAM,GAAG,OAAO,QAAQ,EAAE;AAChC,mBAAO;AAAA,cACN,OAAO;AAAA,cACP,SAAS;AAAA,gBACR,aAAa,MAAM,IAAI,cAAc;AAAA,cACtC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ,KAAK,WAAW,KAAK,mBAAmB;AAAA,QAChD,cAAc;AAAA,MACf,CAAC;AACD,WAAK,aAAa,aAAa,KAAK;AACpC,WAAK,iBAAiB,aAAa,KAAK;AAExC,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,mBAAmB;AAClB,WAAK,aAAa,SAAS,KAAK,gBAAgB,EAAE;AAAA,QACjD;AAAA,+CAC4C,GAAG,OAAO;AAAA,OAClD,OAAO,MAAM,KAAK,SAAS,IAAI;AAAA;AAAA;AAAA,MAGpC;AAEA,WAAK,oBAAoB,KAAK,aAAa,SAAS,KAAK,WAAW;AAEpE,WAAK,kBAAkB,GAAG,SAAS,KAAK,MAAM;AAC7C,aAAK,iBAAiB,EAAE;AACxB,aAAK,aAAa,UAAU;AAAA,MAC7B,CAAC;AAAA,IACF;AAAA,IAEA,iBAAiB,OAAO;AACvB,QAAE,KAAK,aAAa,OAAO,EAAE,EAAE,IAAI,KAAK,EAAE,QAAQ,OAAO;AAAA,IAC1D;AAAA,IAEA,cAAc;AACb,YAAM,KAAK;AACX,aAAO,SAAS,cAAAC;AAEhB,oBAAAA,QAAO,iBAAiB,SAAU,QAAQ;AACzC,YAAI,QAAQ,KAAK,qBAAqB,MAAM;AAC5C,gBAAQ;AAAA,gBACF,SAAS,MAAM,SAAS;AAAA,gBACxB,SAAS,OAAO,SAAS;AAAA,gBACxB,SAAS,OAAO,SAAS,OAAQ,SAAS;AAAA,gBAC3C,SAAS,OAAO,SAAS;AAAA,gBACzB,SAAS,OAAO,SAAS;AAAA,eACzB,SAAS;AACb,gBAAI,OAAO,QAAQ,UAAa,OAAO,QAAQ,IAAI;AAClD,qBAAO,OAAO;AAAA,YACf;AAEA,gBAAI,WAAW,OAAO,aAAa,KAAK;AACxC,oBAAQ,OAAO;AAAA,mBACT;AACJ,2BAAW,SAAS,YAAY;AAChC;AAAA,mBACI;AACJ,2BAAW,SAAS,YAAY;AAChC;AAAA;AAEF,mBAAO;AAAA,gBACH,SAAS,MAAM,SAAS;AAC5B,mBAAO,KAAK,QAAQ;AAAA;AAEtB,eAAO;AAAA,MACR;AAEA,oBAAAA,QAAO,SAAS,UAAU;AAAA,QACzB,QAAQ,CAAC,cAAc;AACtB,cAAI,KAAK,gBAAgB,KAAK,WAAW,GAAG,UAAU,GAAG;AACxD,iBAAK,aAAa,UAAU;AAC5B,iBAAK,iBAAiB,SAAS;AAC/B,iBAAK,kBAAkB;AAAA,UACxB;AAAA,QACD;AAAA,MACD,CAAC;AAED,WAAK,WAAW,GAAG,SAAS,iBAAiB,WAAY;AACxD,cAAM,QAAQ,EAAE,IAAI;AACpB,cAAM,YAAY,SAAS,MAAM,KAAK,gBAAgB,CAAC;AACvD,YAAI,WAAW,SAAS,MAAM,KAAK,eAAe,CAAC;AACnD,YAAI,YAAY,SAAS,MAAM,KAAK,gBAAgB,CAAC;AACrD,YAAI,MAAM,SAAS,MAAM,KAAK,UAAU,CAAC;AACzC,YAAI,OAAO,SAAS,MAAM,KAAK,WAAW,CAAC;AAC3C,YAAI,YAAY,SAAS,MAAM,KAAK,gBAAgB,CAAC;AAGrD,mBAAW,aAAa,cAAc,SAAY;AAClD,oBAAY,cAAc,cAAc,SAAY;AACpD,cAAM,QAAQ,cAAc,SAAY;AACxC,eAAO,SAAS,cAAc,SAAY;AAC1C,oBAAY,cAAc,cAAc,SAAY;AAEpD,WAAG,OAAO,cAAc;AAAA,UACvB,OAAO;AAAA,UACP,OAAO;AAAA,UACP,MAAM,EAAE,WAAW,UAAU,WAAW,KAAK,MAAM,UAAU;AAAA,QAC9D,CAAC;AACD,WAAG,aAAa,UAAU;AAAA,MAC3B,CAAC;AAED,WAAK,aAAa,OAAO,GAAG,SAAS,CAAC,MAAM;AAC3C,qBAAa,KAAK,WAAW;AAC7B,aAAK,cAAc,WAAW,MAAM;AACnC,gBAAM,cAAc,EAAE,OAAO;AAC7B,eAAK,aAAa,EAAE,YAAY,CAAC;AAAA,QAClC,GAAG,GAAG;AAEN,aAAK,kBAAkB,OAAO,QAAQ,KAAK,aAAa,OAAO,IAAI,CAAC,CAAC;AAAA,MACtE,CAAC;AAED,WAAK,aAAa,OAAO,GAAG,SAAS,MAAM;AAC1C,aAAK,kBAAkB,OAAO,QAAQ,KAAK,aAAa,OAAO,IAAI,CAAC,CAAC;AAAA,MACtE,CAAC;AAAA,IACF;AAAA,IAEA,mBAAmB;AAClB,YAAM,aAAa,OAAO,MAAM,OAAO,IAAI,WAAM;AACjD,WAAK,aAAa,OAAO,KAAK,SAAS,GAAG,cAAc;AACxD,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM,KAAK,aAAa,UAAU;AAAA,QAC1C,WAAW,MAAM,KAAK,WAAW,GAAG,UAAU;AAAA,QAC9C,aAAa,GAAG,uBAAuB;AAAA,QACvC,eAAe;AAAA,QACf,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AACD,WAAK,iBAAiB,OAAO,KAAK,SAAS,GAAG,cAAc;AAC5D,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM,KAAK,iBAAiB,UAAU;AAAA,QAC9C,WAAW,MAAM,KAAK,WAAW,GAAG,UAAU;AAAA,QAC9C,aAAa,GAAG,4BAA4B;AAAA,QAC5C,eAAe;AAAA,QACf,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AAGD,aAAO,GAAG,KAAK,GAAG,SAAS,MAAM;AAChC,cAAM,sBAAsB,KAAK,WAAW,GAAG,UAAU;AACzD,YAAI,CAAC,uBAAuB,KAAK,aAAa,UAAU,MAAM;AAAI;AAElE,YAAI,KAAK,MAAM,UAAU,GAAG;AAC3B,eAAK,iBAAiB,KAAK,eAAe,EAAE,MAAM;AAClD,iBAAO,MAAM,WAAW,QAAQ;AAChC,eAAK,iBAAiB,EAAE;AAAA,QACzB,WAAW,KAAK,MAAM,UAAU,KAAK,KAAK,iBAAiB;AAE1D,iBAAO,WAAW;AAAA,YACjB,SAAS,GAAG,qCAAqC;AAAA,YACjD,WAAW;AAAA,UACZ,CAAC;AACD,iBAAO,MAAM,WAAW,OAAO;AAC/B,eAAK,kBAAkB;AACvB,eAAK,iBAAiB,EAAE;AAAA,QACzB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,aAAa,EAAE,cAAc,GAAG,IAAI,CAAC,GAAG;AACvC,YAAM,qBAAqB,KAAK,OAAO,QAAQ,EAAE,IAAI;AAErD,UAAI,aAAa;AAChB,sBAAc,YAAY,YAAY;AAGtC,aAAK,eAAe,KAAK,gBAAgB,CAAC;AAC1C,aAAK,aAAa,sBAAsB,KAAK,aAAa,uBAAuB,CAAC;AAClF,YAAI,KAAK,aAAa,oBAAoB,cAAc;AACvD,gBAAM,QAAQ,KAAK,aAAa,oBAAoB;AACpD,eAAK,QAAQ;AACb,eAAK,iBAAiB,KAAK;AAC3B,eAAK,iBAAiB,KAAK,MAAM,UAAU,KAAK,KAAK,0BAA0B;AAC/E;AAAA,QACD;AAAA,MACD;AAEA,WAAK,UAAU,EAAE,YAAY,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM;AAErD,cAAM,EAAE,OAAO,WAAW,UAAU,QAAQ,IAAI;AAChD,YAAI,eAAe,CAAC,SAAS;AAC5B,eAAK,aAAa,oBAAoB,eAAe;AAAA,QACtD;AACA,aAAK,QAAQ;AACb,aAAK,iBAAiB,KAAK;AAC3B,aAAK,iBAAiB,KAAK,MAAM,UAAU,KAAK,KAAK,0BAA0B;AAAA,MAChF,CAAC;AAAA,IACF;AAAA,IAEA,4BAA4B;AAC3B,WAAK,iBAAiB,KAAK,eAAe,EAAE,MAAM;AAClD,WAAK,iBAAiB,EAAE;AAAA,IACzB;AAAA,IAEA,gBAAgB,UAAU;AACzB,WAAK,WAAW,YAAY,aAAa,QAAQ;AAEjD,UAAI,UAAU;AACb,aAAK,WACH,KAAK,iBAAiB,EACtB,IAAI,yBAAyB,2BAA2B;AAC1D,aAAK,WAAW,KAAK,eAAe,EAAE,IAAI,UAAU,sBAAsB;AAC1E,aAAK,WAAW,IAAI,eAAe,iBAAiB;AACpD,aAAK,iBAAiB,IAAI,yBAAyB,2BAA2B;AAAA,MAC/E,OAAO;AAEN,aAAK,WACH,KAAK,iBAAiB,EACtB,IAAI,yBAAyB,EAAE;AACjC,aAAK,WAAW,KAAK,eAAe,EAAE,IAAI,UAAU,EAAE;AACtD,aAAK,WAAW,IAAI,eAAe,EAAE;AACrC,aAAK,iBAAiB,IAAI,yBAAyB,EAAE;AAAA,MACtD;AAAA,IACD;AAAA,IAEA,iBAAiB,MAAM;AACtB,WAAK,iBAAiB,EAAE;AACxB,WAAK,WAAW,IAAI,WAAW,OAAO,SAAS,MAAM;AAAA,IACtD;AAAA,IAEA,UAAU;AACT,UAAI,KAAK,gBAAgB;AACxB,aAAK,eAAe,WAAW;AAAA,MAChC;AACA,QAAE,MAAM,EAAE,IAAI,qBAAqB;AAAA,IACpC;AAAA,EACD;;;ACvtBA,UAAQ,eAAe,cAAc,MAAM;AAAA,IAC1C,YAAY,EAAE,SAAS,QAAQ,SAAS,GAAG;AAC1C,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,cAAc,SAAS;AAC5B,WAAK,oBAAoB,SAAS;AAClC,WAAK,wBAAwB,SAAS;AACtC,WAAK,eAAe,CAAC;AAErB,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,sBAAsB;AAC3B,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ,OAAO,oDAAoD;AAExE,WAAK,aAAa,KAAK,QAAQ,KAAK,yBAAyB;AAAA,IAC9D;AAAA,IAEA,wBAAwB;AACvB,WAAK,WAAW;AAAA,QACf;AAAA,yBACsB,GAAG,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBxC;AAEA,WAAK,aAAa,KAAK,WAAW,KAAK,YAAY;AACnD,WAAK,oBAAoB,KAAK,WAAW,KAAK,YAAY;AAC1D,WAAK,cAAc,KAAK,WAAW,KAAK,aAAa;AACrD,WAAK,cAAc,KAAK,WAAW,KAAK,aAAa;AACrD,WAAK,kBAAkB,KAAK,WAAW,KAAK,iBAAiB;AAC7D,WAAK,mBAAmB,KAAK,WAAW,KAAK,mBAAmB;AAChE,WAAK,0BAA0B,KAAK,WAAW,KAAK,yBAAyB;AAAA,IAC9E;AAAA,IAEA,0BAA0B,MAAM;AAE/B,aAAO,QAAQ,KAAK,QAAQ,KAAK,aAAa;AAAA,IAC/C;AAAA,IAEA,MAAM,4BAA4B,MAAM;AACvC,YAAM,uBAAuB,CAAC,KAAK,0BAA0B,IAAI;AAGjE,YAAM,oBAAoB,CAAC,QAAQ,IAAI,KAAK,CAAC;AAE7C,UAAK,CAAC,qBAAqB,wBAAyB,mBAAmB;AAGtE,cAAM,KAAK,2BAA2B;AAAA,MACvC;AAEA,WAAK,OAAO,qBAAqB,CAAC,iBAAiB;AACnD,WAAK,iBAAiB,CAAC,iBAAiB;AAExC,UAAI,QAAQ,sBAAsB;AACjC,aAAK,UAAU,KAAK;AACpB,aAAK,YAAY,OAAO,SAAS,KAAK,OAAO;AAC7C,aAAK,OAAO,KAAK;AACjB,aAAK,WAAW;AAChB,aAAK,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAE1C,aAAK,eAAe;AAEpB,aAAK,WAAW,IAAI;AACpB,aAAK,oBAAoB,IAAI;AAC7B,aAAK,YAAY,IAAI;AACrB,aAAK,OAAO,oBAAoB,IAAI;AAAA,MACrC,OAAO;AACN,aAAK,eAAe,CAAC;AAAA,MACtB;AAAA,IACD;AAAA,IAEA,6BAA6B;AAC5B,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,YAAM,WAAW,IAAI,MAAM,KAAK,CAAC,SAAS,KAAK,SAAS,KAAK,IAAI;AAEjE,UAAI,CAAC;AAAU;AAEf,YAAM,aAAa,SAAS;AAC5B,YAAM,UAAU,SAAS;AACzB,YAAM,qBACL,CAAC,SAAS,2BAA2B,CAAC,SAAS,aAAa,CAAC,SAAS;AAEvE,UAAK,cAAc,sBAAwB,WAAW,oBAAqB;AAC1E,eAAO,WAAW;AAAA,UACjB,SAAS,GAAG,sDAAsD;AAAA,UAClE,WAAW;AAAA,QACZ,CAAC;AACD,eAAO,MAAM,WAAW,QAAQ;AAChC,eAAO,KAAK,OAAO,sBAAsB;AAAA,MAC1C;AAAA,IACD;AAAA,IAEA,WAAW,MAAM;AAChB,UAAI,EAAE,WAAW,aAAa,OAAO,gBAAgB,IAAI;AAEzD,eAAS,uBAAuB;AAC/B,YAAI,aAAa;AAChB,wBACC,YAAY,QAAQ,KAAK,MAAM,MAAM,YAAY,SAAS,MACvD,YAAY,OAAO,GAAG,GAAG,IAAI,QAC7B;AACJ,iBAAO;AAAA,QACR;AACA,eAAO;AAAA,MACR;AAEA,WAAK,WAAW,KAAK,SAAS;AAC9B,WAAK,kBAAkB,KAAK,qBAAqB,CAAC;AAClD,WAAK,YAAY,KAAK,gBAAgB,iBAAiB,KAAK,QAAQ,CAAC;AACrE,UAAI,CAAC,KAAK,eAAe,OAAO;AAC/B,aAAK,YAAY;AAAA,UAChB;AAAA;AAAA,2BAEuB;AAAA,YACf,OAAO,SAAS,SAAS;AAAA;AAAA,QAElC;AAAA,MACD,OAAO;AACN,aAAK,YAAY,KAAK,0BAA0B,OAAO,SAAS,SAAS,SAAS;AAAA,MACnF;AAAA,IACD;AAAA,IAEA,oBAAoB,MAAM;AACzB,YAAM,YAAY,EAAE,IAAI,EAAE,KAAK,KAAK;AACpC,QAAE,IAAI,EAAE,YAAY,0BAA0B,iBAAiB;AAAA,IAChE;AAAA,IAEA,oBAAoB,MAAM;AACzB,UAAI,KAAK,qBAAqB;AAC7B,aAAK,iBAAiB;AAAA,UACrB,0BAA0B,gBAAgB,KAAK,iBAAiB,KAAK,QAAQ;AAAA,iCAChD,KAAK;AAAA,QACnC;AACA,aAAK,YAAY,KAAK,gBAAgB,KAAK,MAAM,KAAK,QAAQ,CAAC;AAAA,MAChE,OAAO;AACN,aAAK,iBAAiB,KAAK,EAAE;AAAA,MAC9B;AAAA,IACD;AAAA,IAEA,YAAY,MAAM;AACjB,YAAM,oBAAoB,KAAK,gBAAgB,IAAI;AACnD,WAAK,gBAAgB,KAAK,EAAE;AAE5B,wBAAkB,QAAQ,CAAC,WAAW,QAAQ;AAC7C,aAAK,gBAAgB;AAAA,UACpB,eAAe,sCAAsC;AAAA,QACtD;AAEA,cAAM,aAAa,KAAK,UAAU,OAAO,KAAK,CAAC,OAAO,GAAG,cAAc,SAAS;AAChF,sBAAc,wBAAyB,WAAW,QAAQ,GAAG,cAAc,IAAK;AAChF,cAAM,KAAK;AAEX,aAAK,GAAG,uBAAuB,OAAO,GAAG,KAAK,aAAa;AAAA,UAC1D,IAAI,iCACA,aADA;AAAA,YAEH,UAAU,WAAY;AACrB,iBAAG,OAAO,aAAa,GAAG,cAAc,WAAW,KAAK,KAAK;AAAA,YAC9D;AAAA,UACD;AAAA,UACA,QAAQ,KAAK,gBAAgB,KAAK,IAAI,mBAAmB;AAAA,UACzD,cAAc;AAAA,QACf,CAAC;AACD,aAAK,GAAG,qBAAqB,UAAU,KAAK,UAAU;AAAA,MACvD,CAAC;AAED,WAAK,sBAAsB,IAAI;AAC/B,WAAK,+BAA+B,IAAI;AAExC,WAAK,iCAAiC;AAAA,IACvC;AAAA,IAEA,gBAAgB,MAAM;AACrB,YAAM,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AACA,UAAI,KAAK,iBAAiB,KAAK;AAAW,eAAO,KAAK,WAAW;AACjE,UAAI,KAAK,gBAAgB,KAAK;AAAU,eAAO,KAAK,UAAU;AAC9D,aAAO;AAAA,IACR;AAAA,IAEA,sBAAsB,MAAM;AAC3B,UAAI,KAAK,iBAAiB,KAAK,WAAW;AACzC,aAAK,gBAAgB,KAAK,oBAAoB,EAAE,KAAK,UAAU,EAAE,IAAI,UAAU,MAAM;AAAA,MACtF;AAAA,IACD;AAAA,IAEA,+BAA+B,MAAM;AACpC,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,UAAI,CAAC,IAAI,cAAc,KAAK,iBAAiB,KAAK,YAAY;AAC7D,YAAI,CAAC,KAAK,cAAc;AACvB,eAAK,gBAAgB,OAAO,2CAA2C;AAAA,QACxE;AACA,cAAM,QAAQ,GAAG,2BAA2B;AAC5C,aAAK,gBAAgB;AAAA,UACpB,wDAAwD;AAAA,QACzD;AACA,aAAK,gBAAgB,KAAK,oBAAoB,EAAE,KAAK,UAAU,EAAE,IAAI,UAAU,MAAM;AAAA,MACtF;AAAA,IACD;AAAA,IAEA,mCAAmC;AAClC,YAAM,KAAK;AACX,UAAI,KAAK,cAAc;AACtB,aAAK,aAAa,GAAG,WAAW,WAAY;AAC3C,cAAI,KAAK,SAAS,IAAI,KAAK,KAAK,MAAM,GAAG;AACxC,eAAG,OAAO,aAAa,GAAG,cAAc,QAAQ,KAAK,KAAK,EAAE,KAAK,MAAM;AACtE,oBAAM,WAAW,OAAO,QAAQ,GAAG,SAAS,GAAG,IAAI;AACnD,oBAAM,MAAM,GAAG,OAAO,QAAQ,EAAE;AAChC,iBAAG,YAAY,KAAK,gBAAgB,SAAS,MAAM,IAAI,QAAQ,CAAC;AAChE,iBAAG,oBAAoB,QAAQ;AAAA,YAChC,CAAC;AAAA,UACF;AAAA,QACD;AACA,aAAK,aAAa,GAAG,YAAY,CAAC,KAAK;AACvC,aAAK,aAAa,QAAQ;AAAA,MAC3B;AAEA,UAAI,KAAK,+BAA+B,CAAC,KAAK,uBAAuB;AACpE,aAAK,4BAA4B,GAAG,YAAY;AAChD,aAAK,4BAA4B,QAAQ;AAAA,MAC1C;AAEA,UAAI,KAAK,mBAAmB;AAC3B,aAAK,kBAAkB,GAAG,OAAO;AACjC,aAAK,kBAAkB,GAAG,WAAW,WAAY;AAChD,cAAI,KAAK,OAAO;AACf,eAAG,OAAO,aAAa,GAAG,cAAc,aAAa,KAAK,KAAK,EAAE,KAAK,MAAM;AAC3E,iBAAG,iBAAiB,GAAG,OAAO,mBAAmB;AACjD,oBAAM,gBAAgB,GAAG,eAAe,GAAG,SAAS,WAAW,KAAK,OAAO;AAC3E,oBAAM,gBAAgB;AAAA,gBACrB,GAAG,eAAe,GAAG,SAAS,WAAW,KAAK,OAAO;AAAA,cACtD;AACA,kBAAI,kBAAkB,QAAW;AAChC,mBAAG,OAAO,oBAAoB,GAAG,SAAS,WAAW,KAAK,KAAK,EAAE,KAAK,MAAM;AAE3E,qBAAG,kBAAkB,UAAU,KAAK,KAAK;AAAA,gBAC1C,CAAC;AAAA,cACF,WAAW,kBAAkB,KAAK,eAAe;AAChD,mBAAG,kBAAkB,UAAU,EAAE;AACjC,sBAAM,iBAAiB,GAAG,SAAS,UAAU,KAAK;AAClD,sBAAM,iBAAiB,KAAK,MAAM,KAAK;AACvC,uBAAO;AAAA,kBACN,GAAG,wDAAwD;AAAA,oBAC1D;AAAA,oBACA;AAAA,kBACD,CAAC;AAAA,gBACF;AAAA,cACD;AACA,iBAAG,mBAAmB,UAAU,aAAa;AAAA,YAC9C,CAAC;AAAA,UACF;AAAA,QACD;AACA,aAAK,kBAAkB,GAAG,YAAY,MAAM;AAC3C,iBAAO;AAAA,YACN,SAAS,EAAE,SAAS,KAAK,OAAO,QAAQ,EAAE,IAAI,SAAS,UAAU,EAAE;AAAA,UACpE;AAAA,QACD;AACA,aAAK,kBAAkB,QAAQ;AAAA,MAChC;AAEA,UAAI,KAAK,mBAAmB;AAC3B,aAAK,kBAAkB,GAAG,OAAO;AACjC,aAAK,kBAAkB,GAAG,WAAW,iBAAkB;AACtD,WAAC,GAAG,aAAa,YAAa,MAAM,GAAG,qBAAqB;AAC5D,aAAG,OAAO,aAAa,GAAG,cAAc,aAAa,KAAK,KAAK;AAAA,QAChE;AACA,aAAK,kBAAkB,QAAQ;AAAA,MAChC;AAEA,UAAI,KAAK,kBAAkB;AAC1B,aAAK,iBAAiB,GAAG,OAAO;AAChC,aAAK,iBAAiB,GAAG,YAAY,MAAM;AAC1C,iBAAO;AAAA,YACN,OAAO;AAAA,YACP,SAAS;AAAA,cACR,WAAW,GAAG,SAAS;AAAA,cACvB,WAAW,GAAG,SAAS;AAAA,cACvB,cAAc,GAAG,OAAO,QAAQ,EAAE,IAAI;AAAA,YACvC;AAAA,UACD;AAAA,QACD;AACA,aAAK,iBAAiB,QAAQ;AAAA,MAC/B;AAEA,UAAI,KAAK,aAAa;AACrB,aAAK,YAAY,GAAG,WAAW,WAAY;AAC1C,aAAG,OAAO,aAAa,GAAG,cAAc,OAAO,KAAK,KAAK;AAEzD,gBAAM,WAAW,OAAO,QAAQ,GAAG,SAAS,GAAG,IAAI;AACnD,aAAG,0BAA0B,GAAG,YAAY,SAAS,aAAa,KAAK;AACvE,aAAG,0BAA0B,QAAQ;AAAA,QACtC;AAAA,MACD;AAEA,aAAO,MAAM,GAAG,oBAAoB,KAAK,CAAC,WAAW,OAAO,aAAa;AACxE,cAAM,gBAAgB,KAAK,GAAG;AAC9B,cAAM,2BAA2B,KAAK,0BAA0B,QAAQ;AACxE,YACC,4BACA,iBACA,cAAc,UAAU,MAAM,SAC9B,SAAS,SAAS,YACjB;AACD,wBAAc,UAAU,KAAK;AAC7B,kBAAQ,iBAAiB,QAAQ;AAAA,QAClC;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,MAAM,uBAAuB;AAC5B,UAAI,KAAK,qBAAqB,KAAK,kBAAkB;AACpD,cAAM,sBAAsB,KAAK,kBAC/B,UAAU,EACV,MAAM;AAAA,CAAI,EACV,OAAO,CAAC,MAAM,CAAC;AACjB,YAAI,CAAC,oBAAoB;AAAQ;AAGjC,cAAM,wBAAwB,MAAM,OAAO,GAAG,SAAS,aAAa;AAAA,UACnE,SAAS,EAAE,MAAM,CAAC,MAAM,mBAAmB,EAAE;AAAA,UAC7C,QAAQ,CAAC,YAAY,MAAM;AAAA,QAC5B,CAAC;AACD,cAAM,mBAAmB,sBAAsB,OAAO,CAAC,KAAK,MAAM;AACjE,cAAI,CAAC,IAAI,EAAE,WAAW;AACrB,gBAAI,EAAE,YAAY,CAAC;AAAA,UACpB;AACA,cAAI,EAAE,YAAY,CAAC,GAAG,IAAI,EAAE,WAAW,EAAE,IAAI;AAC7C,iBAAO;AAAA,QACR,GAAG,CAAC,CAAC;AAEL,cAAM,WAAW,OAAO,KAAK,gBAAgB,EAAE;AAC/C,cAAM,mBAAmB,iBAAiB,UAAU,KAAK;AAAA,CAAI;AAE7D,cAAM,oCACL,oBAAoB,WAAW,iBAAiB,UAAU;AAE3D,cAAM,mBAAmB,KAAK,iBAAiB,UAAU;AACzD,4BAAoB,YAAa,MAAM,KAAK,iBAAiB,UAAU,QAAQ;AAE/E,YAAI,mCAAmC;AACtC,eAAK,kBAAkB,UAAU,gBAAgB;AACjD,eAAK,YAAY,UAAU,iBAAiB,UAAU,MAAM;AAE5D,iBAAO,iBAAiB;AACxB,eAAK,OAAO,4BAA4B,kBAAkB,KAAK,YAAY;AAAA,QAC5E;AAAA,MACD;AAAA,IACD;AAAA,IAEA,cAAc;AACb,WAAK,6BAA6B;AAClC,WAAK,6BAA6B;AAElC,WAAK,WAAW,GAAG,SAAS,cAAc,MAAM;AAC/C,aAAK,OAAO,mBAAmB;AAAA,MAChC,CAAC;AAAA,IACF;AAAA,IAEA,mBAAmB;AAClB,WAAK,QAAQ,KAAK,YAAY,EAAE,KAAK,SAAS,KAAK;AACnD,aAAO,GAAG,KAAK,GAAG,UAAU,MAAM;AACjC,cAAM,uBAAuB,KAAK,WAAW,GAAG,UAAU;AAC1D,YAAI,sBAAsB;AACzB,eAAK,OAAO,mBAAmB;AAAA,QAChC;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,+BAA+B;AAC9B,YAAM,KAAK;AACX,WAAK,gBAAgB,GAAG,SAAS,wBAAwB,WAAY;AACpE,cAAM,YAAY,EAAE,IAAI,EAAE,KAAK,gBAAgB;AAC/C,YAAI,KAAK,sBAAsB,WAAW;AACzC,aAAG,OAAO,mBAAmB,SAAS;AACtC,eAAK,qBAAqB;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,+BAA+B;AAC9B,WAAK,gBAAgB,GAAG,SAAS,mBAAmB,MAAM;AACzD,aAAK,oBAAoB,KAAK,iBAAiB,UAAU,EAAE;AAC3D,YAAI,MAAM,KAAK,YAAY,UAAU;AACrC,YAAI,oBAAoB,KAAK,0BAA0B,UAAU;AACjE,YAAI,cAAc,KAAK,SAAS,eAAe,KAAK,OAAO,QAAQ,EAAE,IAAI,eAAe;AAExF,YAAI,UAAU,OAAO,KAAK;AAAA,UACzB,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,KAAK,MAAM;AAAA,YACX,WAAW,KAAK,aAAa;AAAA,YAC7B,WAAW,KAAK,kBAAkB,UAAU,KAAK;AAAA,YACjD,WAAW,KAAK,aAAa,YAAY;AAAA,YACzC,cAAc;AAAA,YACd,aAAa;AAAA,UACd;AAAA,QACD,CAAC;AAED,gBAAQ,KAAK,CAAC,SAAS;AACtB,cAAI,8BAA8B,KAAK;AACvC,cAAI,iBAAiB,4BAA4B;AACjD,cAAI,CAAC,gBAAgB;AACpB,kBAAM,YAAY,KAAK,kBAAkB,UAAU,EAAE,KAAK;AAC1D,kBAAM,YAAY,KAAK,aAAa,UAAU,KAAK;AACnD,mBAAO;AAAA,cACN;AAAA,gBACC;AAAA,gBACA,CAAC,WAAW,SAAS;AAAA,cACtB;AAAA,YACD;AAAA,UACD,WAAW,iBAAiB,KAAK;AAChC,mBAAO,SAAS,GAAG,8CAA8C,CAAC,cAAc,CAAC,CAAC;AAClF,iBAAK,YAAY,UAAU,cAAc;AAAA,UAC1C;AACA,oBAAU,4BAA4B,KAAK;AAAA,CAAI;AAC/C,eAAK,kBAAkB,UAAU,OAAO;AAAA,QACzC,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAEA,iBAAiB,MAAM;AACtB,aAAO,KAAK,WAAW,IAAI,WAAW,MAAM,IAAI,KAAK,WAAW,IAAI,WAAW,MAAM;AAAA,IACtF;AAAA,EACD;;;ACtcA,UAAQ,eAAe,YAAY,MAAM;AAAA,IACxC,YAAY,EAAE,SAAS,QAAQ,MAAM,MAAM,aAAa,eAAe,GAAG;AACzE,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,cAAc,eAAe,CAAC;AACnC,WAAK,aAAa,kBAAkB,CAAC;AAErC,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,YAAY;AAAA,IAClB;AAAA,IAEA,cAAc;AACb,YAAM,EAAE,MAAM,MAAM,aAAa,WAAW,IAAI;AAEhD,eAAS,WAAW;AACnB,eAAO,KAAK,OAAO,CAAC,GAAG,KAAK,MAAM;AACjC,iBACC,IACA,IAAI,OAAO,CAAC,IAAI,QAAQ,MAAM;AAC7B,kBAAM,kBAAkB,eAAe,YAAY,KAAK,YAAY,GAAG,KAAK;AAC5E,kBAAM,YACL,cAAc,WAAW,UACtB,WAAW,UACX,OAAO,WAAW,WAClB,OAAO,MAAM,MAAM,IACnB;AAEJ,mBACC,KACA,0BAA0B,uCAAuC,cAAc;AAAA,cAC9E;AAAA,YACD;AAAA,UAEF,GAAG,EAAE;AAAA,QAEP,GAAG,EAAE;AAAA,MACN;AAEA,WAAK,QAAQ;AAAA,QACZ;AAAA,MACG,SAAS;AAAA;AAAA,MAEb;AAAA,IACD;AAAA,IAEA,cAAc;AACb,YAAM,KAAK;AACX,WAAK,QAAQ,GAAG,SAAS,eAAe,WAAY;AACnD,cAAM,OAAO,EAAE,IAAI;AACnB,WAAG,OAAO,aAAa,IAAI;AAAA,MAC5B,CAAC;AAAA,IACF;AAAA,EACD;;;AC1DA,UAAQ,eAAe,UAAU,MAAM;AAAA,IACtC,YAAY,EAAE,QAAQ,QAAQ,GAAG;AAChC,WAAK,UAAU;AACf,WAAK,SAAS;AAEd,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,kBAAkB;AACvB,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ;AAAA,QACZ;AAAA,iDAC8C,GAAG,gBAAgB;AAAA;AAAA;AAAA;AAAA,mCAIjC,GAAG,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAQ1B,GAAG,gBAAgB;AAAA;AAAA,MAErD;AACA,WAAK,aAAa,KAAK,QAAQ,KAAK,oBAAoB;AACxD,WAAK,iBAAiB,KAAK,WAAW,KAAK,gBAAgB;AAC3D,WAAK,kBAAkB,KAAK,WAAW,KAAK,iBAAiB;AAC7D,WAAK,UAAU,KAAK,WAAW,KAAK,SAAS;AAC7C,WAAK,UAAU,KAAK,WAAW,KAAK,aAAa;AACjD,WAAK,0BAA0B,KAAK,WAAW,KAAK,iBAAiB;AAGrE,WAAK,sBAAsB;AAAA,IAC5B;AAAA,IAEA,wBAAwB;AAEvB,YAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2Qd,UAAI,CAAC,EAAE,gCAAgC,EAAE,QAAQ;AAChD,UAAE,MAAM,EAAE,OAAO,MAAM,QAAQ,WAAW,4CAA4C,CAAC;AAAA,MACxF;AAAA,IACD;AAAA,IAEA,8BAA8B;AAC7B,WAAK,sBAAsB,CAAC;AAC5B,aAAO,GAAG,QAAQ,gBAAgB,MAAS,EAAE,KAAK,CAAC,QAAQ;AAC1D,cAAM,SAAS,IAAI;AACnB,YAAI,CAAC,OAAO;AAAQ;AAEpB,aAAK,kBAAkB,KAAK,wBAAwB,KAAK,iBAAiB;AAC1E,aAAK,gBAAgB,KAAK,EAAE;AAC5B,cAAM,MAAM,KAAK,OAAO,QAAQ;AAEhC,eAAO,QAAQ,CAAC,OAAO;AACtB,eAAK,gBAAgB;AAAA,YACpB,oCAAoC,GAAG,oCAAoC,GAAG;AAAA,UAC/E;AACA,cAAI,YAAY;AAAA,YACf,UAAU,WAAY;AACrB,kBAAI,UAAU,KAAK,GAAG,WAAW,KAAK,UAAU,CAAC;AAAA,YAClD;AAAA,UACD;AACA,cAAI,GAAG,aAAa,UAAU;AAC7B,wBAAY;AAAA,cACX,OAAO,WAAY;AAClB,oBAAI,IAAI,eAAe,aAAa,GAAG,WAAW,IAAI,IAAI,OAAO,GAAG;AACnE,sBAAI,eAAe,QAAQ,GAAG,WAAW,IAAI,IAAI,SAAS,IAAI,IAAI,OAAO;AAAA,gBAC1E;AAAA,cACD;AAAA,YACD;AAAA,UACD;AACA,cAAI,GAAG,SAAS,GAAG,cAAc,YAAY,CAAC,GAAG,YAAY;AAC5D,iBAAK,oBAAoB,KAAK,EAAE,WAAW,GAAG,WAAW,OAAO,GAAG,MAAM,CAAC;AAAA,UAC3E;AAEA,eAAK,GAAG,GAAG,qBAAqB,OAAO,GAAG,KAAK,aAAa;AAAA,YAC3D,IAAI,kCACA,KACA;AAAA,YAEJ,QAAQ,KAAK,gBAAgB,KAAK,IAAI,GAAG,iBAAiB;AAAA,YAC1D,cAAc;AAAA,UACf,CAAC;AACD,eAAK,GAAG,GAAG,mBAAmB,UAAU,IAAI,IAAI,GAAG,UAAU;AAAA,QAC9D,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAEA,oBAAoB;AACnB,YAAM,KAAK;AACX,WAAK,aAAa,IAAI,QAAQ,eAAe,UAAU;AAAA,QACtD,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,UACP,cAAc,SAAU,MAAM;AAC7B,eAAG,kBAAkB,IAAI;AAAA,UAC1B;AAAA,QACD;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,UACL,CAAC,GAAG,GAAG,CAAC;AAAA,UACR,CAAC,GAAG,GAAG,CAAC;AAAA,UACR,CAAC,GAAG,GAAG,CAAC;AAAA,UACR,CAAC,KAAK,GAAG,QAAQ;AAAA,QAClB;AAAA,MACD,CAAC;AAED,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,kBAAkB,MAAM;AACvB,YAAM,eAAe,KAAK,KAAK,mBAAmB;AAElD,2BAAqB,IAAI;AACzB,WAAK,eACJ,iBAAiB,WAAW,KAAK,aAAa,MAAM,GAAG,EAAE,IAAI,KAAK,eAAe;AAClF,WAAK,cAAc,OAAO,IAAI,CAAC,EAAE,MAAM;AACvC,WAAK,cAAc,UAAU,KAAK,YAAY;AAE9C,eAAS,qBAAqBC,OAAM;AACnC,QAAAA,MAAK,SAAS,+BAA+B;AAC7C,mBAAW,MAAM;AAChB,UAAAA,MAAK,YAAY,+BAA+B;AAAA,QACjD,GAAG,GAAG;AAAA,MACP;AAAA,IACD;AAAA,IAEA,cAAc;AACb,YAAM,KAAK;AAEX,WAAK,eAAe,GAAG,SAAS,oBAAoB,SAAU,GAAG;AAChE,YAAI,EAAE,EAAE,MAAM,EAAE,QAAQ,sEAAsE,EAAE,QAAQ;AACvG;AAAA,QACD;AAEA,cAAM,eAAe,EAAE,IAAI;AAC3B,cAAM,aACL,aAAa,OAAO,EAAE,OAAO,GAAG,eAAe,OAAO,EAAE,OAAO,GAAG,eAAe,WAAW;AAC7F,WAAG,eAAe,QAAQ,EAAE,WAAW,CAAC;AAExC,cAAM,OAAO,aAAa,KAAK,WAAW;AAE1C,UAAE,0BAA0B,EAAE,IAAI,WAAW,MAAM;AACnD,UAAE,2BAA2B,EAAE,IAAI,WAAW,MAAM;AACpD,UAAE,iBAAiB,EAAE,IAAI,WAAW,MAAM;AAC1C,WAAG,eAAe,KAAK,aAAa,EAAE,IAAI,WAAW,QAAQ;AAC7D,WAAG,eAAe,KAAK,sBAAsB,EAAE,IAAI,WAAW,MAAM;AAEpE,UAAE,kBAAkB,EAAE,YAAY,gBAAgB;AAElD,YAAI,aAAa,SAAS,gBAAgB,GAAG;AAC5C,uBAAa,YAAY,gBAAgB;AACzC,aAAG,gBAAgB;AAAA,QACpB,OAAO;AACN,uBAAa,SAAS,gBAAgB;AACtC,uBAAa,KAAK,0BAA0B,EAAE,IAAI,WAAW,MAAM;AACnE,uBAAa,KAAK,2BAA2B,EAAE,IAAI,WAAW,MAAM;AACpE,uBAAa,KAAK,iBAAiB,EAAE,IAAI,WAAW,MAAM;AAC1D,aAAG,eAAe,KAAK,IAAI,aAAa,EAAE,IAAI,WAAW,MAAM;AAC/D,aAAG,eAAe,KAAK,IAAI,WAAW,EAAE,IAAI,WAAW,QAAQ;AAE/D,aAAG,gBAAgB,GAAG,GAAG;AACzB,aAAG,iBAAiB,GAAG,cAAc,OAAO,IAAI,CAAC,EAAE,MAAM;AACzD,aAAG,0BAA0B;AAAA,QAC9B;AAAA,MACD,CAAC;AAED,aAAO,GAAG,KAAK,GAAG,iBAAiB,kBAAkB,CAAC,QAAQ;AAzbhE;AA0bG,cAAM,UAAU,IAAI,IAAI;AACxB,cAAM,iBAAiB,GAAE,UAAK,8BAAL,mBAAgC,OAAO,EAAE;AAClE,YAAI,SAAS;AACZ,yBAAe,YAAY,aAAa,EAAE,SAAS,aAAa;AAAA,QACjE,OAAO;AACN,yBAAe,YAAY,aAAa,EAAE,SAAS,aAAa;AAAA,QACjE;AAAA,MACD,CAAC;AAED,aAAO,GAAG,KAAK,GAAG,iBAAiB,eAAe,CAAC,QAAQ;AAC1D,YAAI,IAAI,IAAI,eAAe,CAAC,IAAI,0BAA0B;AACzD,cAAI,CAAC,IAAI,IAAI,qBAAqB;AACjC,gBAAI,2BAA2B;AAC/B,mBAAO,aAAa;AAAA,cACnB,MAAO,IAAI,IAAI,sBAAsB;AAAA,cACrC,MAAM,IAAI,QAAQ,qBAAqB;AAAA,cACvC,MAAO,IAAI,IAAI,sBAAsB;AAAA,cACrC,MAAM,IAAI,QAAQ,oBAAoB;AAAA,cACtC,MAAM,IAAI,KAAK;AAAA,cACf,MAAM,KAAK,sBAAsB,IAAI,GAAG;AAAA,cACxC,MAAO,IAAI,2BAA2B;AAAA,YACvC,CAAC;AAAA,UACF,WAAW,IAAI,IAAI,qBAAqB;AACvC,mBAAO,WAAW;AAAA,cACjB,SAAS,GAAG,2DAA2D;AAAA,cACvE,WAAW;AAAA,YACZ,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAED,WAAK,4BAA4B;AAEjC,WAAK,eAAe,GAAG,SAAS,aAAa,WAAY;AACxD,cAAM,QAAQ,EAAE,IAAI,EAAE,KAAK,YAAY;AACvC,WAAG,cAAc,UAAU,KAAK;AAAA,MACjC,CAAC;AAED,WAAK,WAAW,GAAG,SAAS,qBAAqB,MAAM;AACtD,cAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,cAAM,cAAc,IAAI;AACxB,cAAM,QAAQ,IAAI;AAElB,YAAI,CAAC,KAAK,6BAA6B,GAAG;AACzC;AAAA,QACD;AAGA,YAAI,CAAC,MAAM,QAAQ;AAClB,gBAAM,UAAU,GAAG,gCAAgC;AACnD,iBAAO,WAAW,EAAE,SAAS,WAAW,SAAS,CAAC;AAClD,iBAAO,MAAM,WAAW,OAAO;AAC/B;AAAA,QACD;AAGA,YAAI,eAAe,KAAK,IAAI,kCAAkC,KAAK;AAClE,iBAAO;AAAA,YACN,GAAG,4EAA4E;AAAA,YAC/E,MAAM;AACL,mBAAK,OAAO,eAAe;AAAA,YAC5B;AAAA,UACD;AAAA,QACD,OAAO;AACN,eAAK,OAAO,eAAe;AAAA,QAC5B;AAAA,MACD,CAAC;AAED,aAAO,GAAG,KAAK,GAAG,iBAAiB,eAAe,CAAC,QAAQ;AAC1D,aAAK,sBAAsB,IAAI,GAAG;AAGlC,cAAM,8BAA8B,CAAC,KAAK,eAAe,KAAK,iBAAiB,EAAE,GAAG,UAAU;AAC9F,SAAC,+BACA,KAAK,eAAe,KAAK,iBAAiB,EAAE,IAAI,WAAW,MAAM;AAClE,aAAK,wBAAwB;AAAA,MAC9B,CAAC;AAED,aAAO,GAAG,KAAK,GAAG,iBAAiB,kBAAkB,CAAC,QAAQ;AAC7D,cAAM,qBAAqB,gBAAgB,IAAI,IAAI,gBAAgB,IAAI,IAAI,QAAQ;AACnF,aAAK,eAAe,KAAK,wBAAwB,EAAE,KAAK,kBAAkB;AAAA,MAC3E,CAAC;AAED,aAAO,GAAG,KAAK,GAAG,yBAAyB,UAAU,CAAC,KAAK,KAAK,QAAQ;AAEvE,cAAM,cAAc,OAAO,KAAK;AAChC,cAAM,OAAO,KAAK,yBAAyB,YAAY,eAAe;AACtE,YAAI,KAAK,GAAG,mBAAmB,KAAK,GAAG,gBAAgB,UAAU,KAAK,YAAY,QAAQ;AACzF,eAAK,GAAG,gBAAgB,UAAU,YAAY,MAAM;AAAA,QACrD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,8BAA8B;AAC7B,aAAO,SAAS,GAAG,yBAAyB,CAAC,SAAS;AACrD,cAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,cAAM,EAAE,UAAU,QAAQ,SAAS,gBAAgB,IAAI;AACvD,YAAI,SAAS;AAEb,YAAI,SAAS;AACZ,kBAAQ,GAAG,kBAAkB;AAC7B,gBAAM,cAAc,KAAK,OAAO,aAAa,qBAAqB,IAC/D,IAAI,cACJ,IAAI;AACP,cAAI,UAAU,aAAa;AAC1B,mBAAO,IAAI,SAAS;AACpB,sBAAU,GAAG,yCAAyC;AAAA,cACrD,gBAAgB,QAAQ,IAAI,UAAU,CAAC;AAAA,YACxC,CAAC;AACD,iBAAK,OAAO,eAAe;AAC3B,oBAAQ,WAAW;AAAA,UACpB,OAAO;AACN,sBAAU;AAAA,cACT;AAAA,cACA,CAAC,gBAAgB,QAAQ,IAAI,UAAU,CAAC,CAAC;AAAA,YAC1C;AAAA,UACD;AAAA,QACD,WAAW,iBAAiB;AAC3B,oBAAU;AACV,kBAAQ,GAAG,gBAAgB;AAAA,QAC5B;AAEA,eAAO,SAAS,EAAE,SAAkB,MAAa,CAAC;AAAA,MACnD,CAAC;AAAA,IACF;AAAA,IAEA,4BAA4B;AAC3B,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,YAAM,cAAc,KAAK,OAAO,aAAa,qBAAqB,IAC/D,IAAI,cACJ,IAAI;AACP,YAAM,mBAAmB,cAAc,IAAI;AAC3C,YAAM,gBAAgB,KAAK,gBAAgB,KAAK,cAAc,UAAU,IAAI;AAC5E,UAAI,CAAC,iBAAiB,mBAAmB,KAAK,KAAK,eAAe;AACjE,aAAK,cAAc,UAAU,gBAAgB;AAAA,MAC9C;AAAA,IACD;AAAA,IAEA,mBAAmB;AAClB,YAAM,aAAa,OAAO,MAAM,OAAO,IAAI,WAAM;AACjD,WAAK,WAAW,KAAK,mBAAmB,EAAE,KAAK,SAAS,GAAG,kBAAkB;AAC7E,aAAO,GAAG,KAAK,GAAG,cAAc,MAAM;AACrC,cAAM,qBAAqB,KAAK,WAAW,GAAG,UAAU;AACxD,cAAM,cAAc,KAAK,eAAe,KAAK,iBAAiB;AAC9D,YAAI,sBAAsB,YAAY,QAAQ;AAC7C,eAAK,WAAW,KAAK,mBAAmB,EAAE,MAAM;AAAA,QACjD;AAAA,MACD,CAAC;AAED,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM;AACb,gBAAM,qBAAqB,KAAK,WAAW,GAAG,UAAU;AACxD,cAAI,cAAc,KAAK,eAAe,KAAK,iBAAiB;AAC5D,wBAAc,YAAY,SAAS,YAAY,KAAK,WAAW,IAAI;AAEnE,cAAI,CAAC;AAAa;AAElB,gBAAM,mBAAmB,MAAM,KAAK,KAAK,eAAe,KAAK,kBAAkB,CAAC,EAAE;AAAA,YAAI,CAAC,MACtF,EAAE,CAAC,EAAE,KAAK,WAAW;AAAA,UACtB;AACA,gBAAM,aAAa,iBAAiB,QAAQ,WAAW;AACvD,gBAAM,mBAAmB,aAAa,KAAK,iBAAiB;AAC5D,gBAAM,0BAA0B,KAAK,eAAe;AAAA,YACnD,+BAA+B,iBAAiB;AAAA,UACjD;AAEA,cAAI,sBAAsB,cAAc,iBAAiB;AACxD,oCAAwB,MAAM;AAAA,UAC/B;AAAA,QACD;AAAA,QACA,WAAW,MACV,KAAK,WAAW,GAAG,UAAU,KAAK,KAAK,eAAe,KAAK,iBAAiB,EAAE;AAAA,QAC/E,aAAa,GAAG,8BAA8B;AAAA,QAC9C,eAAe;AAAA,QACf,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AAAA,IACF;AAAA,IAEA,gBAAgB;AAAA,IAEhB;AAAA,IAEA,yBAAyB;AACxB,WAAK,wBAAwB;AAC7B,WAAK,4BAA4B;AACjC,WAAK,sBAAsB;AAC3B,WAAK,iCAAiC;AAAA,IACvC;AAAA,IAEA,eAAe;AACd,YAAM,MAAM,KAAK,OAAO,QAAQ;AAChC,UAAI,eAAe,QAAQ,wBAAwB,IAAI,IAAI,SAAS,IAAI,IAAI,OAAO;AAAA,IACpF;AAAA,IAEA,YAAY;AACX,WAAK,OAAO,sBAAsB,KAAK;AACvC,WAAK,iBAAiB,KAAK;AAAA,IAC5B;AAAA,IAEA,WAAW;AACV,YAAM,MAAM,KAAK,OAAO,QAAQ;AAChC,UAAI,QAAQ,6BAA6B;AACzC,UAAI,cAAc,oBAAoB;AACtC,UAAI,cAAc,aAAa;AAC/B,UAAI,cAAc,kBAAkB;AACpC,WAAK,OAAO,sBAAsB,IAAI;AACtC,WAAK,iBAAiB,IAAI;AAE1B,WAAK,uBAAuB;AAC5B,WAAK,aAAa;AAAA,IACnB;AAAA,IAEA,yBAAyB;AACxB,UAAI,KAAK,SAAS,KAAK,iBAAiB,EAAE,QAAQ;AACjD,aAAK,SAAS,KAAK,cAAc;AAAA,MAClC,OAAO;AACN,aAAK,SAAS,KAAK,EAAE;AACrB,aAAK,oBAAoB,OAAO,GAAG,KAAK,aAAa;AAAA,UACpD,IAAI;AAAA,YACH,OAAO,GAAG,QAAQ;AAAA,YAClB,WAAW;AAAA,YACX,UAAU,WAAY;AAAA,YAAC;AAAA,UACxB;AAAA,UACA,QAAQ,KAAK,gBAAgB,KAAK,UAAU;AAAA,UAC5C,cAAc;AAAA,QACf,CAAC;AACD,aAAK,kBAAkB,UAAU,EAAE;AAAA,MACpC;AAAA,IACD;AAAA,IAEA,0BAA0B;AACzB,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,YAAM,WAAW,IAAI;AACrB,YAAM,WAAW,IAAI;AAErB,WAAK,eAAe;AAAA,QACnB,GAAG,SACD,IAAI,CAAC,GAAG,MAAM;AACd,gBAAM,OAAO,KAAK,yBAAyB,EAAE,eAAe;AAC5D,gBAAM,eAAe,EAAE;AACvB,gBAAM,SAAS,EAAE,SAAS,IAAI,gBAAgB,EAAE,QAAQ,QAAQ,IAAI;AAEpE,iBAAO;AAAA;AAAA,gDAEoC,4BAA4B;AAAA;AAAA,4CAEhC,EAAE;AAAA,sBACxB,2BAA2B;AAAA,sBAC3B;AAAA;AAAA;AAAA,kDAG4B;AAAA,6CACL;AAAA;AAAA;AAAA;AAAA;AAAA,QAKzC,CAAC,EACA,KAAK,EAAE;AAAA,MACV;AAEA,eAAS,QAAQ,CAAC,MAAM;AACvB,cAAM,OAAO,KAAK,yBAAyB,EAAE,eAAe;AAC5D,cAAM,KAAK;AAGX,aAAK,GAAG,kBAAkB,OAAO,GAAG,KAAK,aAAa;AAAA,UACrD,IAAI;AAAA,YACH,OAAO,EAAE;AAAA,YACT,WAAW;AAAA,YACX,aAAa,GAAG,qBAAqB,CAAC,EAAE,eAAe,CAAC;AAAA,YACxD,UAAU,WAAY;AACrB,oBAAM,gBAAgB,OAAO,MAAM,UAAU,EAAE,SAAS,EAAE,MAAM,QAAQ;AACxE,kBAAI,iBAAiB,KAAK,OAAO;AAChC,uBAAO,MACL,UAAU,EAAE,SAAS,EAAE,MAAM,UAAU,IAAI,KAAK,KAAK,CAAC,EACtD,KAAK,MAAM,GAAG,sBAAsB,CAAC;AAEvC,sBAAM,qBAAqB,gBAAgB,KAAK,OAAO,QAAQ;AAC/D,mBAAG,eAAe,KAAK,IAAI,aAAa,EAAE,KAAK,kBAAkB;AAAA,cAClE;AAAA,YACD;AAAA,UACD;AAAA,UACA,QAAQ,KAAK,eAAe,KAAK,IAAI,8BAA8B;AAAA,UACnE,cAAc;AAAA,QACf,CAAC;AACD,aAAK,GAAG,gBAAgB,aAAa,KAAK;AAC1C,aAAK,GAAG,gBAAgB,UAAU,EAAE,MAAM;AAE1C,YAAI,EAAE,SAAS,QAAO;AAEtB,eAAK,GAAG,4BAA4B,OAAO,GAAG,KAAK,aAAa;AAAA,YAC/D,IAAI;AAAA,cACH,OAAO,GAAG,cAAc;AAAA,cACxB,WAAW;AAAA,cACX,aAAa,GAAG,eAAe;AAAA,cAC/B,UAAU,WAAY;AACrB,uBAAO,MAAM,UAAU,EAAE,SAAS,EAAE,MAAM,gBAAgB,KAAK,KAAK;AAAA,cACrE;AAAA,YACD;AAAA,YACA,QAAQ,KAAK,eAAe,KAAK,+BAA+B,QAAQ;AAAA,YACxE,cAAc;AAAA,UACf,CAAC;AACD,eAAK,GAAG,0BAA0B,aAAa,KAAK;AACpD,eAAK,GAAG,0BAA0B,UAAU,EAAE,gBAAgB,EAAE;AAAA,QAChE;AAGA,YAAI,EAAE,SAAS,QAAQ;AACtB,eAAK,GAAG,uBAAuB,OAAO,GAAG,KAAK,aAAa;AAAA,YAC1D,IAAI;AAAA,cACH,OAAO,GAAG,gBAAgB;AAAA,cAC1B,WAAW;AAAA,cACX,SAAS,OAAO,SAAS,UAAU;AAAA,cACnC,UAAU,WAAY;AACrB,uBAAO,MAAM,UAAU,EAAE,SAAS,EAAE,MAAM,kBAAkB,KAAK,KAAK;AACtE,uBAAO,MAAM,UAAU,EAAE,SAAS,EAAE,MAAM,kBAAkB,KAAK,KAAK;AAAA,cACvE;AAAA,YACD;AAAA,YACA,QAAQ,KAAK,eAAe,KAAK,0BAA0B,QAAQ;AAAA,YACnE,cAAc;AAAA,UACf,CAAC;AACD,eAAK,GAAG,qBAAqB,aAAa,KAAK;AAC/C,eAAK,GAAG,qBAAqB,UAAU,EAAE,kBAAkB,OAAO,SAAS,UAAU,CAAC;AAAA,QACvF,OAAO;AAEN,eAAK,eAAe,KAAK,0BAA0B,QAAQ,EAAE,KAAK;AAAA,QACnE;AAAA,MACD,CAAC;AAED,WAAK,mCAAmC;AAAA,IACzC;AAAA,IAEA,uBAAuB;AACtB,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,YAAM,WAAW,IAAI;AACrB,eAAS,QAAQ,CAAC,MAAM;AACvB,cAAM,OAAO,KAAK,yBAAyB,EAAE,eAAe;AAC5D,YAAI,EAAE,SAAS;AACd,qBAAW,MAAM;AAChB,iBAAK,eAAe,KAAK,+BAA+B,QAAQ,EAAE,MAAM;AAAA,UACzE,GAAG,GAAG;AAAA,QACP;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,qCAAqC;AACpC,YAAM,KAAK;AACX,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,YAAM,EAAE,iBAAiB,gBAAgB,kBAAkB,IAAI,KAAK,OAAO,qBAAqB;AAEhG,WAAK,eAAe,KAAK,8CAA8C,EAAE,OAAO,EAAE,OAAO;AAEzF,UAAI,CAAC;AAAiB;AAEtB,UAAI,aAAa,WAAW;AAC5B,UAAI,CAAC,gBAAgB;AACpB,sBAAc,GAAG,yCAAyC;AAC1D,oBAAY;AAAA,MACb,OAAO;AACN,gCAAwB;AAAA,UACvB,IAAI,cAAc,IAAI,IAAI,iBAAiB;AAAA,UAC3C,UAAU,kBAAkB,GAAG;AAAA,QAChC;AACA,sBAAc,GAAG,4BAA4B,CAAC,gBAAgB,qBAAqB,CAAC,CAAC;AACrF,oBAAY;AAAA,MACb;AAEA,YAAM,SAAS,KAAK,eAAe,SAAS,EAAE,SAAS,MAAM,IAAI,SAAS;AAC1E,YAAM,SAAS,IAAI,iBAAiB,IAAI,gBAAgB,IAAI,gBAAgB,IAAI,QAAQ,IAAI;AAC5F,WAAK,eAAe;AAAA,QACnB;AAAA;AAAA;AAAA,qDAGkD;AAAA,wCACb;AAAA;AAAA;AAAA;AAAA,MAItC;AAEA,WAAK,4BAA4B,OAAO,GAAG,KAAK,aAAa;AAAA,QAC5D,IAAI;AAAA,UACH,OAAO,GAAG,uBAAuB;AAAA,UACjC,WAAW;AAAA,UACX,aAAa,GAAG,8BAA8B;AAAA,UAC9C,SAAS;AAAA,UACT;AAAA,UACA,UAAU,iBAAkB;AAC3B,gBAAI,CAAC;AAAgB;AAErB,gBAAI,KAAK,QAAQ,uBAAuB;AACvC,qBAAO,WAAW;AAAA,gBACjB,SAAS,GAAG,oCAAoC;AAAA,kBAC/C,gBAAgB,qBAAqB;AAAA,gBACtC,CAAC;AAAA,gBACD,WAAW;AAAA,cACZ,CAAC;AACD,qBAAO,MAAM,WAAW,QAAQ;AAChC,iBAAG,0BAA0B,UAAU,CAAC;AACxC;AAAA,YACD;AACA,kBAAM,wBAAwB,KAAK,QAAQ,IAAI,IAAI;AACnD,kBAAM,OAAO,MAAM;AAAA,cAClB,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ;AAAA,cACA;AAAA,YACD;AACA,mBAAO,MAAM;AAAA,cACZ,IAAI;AAAA,cACJ,IAAI;AAAA,cACJ;AAAA,cACA,SAAS,KAAK,QAAQ,iBAAiB;AAAA,YACxC;AAAA,UACD;AAAA,UACA;AAAA,QACD;AAAA,QACA,QAAQ,KAAK,eAAe,KAAK,yCAAyC;AAAA,QAC1E,cAAc;AAAA,MACf,CAAC;AACD,WAAK,0BAA0B,aAAa,KAAK;AAAA,IAElD;AAAA,IAEA,gCAAgC;AAC/B,YAAM,YAAY,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC5C,UAAI,cAAc;AACjB,aAAK,eAAe;AAAA,UACnB;AAAA;AAAA;AAAA,QAGD;AAAA,IACF;AAAA,IAEA,sBAAsB,KAAK;AAC1B,UAAI,CAAC;AAAK,cAAM,KAAK,OAAO,QAAQ,EAAE;AACtC,YAAM,cAAc,IAAI;AACxB,YAAM,cAAc,KAAK,OAAO,aAAa,qBAAqB,IAC/D,IAAI,cACJ,IAAI;AACP,YAAM,YAAY,cAAc,IAAI;AACpC,YAAM,SAAS,IAAI,iBAAiB,aAAa,IAAI,KAAK,YAAY;AACtE,YAAM,WAAW,IAAI;AAErB,YAAM,QAAQ,YAAY,IAAI,GAAG,oBAAoB,IAAI,GAAG,eAAe;AAE3E,WAAK,QAAQ;AAAA,QACZ;AAAA,+BAC4B,GAAG,aAAa;AAAA,yBACtB,gBAAgB,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA,+BAI/B,GAAG,aAAa;AAAA,yBACtB,gBAAgB,aAAa,QAAQ;AAAA;AAAA;AAAA;AAAA,+BAI/B;AAAA,yBACN,gBAAgB,KAAK,IAAI,UAAU,SAAS,GAAG,QAAQ;AAAA;AAAA,MAE9E;AAAA,IACD;AAAA,IAEA,iBAAiB,MAAM;AACtB,aAAO,KAAK,WAAW,IAAI,WAAW,MAAM,IAAI,KAAK,WAAW,IAAI,WAAW,MAAM;AAAA,IACtF;AAAA,IAEA,yBAAyB,iBAAiB;AACzC,aAAO,gBACL,QAAQ,OAAO,GAAG,EAClB,QAAQ,qBAAqB,EAAE,EAC/B,QAAQ,sBAAsB,EAAE,EAChC,YAAY;AAAA,IACf;AAAA,IAEA,MAAM,mCAAmC;AACxC,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,UAAI,IAAI,MAAM,OAAO,GAAG;AAAA,QACvB;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,MACD;AAEA,UAAI,CAAC,EAAE,QAAQ,oCAAoC;AAClD,aAAK,qBAAqB;AAAA,MAC3B;AAAA,IACD;AAAA,IAEA,+BAA+B;AAC9B,YAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;AAClC,UAAI,kBAAkB;AACtB,eAAS,SAAS,KAAK,qBAAqB;AAC3C,YAAI,CAAC,IAAI,MAAM,YAAY;AAC1B,4BAAkB;AAClB,iBAAO,WAAW;AAAA,YACjB,SAAS,GAAG,6BAA6B,CAAC,MAAM,KAAK,CAAC;AAAA,YACtD,WAAW;AAAA,UACZ,CAAC;AACD,iBAAO,MAAM,WAAW,OAAO;AAAA,QAChC;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA,EACD;;;ACp7BA,UAAQ,eAAe,gBAAgB,MAAM;AAAA,IAC5C,YAAY,EAAE,SAAS,OAAO,GAAG;AAChC,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,kBAAkB,EAAE,aAAa,IAAI,QAAQ,QAAQ;AAC1D,WAAK,aAAa;AAAA,QACjB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,aAAa;AAAA,MACd;AACA,WAAK,eAAe,CAAC;AACrB,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,wBAAwB;AAC7B,WAAK,oBAAoB;AACzB,WAAK,YAAY;AAAA,IAElB;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ;AAAA,QACZ;AAAA;AAAA,0BAEuB,GAAG,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1C;AAEA,WAAK,aAAa,KAAK,QAAQ,KAAK,kBAAkB;AACtD,WAAK,sBAAsB,KAAK,WAAW,KAAK,qBAAqB;AACrE,WAAK,sBAAsB,KAAK,WAAW,KAAK,qBAAqB;AAAA,IACtE;AAAA,IAEA,0BAA0B;AAEzB,YAAM,gBAAgB,SAAS,eAAe,uBAAuB;AACrE,UAAI,eAAe;AAClB,sBAAc,OAAO;AAAA,MACtB;AAEA,YAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,KAAK;AACX,YAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoPpB,eAAS,KAAK,YAAY,KAAK;AAG/B,iBAAW,MAAM;AAChB,YAAI,KAAK,uBAAuB,KAAK,oBAAoB,QAAQ;AAChE,eAAK,oBAAoB,GAAG;AAAA,QAC7B;AAAA,MACD,GAAG,CAAC;AAAA,IACL;AAAA,IAEA,4BAA4B;AAE3B,WAAK,oBAAoB,KAAK,iBAAiB,EAAE,KAAK,WAAW;AAChE,UAAE,IAAI,EAAE,YAAY,gBAAgB,EAAE,SAAS,gBAAgB;AAAA,MAChE,CAAC;AAGD,UAAI,KAAK,oBAAoB,IAAI;AAChC,aAAK,oBAAoB,GAAG;AAAA,MAC7B;AAAA,IACD;AAAA,IAEA,cAAc;AACb,WAAK,aAAa,OAAO,GAAG,SAAS,CAAC,MAAM;AAC3C,qBAAa,KAAK,WAAW;AAC7B,aAAK,cAAc,WAAW,MAAM;AACnC,gBAAM,cAAc,EAAE,OAAO;AAC7B,eAAK,gBAAgB,cAAc;AACnC,eAAK,WAAW,eAAe;AAC/B,eAAK,aAAa,aAAa,KAAK,aAAa,UAAU,CAAC;AAAA,QAC7D,GAAG,GAAG;AAAA,MACP,CAAC;AAED,YAAM,KAAK;AACX,WAAK,oBAAoB,GAAG,SAAS,oBAAoB,WAAY;AACpE,cAAM,eAAe,SAAS,EAAE,IAAI,EAAE,KAAK,mBAAmB,CAAC;AAC/D,WAAG,OAAO,kBAAkB,YAAY;AAAA,MACzC,CAAC;AAGD,QAAE,MAAM,EAAE,GAAG,UAAU,KAAK,SAAS,MAAM;AAC1C,aAAK,cAAc;AAAA,MACpB,GAAG,GAAG,CAAC;AAAA,IACR;AAAA,IAEA,SAAS,MAAM,MAAM;AACpB,UAAI;AACJ,aAAO,SAAS,oBAAoB,MAAM;AACzC,cAAM,QAAQ,MAAM;AACnB,uBAAa,OAAO;AACpB,eAAK,GAAG,IAAI;AAAA,QACb;AACA,qBAAa,OAAO;AACpB,kBAAU,WAAW,OAAO,IAAI;AAAA,MACjC;AAAA,IACD;AAAA,IAEA,gBAAgB;AAEf,UAAI,OAAO,cAAc,KAAK;AAC7B,aAAK,WAAW,iBAAiB;AAAA,MAClC,WAAW,OAAO,cAAc,KAAK;AACpC,aAAK,WAAW,iBAAiB;AAAA,MAClC,OAAO;AACN,aAAK,WAAW,iBAAiB;AAAA,MAClC;AAGA,WAAK,qBAAqB;AAC1B,WAAK,oBAAoB;AACzB,WAAK,kBAAkB;AAAA,IACxB;AAAA,IAEA,sBAAsB;AACrB,YAAM,KAAK;AAEX,WAAK,eAAe,OAAO,GAAG,KAAK,aAAa;AAAA,QAC/C,IAAI;AAAA,UACH,OAAO,GAAG,QAAQ;AAAA,UAClB,WAAW;AAAA,UACX,aAAa,GAAG,uCAAuC;AAAA,QACxD;AAAA,QACA,QAAQ,KAAK,WAAW,KAAK,eAAe;AAAA,QAC5C,cAAc;AAAA,MACf,CAAC;AAED,WAAK,eAAe,OAAO,GAAG,KAAK,aAAa;AAAA,QAC/C,IAAI;AAAA,UACH,OAAO,GAAG,gBAAgB;AAAA,UAC1B,WAAW;AAAA,UACX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,UACT,aAAa,GAAG,0BAA0B;AAAA,UAC1C,UAAU,WAAY;AACrB,gBAAI,GAAG,WAAW,GAAG,UAAU,GAAG;AACjC,iBAAG,gBAAgB,SAAS,KAAK,UAAU;AAC3C,iBAAG,WAAW,eAAe;AAC7B,iBAAG,aAAa;AAAA,YACjB;AAAA,UACD;AAAA,QACD;AAAA,QACA,QAAQ,KAAK,WAAW,KAAK,eAAe;AAAA,QAC5C,cAAc;AAAA,MACf,CAAC;AAED,WAAK,aAAa,aAAa,KAAK;AACpC,WAAK,aAAa,aAAa,KAAK;AACpC,WAAK,aAAa,UAAU,OAAO;AACnC,WAAK,gBAAgB,SAAS;AAE9B,WAAK,oBAAoB,EAAE;AAAA;AAAA,kCAEK,GAAG,iBAAiB;AAAA;AAAA,GAEnD;AAED,WAAK,WAAW,KAAK,iBAAiB,EAAE,OAAO,KAAK,iBAAiB;AAErE,WAAK,kBAAkB,GAAG,SAAS,MAAM;AACxC,aAAK,uBAAuB;AAAA,MAC7B,CAAC;AAAA,IACF;AAAA,IAEA,yBAAyB;AACxB,UAAI,SAAS,IAAI,OAAO,GAAG,OAAO;AAAA,QACjC,OAAO,GAAG,iBAAiB;AAAA,QAC3B,QAAQ;AAAA,UACP;AAAA,YACC,OAAO,GAAG,UAAU;AAAA,YACpB,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,MAAM;AAAA,YACN,WAAW,WAAW;AACrB,qBAAO;AAAA,gBACN,SAAS;AAAA,kBACR,YAAY;AAAA,gBACb;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QACA,sBAAsB,GAAG,QAAQ;AAAA,QACjC,gBAAgB,CAAC,WAAW;AAC3B,cAAI,CAAC,OAAO,UAAU;AACrB,mBAAO,SAAS,GAAG,wBAAwB,CAAC;AAC5C;AAAA,UACD;AAEA,eAAK,qBAAqB,OAAO,QAAQ;AACzC,iBAAO,KAAK;AAAA,QACb;AAAA,MACD,CAAC;AAED,aAAO,KAAK;AAAA,IACb;AAAA,IAEA,qBAAqB,UAAU;AAC9B,aAAO,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,SAAS,OAAO,SAAS,iBAAiB,SAAS;AAAA,QACpD;AAAA,QACA,UAAU,CAAC,MAAM;AAChB,cAAI,OAAO,EAAE,YAAY,UAAU;AAClC,gBAAI,qBAAqB,KAAK,IAAI,EAAE,OAAO;AAE3C,mBAAO,QAAQ,eAAe;AAC9B,uBAAW,MAAM;AAChB,kBAAI,WAAW,QAAQ,IAAI,YAAY,iBAAiB;AACvD,wBAAQ,UAAU,cAAc,UAAU;AAC1C,wBAAQ,UAAU,SAAS,QAAQ;AACnC,wBAAQ,UAAU,gBAAgB,SAAS;AAAA,cAC5C;AAAA,YACD,GAAG,GAAG;AAAA,UACP,OAAO;AACN,mBAAO,SAAS,GAAG,uDAAuD,CAAC,QAAQ,CAAC,CAAC;AAAA,UACtF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,uBAAuB,QAAQ,cAAc,IAAI;AAChD,cAAQ,IAAI,0BAA0B,uBAAuB,aAAa;AAE1E,WAAK,gBAAgB;AAErB,WAAK,gBAAgB,SAAS;AAC9B,WAAK,gBAAgB,cAAc;AACnC,WAAK,WAAW,eAAe;AAE/B,WAAK,aAAa,UAAU,MAAM;AAClC,UAAI,aAAa;AAChB,aAAK,aAAa,UAAU,WAAW;AAAA,MACxC,OAAO;AACN,aAAK,aAAa,UAAU,EAAE;AAAA,MAC/B;AAEA,WAAK,aAAa,aAAa,MAAM;AAAA,IACtC;AAAA,IAEA,kBAAkB;AACjB,WAAK,oBAAoB,KAAK;AAAA;AAAA,WAErB,GAAG,mBAAmB;AAAA;AAAA,GAE9B;AACD,WAAK,oBAAoB,KAAK,EAAE;AAAA,IACjC;AAAA,IAEA,aAAa,cAAc,MAAM,SAAS,MAAM;AAC/C,YAAM,oBAAoB,gBAAgB,OAAO,cAAc,KAAK,gBAAgB;AACpF,YAAM,eAAe,WAAW,OAAO,SAAS,KAAK,gBAAgB;AAErE,cAAQ,IAAI,sCAAsC,+BAA+B,eAAe;AAEhG,WAAK,gBAAgB,cAAc;AACnC,WAAK,gBAAgB,SAAS;AAE9B,UAAI,KAAK,oBAAoB,KAAK,gBAAgB,EAAE,WAAW,GAAG;AACjE,aAAK,gBAAgB;AAAA,MACtB;AAEA,WAAK,OAAO,cAAc;AAE1B,aAAO,OAAO,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,QACA,UAAU,CAAC,aAAa;AACvB,eAAK,oBAAoB,KAAK,EAAE;AAEhC,cAAI,SAAS,WAAW,SAAS,QAAQ,SAAS,GAAG;AACpD,iBAAK,eAAe,SAAS;AAC7B,iBAAK,qBAAqB;AAC1B,iBAAK,oBAAoB;AACzB,iBAAK,kBAAkB;AAEvB,iBAAK,oBAAoB,cAAc,SAAS,QAAQ,MAAM;AAAA,UAC/D,OAAO;AACN,iBAAK,eAAe,CAAC;AACrB,iBAAK,WAAW,cAAc;AAC9B,iBAAK,WAAW,cAAc;AAE9B,iBAAK,oBAAoB;AAAA,cACxB;AAAA;AAAA,cAEQ,GAAG,2CAA2C;AAAA,0EACc,GAAG,QAAQ,MAAM;AAAA;AAAA,YAEtF;AACA,iBAAK,oBAAoB,KAAK,EAAE;AAAA,UACjC;AAAA,QACD;AAAA,QACA,OAAO,CAAC,UAAU;AACjB,kBAAQ,MAAM,+BAA+B,KAAK;AAClD,eAAK,oBAAoB,KAAK;AAAA;AAAA;AAAA,aAGrB,GAAG,2BAA2B;AAAA,2DACgB,GAAG,kBAAkB;AAAA;AAAA,KAE3E;AACD,eAAK,oBAAoB,KAAK,EAAE;AAChC,iBAAO,SAAS,GAAG,8CAA8C,CAAC;AAAA,QACnE;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,uBAAuB;AACtB,WAAK,WAAW,cAAc,KAAK,aAAa;AAChD,WAAK,WAAW,cAAc,KAAK,KAAK,KAAK,WAAW,cAAc,KAAK,WAAW,cAAc;AAGpG,UAAI,KAAK,WAAW,eAAe,KAAK,WAAW,aAAa;AAC/D,aAAK,WAAW,eAAe,KAAK,IAAI,GAAG,KAAK,WAAW,WAAW;AAAA,MACvE;AAAA,IACD;AAAA,IAEA,sBAAsB;AACrB,YAAM,eAAe,KAAK,WAAW,eAAe,KAAK,KAAK,WAAW;AACzE,YAAM,YAAY,cAAc,KAAK,WAAW;AAChD,YAAM,wBAAwB,KAAK,aAAa,MAAM,aAAa,SAAS;AAE5E,WAAK,oBAAoB,KAAK,EAAE;AAEhC,UAAI,sBAAsB,SAAS,GAAG;AACrC,8BAAsB,QAAQ,CAAC,SAAS,UAAU;AACjD,gBAAM,eAAe,KAAK,iBAAiB,OAAO;AAClD,gBAAM,WAAW,EAAE,YAAY;AAG/B,mBAAS,IAAI,mBAAmB,GAAG,MAAO,QAAQ,OAAQ;AAE1D,eAAK,oBAAoB,OAAO,QAAQ;AAAA,QACzC,CAAC;AAAA,MACF;AAAA,IACD;AAAA,IAEA,oBAAoB;AACnB,UAAI,KAAK,WAAW,eAAe,GAAG;AACrC,aAAK,oBAAoB,KAAK,EAAE;AAChC;AAAA,MACD;AAEA,YAAM,cAAc,KAAK,WAAW,eAAe,KAAK,KAAK,WAAW,iBAAiB;AACzF,YAAM,WAAW,KAAK,IAAI,aAAa,KAAK,WAAW,iBAAiB,GAAG,KAAK,WAAW,WAAW;AAEtG,UAAI,kBAAkB;AAAA;AAAA;AAAA,OAGjB,GAAG,SAAS,KAAK,cAAc,YAAY,GAAG,IAAI,KAAK,KAAK,WAAW,eAAe,GAAG,QAAQ;AAAA;AAAA;AAAA,yDAG/C,KAAK,WAAW,iBAAiB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAOzG,YAAM,oBAAoB;AAC1B,UAAI,aAAa,KAAK,IAAI,GAAG,KAAK,WAAW,eAAe,KAAK,MAAM,oBAAoB,CAAC,CAAC;AAC7F,UAAI,WAAW,KAAK,IAAI,KAAK,WAAW,aAAa,aAAa,oBAAoB,CAAC;AAGvF,UAAI,WAAW,aAAa,IAAI,mBAAmB;AAClD,qBAAa,KAAK,IAAI,GAAG,WAAW,oBAAoB,CAAC;AAAA,MAC1D;AAEA,eAAS,IAAI,YAAY,KAAK,UAAU,KAAK;AAC5C,2BAAmB;AAAA,oCACc,MAAM,KAAK,WAAW,eAAe,WAAW,qCAAqC;AAAA,OAClH;AAAA;AAAA;AAAA,MAGL;AAEA,yBAAmB;AAAA;AAAA,yDAEoC,KAAK,WAAW,iBAAiB,KAAK,WAAW,cAAc,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAOnI,WAAK,oBAAoB,KAAK,eAAe;AAE7C,iBAAW,MAAM;AAChB,aAAK,0BAA0B;AAAA,MAChC,GAAG,GAAG;AAGN,WAAK,oBAAoB,KAAK,iBAAiB,EAAE,GAAG,SAAS,CAAC,MAAM;AACnE,cAAM,OAAO,EAAE,EAAE,aAAa;AAC9B,YAAI,KAAK,KAAK,UAAU;AAAG;AAE3B,cAAM,SAAS,KAAK,KAAK,QAAQ;AACjC,YAAI,WAAW,KAAK,WAAW;AAE/B,gBAAQ;AAAA,eACF;AACJ,uBAAW;AACX;AAAA,eACI;AACJ,uBAAW,KAAK,IAAI,GAAG,KAAK,WAAW,eAAe,CAAC;AACvD;AAAA,eACI;AACJ,uBAAW,KAAK,IAAI,KAAK,WAAW,aAAa,KAAK,WAAW,eAAe,CAAC;AACjF;AAAA,eACI;AACJ,uBAAW,KAAK,WAAW;AAC3B;AAAA,eACI;AACJ,uBAAW,SAAS,KAAK,KAAK,MAAM,CAAC;AACrC;AAAA;AAGF,YAAI,aAAa,KAAK,WAAW,cAAc;AAC9C,eAAK,UAAU,QAAQ;AAAA,QACxB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,UAAU,MAAM;AACf,UAAI,OAAO,KAAK,OAAO,KAAK,WAAW,eAAe,SAAS,KAAK,WAAW,cAAc;AAC5F;AAAA,MACD;AAGA,WAAK,oBAAoB,SAAS,sBAAsB;AAExD,iBAAW,MAAM;AAChB,aAAK,WAAW,eAAe;AAC/B,aAAK,oBAAoB;AACzB,aAAK,kBAAkB;AAEvB,aAAK,oBAAoB,YAAY,sBAAsB;AAC3D,aAAK,oBAAoB,SAAS,uBAAuB;AAEzD,mBAAW,MAAM;AAChB,eAAK,oBAAoB,YAAY,uBAAuB;AAAA,QAC7D,GAAG,GAAG;AAAA,MACP,GAAG,GAAG;AAAA,IACP;AAAA,IAEA,oBAAoB,QAAQ,OAAO;AAClC,YAAM,QAAQ,KAAK,WAAW,KAAK,QAAQ;AAC3C,YAAM,KAAK,GAAG,GAAG,eAAe,OAAO,WAAW,QAAQ;AAAA,IAC3D;AAAA,IAEA,iBAAiB,SAAS;AACzB,YAAM,mBAAmB,OAAO,SAAS;AAAA,QACxC,QAAQ,eAAe,MAAM,QAAQ;AAAA,MACtC;AAEA,aAAO,mDAAmD,OAAO,QAAQ,IAAI;AAAA;AAAA,iCAE9C,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMjC,OAAO,SAAS,QAAQ,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA,kCAIV,gBAAgB,QAAQ,aAAa,QAAQ,QAAQ,KAAK;AAAA,qCACvD;AAAA;AAAA;AAAA,IAGpC;AAAA,IAEA,iBAAiB,MAAM;AACtB,UAAI,MAAM;AACT,aAAK,WAAW,IAAI,WAAW,MAAM;AACrC,YAAI,CAAC,KAAK,gBAAgB,QAAQ;AACjC,eAAK,gBAAgB,SAAS;AAC9B,eAAK,aAAa,UAAU,OAAO;AAAA,QACpC;AACA,aAAK,WAAW,eAAe;AAC/B,aAAK,aAAa;AAClB,aAAK,cAAc;AAAA,MACpB,OAAO;AACN,aAAK,WAAW,IAAI,WAAW,MAAM;AACrC,aAAK,gBAAgB;AAAA,MACtB;AAAA,IACD;AAAA,IAEA,gBAAgB;AACf,WAAK,kBAAkB,EAAE,aAAa,IAAI,QAAQ,QAAQ;AAC1D,WAAK,WAAW,eAAe;AAC/B,WAAK,aAAa,UAAU,EAAE;AAC9B,WAAK,aAAa,UAAU,OAAO;AACnC,WAAK,WAAW,KAAK,QAAQ,EAAE,KAAK,GAAG,eAAe,CAAC;AAAA,IACxD;AAAA,IAEA,kBAAkB,cAAc;AAE/B,YAAM,gBAAgB,KAAK,aAAa,UAAU,SAAO,IAAI,SAAS,YAAY;AAClF,UAAI,kBAAkB,IAAI;AACzB,cAAM,cAAc,KAAK,MAAM,gBAAgB,KAAK,KAAK,WAAW,cAAc;AAElF,YAAI,gBAAgB,KAAK,WAAW,cAAc;AACjD,eAAK,UAAU,WAAW;AAAA,QAC3B;AAGA,mBAAW,MAAM;AAChB,gBAAM,kBAAkB,KAAK,oBAAoB,KAAK,uBAAuB,OAAO,YAAY,KAAK;AACrG,cAAI,gBAAgB,QAAQ;AAE3B,4BAAgB,IAAI;AAAA,cACnB,cAAc;AAAA,cACd,aAAa;AAAA,cACb,cAAc;AAAA,YACf,CAAC;AAGD,uBAAW,MAAM;AAChB,8BAAgB,IAAI;AAAA,gBACnB,cAAc;AAAA,gBACd,aAAa;AAAA,gBACb,cAAc;AAAA,cACf,CAAC;AAAA,YACF,GAAG,GAAI;AAAA,UACR;AAAA,QACD,GAAG,GAAG;AAAA,MACP;AAAA,IACD;AAAA,IAEA,sBAAsB;AACrB,aAAO;AAAA,QACN,MAAM,KAAK,WAAW;AAAA,QACtB,YAAY,KAAK,oBAAoB,UAAU;AAAA,MAChD;AAAA,IACD;AAAA,IAEA,oBAAoB,UAAU;AAC7B,UAAI,SAAS,QAAQ,SAAS,SAAS,KAAK,WAAW,cAAc;AACpE,aAAK,UAAU,SAAS,IAAI;AAAA,MAC7B;AACA,UAAI,SAAS,YAAY;AACxB,mBAAW,MAAM;AAChB,eAAK,oBAAoB,UAAU,SAAS,UAAU;AAAA,QACvD,GAAG,GAAG;AAAA,MACP;AAAA,IACD;AAAA,IAGA,wBAAwB;AACvB,aAAO;AAAA,QACN,cAAc,KAAK,WAAW;AAAA,QAC9B,aAAa,KAAK,WAAW;AAAA,QAC7B,gBAAgB,KAAK,WAAW;AAAA,QAChC,aAAa,KAAK,WAAW;AAAA,QAC7B,aAAa,KAAK,WAAW,eAAe,KAAK,KAAK,WAAW,iBAAiB;AAAA,QAClF,UAAU,KAAK,IAAI,KAAK,WAAW,eAAe,KAAK,WAAW,gBAAgB,KAAK,WAAW,WAAW;AAAA,MAC9G;AAAA,IACD;AAAA,IAEA,0BAA0B;AACzB,WAAK,qBAAqB;AAC1B,WAAK,kBAAkB;AAAA,IACxB;AAAA,IAGA,4BAA4B;AAC3B,QAAE,QAAQ,EAAE,GAAG,sBAAsB,CAAC,MAAM;AAC3C,YAAI,CAAC,KAAK,WAAW,GAAG,UAAU;AAAG;AAGrC,YAAI,EAAE,EAAE,MAAM,EAAE,GAAG,yBAAyB;AAAG;AAE/C,gBAAO,EAAE;AAAA,eACH;AACJ,gBAAI,KAAK,WAAW,eAAe,GAAG;AACrC,gBAAE,eAAe;AACjB,mBAAK,UAAU,KAAK,WAAW,eAAe,CAAC;AAAA,YAChD;AACA;AAAA,eACI;AACJ,gBAAI,KAAK,WAAW,eAAe,KAAK,WAAW,aAAa;AAC/D,gBAAE,eAAe;AACjB,mBAAK,UAAU,KAAK,WAAW,eAAe,CAAC;AAAA,YAChD;AACA;AAAA,eACI;AACJ,gBAAI,KAAK,WAAW,iBAAiB,GAAG;AACvC,gBAAE,eAAe;AACjB,mBAAK,UAAU,CAAC;AAAA,YACjB;AACA;AAAA,eACI;AACJ,gBAAI,KAAK,WAAW,iBAAiB,KAAK,WAAW,aAAa;AACjE,gBAAE,eAAe;AACjB,mBAAK,UAAU,KAAK,WAAW,WAAW;AAAA,YAC3C;AACA;AAAA;AAAA,MAEH,CAAC;AAAA,IACF;AAAA,IAEA,UAAU;AAET,QAAE,QAAQ,EAAE,IAAI,oBAAoB;AACpC,QAAE,MAAM,EAAE,IAAI,QAAQ;AAGtB,UAAI,KAAK,aAAa;AACrB,qBAAa,KAAK,WAAW;AAAA,MAC9B;AAAA,IACD;AAAA,EACD;;;ACz2BA,UAAQ,eAAe,mBAAmB,MAAM;AAAA,IAC/C,YAAY,EAAE,SAAS,UAAU,OAAO,GAAG;AAC1C,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,kCAAkC,SAAS;AAEhD,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,iBAAiB;AAChB,WAAK,YAAY;AACjB,WAAK,wBAAwB;AAC7B,WAAK,sBAAsB;AAC3B,WAAK,YAAY;AACjB,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ;AAAA,QACZ;AAAA;AAAA,OAEI,GAAG,wCAAwC;AAAA;AAAA;AAAA;AAAA;AAAA,2BAKvB,GAAG,OAAO;AAAA;AAAA,2BAEV,GAAG,QAAQ;AAAA;AAAA,2BAEX,GAAG,UAAU;AAAA;AAAA;AAAA,4BAGZ,GAAG,oBAAoB;AAAA;AAAA;AAAA;AAAA,0CAIT,GAAG,wBAAwB;AAAA;AAAA;AAAA,oEAGD,GAAG,eAAe;AAAA,6FACO,GAAG,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQ/G;AAEA,WAAK,aAAa,KAAK,QAAQ,KAAK,qBAAqB;AACzD,WAAK,mBAAmB,KAAK,WAAW,KAAK,0BAA0B;AACvE,WAAK,qBAAqB,KAAK,WAAW,KAAK,gBAAgB;AAC/D,WAAK,iBAAiB,KAAK,mBAAmB,KAAK,gBAAgB;AACnE,WAAK,mBAAmB,KAAK,mBAAmB,KAAK,kBAAkB;AACvE,WAAK,oBAAoB,KAAK,mBAAmB,KAAK,mBAAmB;AACzE,WAAK,qBAAqB,KAAK,mBAAmB,KAAK,qBAAqB;AAC5E,WAAK,gBAAgB,KAAK,mBAAmB,KAAK,eAAe;AACjE,WAAK,6BAA6B,KAAK,mBAAmB,KAAK,oBAAoB;AACnF,WAAK,mBAAmB,KAAK,mBAAmB,KAAK,kBAAkB;AACvE,WAAK,qBAAqB,KAAK,mBAAmB,KAAK,oBAAoB;AAAA,IAC5E;AAAA,IAEA,wBAAwB;AAEvB,WAAK,mBAAmB,IAAI,OAAO,GAAG,OAAO;AAAA,QAC5C,OAAO,GAAG,oBAAoB;AAAA,QAC9B,QAAQ;AAAA,UACP;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,WAAW;AAAA,YACrB,MAAM;AAAA,UACP;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,UACZ;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,eAAe;AAAA,YACzB,WAAW;AAAA,UACZ;AAAA,QACD;AAAA,QACA,gBAAgB,CAAC,WAAW;AAC3B,eAAK,eAAe,OAAO,SAAS;AAAA,QACrC;AAAA,QACA,sBAAsB,GAAG,gBAAgB;AAAA,MAC1C,CAAC;AAAA,IACF;AAAA,IAEA,0BAA0B;AACzB,YAAM,eAAe,IAAI,OAAO,GAAG,OAAO;AAAA,QACzC,OAAO,GAAG,eAAe;AAAA,QACzB,QAAQ;AAAA,UACP,EAAE,WAAW,YAAY,WAAW,QAAQ,SAAS,SAAS,OAAO,YAAY,MAAM,EAAE;AAAA,UACzF,EAAE,WAAW,WAAW,WAAW,cAAc,OAAO,mBAAmB;AAAA,QAC5E;AAAA,QACA,gBAAgB,MAAM;AACrB,eAAK,WAAW;AAAA,QACjB;AAAA,QACA,sBAAsB,GAAG,MAAM;AAAA,MAChC,CAAC;AACD,WAAK,eAAe;AAEpB,YAAM,eAAe,IAAI,OAAO,GAAG,OAAO;AAAA,QACzC,OAAO,GAAG,eAAe;AAAA,QACzB,QAAQ,CAAC,EAAE,WAAW,SAAS,WAAW,QAAQ,OAAO,gBAAgB,CAAC;AAAA,QAC1E,gBAAgB,MAAM;AACrB,eAAK,cAAc;AAAA,QACpB;AAAA,QACA,sBAAsB,GAAG,OAAO;AAAA,MACjC,CAAC;AACD,WAAK,eAAe;AAAA,IACrB;AAAA,IAEA,uBAAuB,KAAK;AAC3B,YAAM,EAAE,OAAO,IAAI;AACnB,UAAI,kBAAkB;AAEtB,OAAC,QAAQ,cAAc,EAAE,SAAS,MAAM,MAAM,kBAAkB;AAChE,iBAAW,YAAY,kBAAkB;AACzC,iBAAW,aAAa,kBAAkB;AAC1C,iBAAW,kBAAkB,kBAAkB;AAE/C,YAAM,cAAc,IAAI,eAAgB,IAAI,eAAe,IAAI,sBAAsB;AACrF,YAAM,qBAAqB,IAAI,sBAAsB;AAErD,UAAI,WAAW,eAAe;AAC7B,eAAO;AAAA,mCACyB,IAAI;AAAA,oCACH,KAAK;AAAA,6BACZ,GAAG,SAAS,MAAM,IAAI;AAAA;AAAA;AAAA;AAAA,kCAIjB,GAAG,MAAM,MAAM,gBAAgB,aAAa,IAAI,QAAQ;AAAA,qDACrC,GAAG,aAAa,MAAM,gBAAgB,oBAAoB,IAAI,QAAQ;AAAA;AAAA,kCAEzF,IAAI;AAAA,sDACgB,0BAA0B,GAAG,IAAI,MAAM;AAAA;AAAA,MAE3F;AAEA,aAAO;AAAA,kCACyB,IAAI;AAAA,mCACH,KAAK;AAAA,4BACZ,GAAG,SAAS,MAAM,IAAI;AAAA;AAAA;AAAA,gCAGlB,gBAAgB,aAAa,IAAI,QAAQ;AAAA,iCACxC,IAAI;AAAA,qDACgB,0BAA0B,GAAG,IAAI,MAAM;AAAA;AAAA,IAE3F;AAAA,IAEA,cAAc,KAAK,WAAW;AAC7B,aAAO;AAAA,8BACqB,UAAU;AAAA,6BACX,UAAU,OAAO,KAAK,UAAU;AAAA,mCAC1B,uBAAuB;AAAA;AAGxD,eAAS,yBAAyB;AACjC,YAAI,UAAU,QAAQ,UAAU,mBAAmB,UAAU,SAAS,UAAU,iBAAiB;AAChG,iBAAO,4BAA4B,UAAU;AAAA,+BAClB,gBAAgB,UAAU,MAAM,IAAI,QAAQ;AAAA,QACxE,OAAO;AACN,iBAAO,0BAA0B;AAAA,YAChC,UAAU,mBAAmB,UAAU;AAAA,YACvC,IAAI;AAAA,UACL;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IAEA,kBAAkB,KAAK;AACtB,UAAI,IAAI,iBAAiB;AACxB,eAAO;AAAA,aACG,GAAG,UAAU,MAAM,IAAI;AAAA,aACvB,gBAAgB,IAAI,iBAAiB,IAAI,QAAQ;AAAA;AAAA,MAE5D,OAAO;AACN,eAAO;AAAA,MACR;AAAA,IACD;AAAA,IAEA,mBAAmB,KAAK;AACvB,aAAO;AAAA,YACG,GAAG,WAAW;AAAA,YACd,gBAAgB,IAAI,WAAW,IAAI,QAAQ;AAAA;AAAA,IAEtD;AAAA,IAEA,eAAe,KAAK;AACnB,UAAI,CAAC,IAAI,MAAM;AAAQ,eAAO;AAE9B,UAAI,aAAa,IAAI,MACnB,IAAI,CAAC,MAAM;AAEX,cAAM,cAAc,SAAS,KAAK,EAAE,WAAW,IAC5C,EAAE,cACF,EAAE,QAAQ,IACV,GAAG,EAAE,iBAAiB,EAAE,UACxB,EAAE;AACL,eAAO;AAAA;AAAA,8BAEmB;AAAA,8BACA,gBAAgB,EAAE,kCAAkC,IAAI,QAAQ;AAAA;AAAA;AAAA,MAG3F,CAAC,EACA,KAAK,EAAE;AAET,aAAO,8BAA8B;AAAA,IACtC;AAAA,IAEA,qBAAqB,KAAK;AACzB,aAAO;AAAA,YACG,GAAG,aAAa;AAAA,YAChB,gBAAgB,IAAI,aAAa,IAAI,QAAQ;AAAA;AAAA,IAExD;AAAA,IAEA,iBAAiB,KAAK,SAAS;AAC9B,aAAO;AAAA,YACG,GAAG,QAAQ,eAAe;AAAA,YAC1B,gBAAgB,QAAQ,QAAQ,IAAI,QAAQ;AAAA;AAAA,IAEvD;AAAA,IAEA,cAAc;AAEb,WAAK,mBAAmB,GAAG,SAAS,eAAe,MAAM;AACxD,aAAK,OAAO,eAAe,KAAK,IAAI,IAAI;AACxC,aAAK,iBAAiB,KAAK;AAC3B,aAAK,WAAW,KAAK,yBAAyB,EAAE,IAAI,WAAW,MAAM;AACrE,aAAK,iBAAiB,IAAI,WAAW,MAAM;AAAA,MAC5C,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,aAAa,MAAM;AACtD,aAAK,OAAO,WAAW,KAAK,IAAI,IAAI;AACpC,aAAK,iBAAiB,KAAK;AAC3B,aAAK,WAAW,KAAK,yBAAyB,EAAE,IAAI,WAAW,MAAM;AACrE,aAAK,iBAAiB,IAAI,WAAW,MAAM;AAAA,MAC5C,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,eAAe,MAAM;AACxD,aAAK,OAAO,aAAa,KAAK,IAAI,IAAI;AACtC,aAAK,yBAAyB;AAAA,MAC/B,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,YAAY,MAAM;AACrD,aAAK,OAAO,UAAU;AACtB,aAAK,iBAAiB,KAAK;AAC3B,aAAK,WAAW,KAAK,yBAAyB,EAAE,IAAI,WAAW,MAAM;AACrE,aAAK,iBAAiB,IAAI,WAAW,MAAM;AAAA,MAC5C,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,cAAc,MAAM;AACvD,aAAK,aAAa,YAAY,SAAS,UAAU,KAAK,cAAc;AACpE,aAAK,aAAa,KAAK;AAAA,MACxB,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,cAAc,MAAM;AACvD,aAAK,cAAc;AAAA,MACpB,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,aAAa,MAAM;AACtD,aAAK,mBAAmB;AAAA,MACzB,CAAC;AAGD,WAAK,mBAAmB,GAAG,SAAS,YAAY,MAAM;AACrD,aAAK,sBAAsB;AAAA,MAC5B,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,sBAAsB,MAAM;AAC/D,aAAK,sBAAsB;AAAA,MAC5B,CAAC;AAED,WAAK,mBAAmB,GAAG,SAAS,wBAAwB,MAAM;AACjE,aAAK,gBAAgB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,IAEA,wBAAwB;AAEvB,WAAK,iBAAiB,YAAY,cAAc,UAAU,KAAK,IAAI,QAAQ;AAG3E,UAAI,KAAK,IAAI,oBAAoB;AAChC,aAAK,iBAAiB,YAAY,UAAU,UAAU,KAAK,IAAI,kBAAkB;AAAA,MAClF;AAEA,WAAK,iBAAiB,KAAK;AAAA,IAC5B;AAAA,IAEA,eAAe,gBAAgB;AAC9B,UAAI,CAAC,gBAAgB;AACpB,eAAO,SAAS,GAAG,0CAA0C,CAAC;AAC9D;AAAA,MACD;AAGA,aAAO,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,SAAS;AAAA,UACT,MAAM,KAAK,IAAI;AAAA,UACf,WAAW;AAAA,UACX,OAAO;AAAA,QACR;AAAA,QACA,UAAU,CAAC,MAAM;AAChB,cAAI,CAAC,EAAE,KAAK;AACX,mBAAO,WAAW;AAAA,cACjB,SAAS,GAAG,8BAA8B;AAAA,cAC1C,WAAW;AAAA,YACZ,CAAC;AAGD,iBAAK,iBAAiB,KAAK;AAG3B,iBAAK,IAAI,qBAAqB;AAG9B,iBAAK,uBAAuB;AAG5B,gBAAI,KAAK,OAAO,cAAc;AAC7B,mBAAK,OAAO,aAAa;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,yBAAyB;AAExB,UAAI,KAAK,IAAI,cAAc,GAAG;AAC7B,aAAK,2BAA2B,KAAK;AAGrC,YAAI,KAAK,IAAI,oBAAoB;AAChC,eAAK,iBAAiB,KAAK,OAAO,KAAK,IAAI,kBAAkB,EAAE,KAAK;AACpE,eAAK,mBAAmB,KAAK;AAC7B,eAAK,mBAAmB,KAAK,sBAAsB,EAAE,KAAK;AAAA,QAC3D,OAAO;AACN,eAAK,iBAAiB,KAAK;AAC3B,eAAK,mBAAmB,KAAK;AAC7B,eAAK,mBAAmB,KAAK,sBAAsB,EAAE,KAAK;AAAA,QAC3D;AAAA,MACD;AAAA,IACD;AAAA,IAEA,kBAAkB;AACjB,aAAO,QAAQ,GAAG,+CAA+C,GAAG,MAAM;AACzE,eAAO,KAAK;AAAA,UACX,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,SAAS;AAAA,YACT,MAAM,KAAK,IAAI;AAAA,YACf,WAAW;AAAA,YACX,OAAO;AAAA,UACR;AAAA,UACA,UAAU,CAAC,MAAM;AAChB,gBAAI,CAAC,EAAE,KAAK;AACX,qBAAO,WAAW;AAAA,gBACjB,SAAS,GAAG,gCAAgC;AAAA,gBAC5C,WAAW;AAAA,cACZ,CAAC;AAGD,mBAAK,IAAI,qBAAqB;AAG9B,mBAAK,uBAAuB;AAG5B,kBAAI,KAAK,OAAO,cAAc;AAC7B,qBAAK,OAAO,aAAa;AAAA,cAC1B;AAAA,YACD;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IAEA,qBAAqB;AACpB,YAAM,qBAAqB,KAAK,IAAI,sBAAsB;AAE1D,UAAI,sBAAsB,GAAG;AAC5B,eAAO,SAAS,GAAG,8BAA8B,CAAC;AAClD;AAAA,MACD;AAEA,YAAM,iBAAiB,IAAI,OAAO,GAAG,OAAO;AAAA,QAC3C,OAAO,GAAG,oBAAoB;AAAA,QAC9B,QAAQ;AAAA,UACP;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,oBAAoB;AAAA,YAC9B,SAAS;AAAA,YACT,WAAW;AAAA,UACZ;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,gBAAgB;AAAA,YAC1B,SAAS;AAAA,YACT,MAAM;AAAA,UACP;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,OAAO,GAAG,iBAAiB;AAAA,YAC3B,MAAM;AAAA,YACN,WAAW,MAAM;AAChB,qBAAO;AAAA,gBACN,SAAS,EAAE,SAAS,EAAE;AAAA,cACvB;AAAA,YACD;AAAA,UACD;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,uBAAuB;AAAA,UAClC;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,gBAAgB;AAAA,YAC1B,SAAS,OAAO,SAAS,UAAU;AAAA,UACpC;AAAA,QACD;AAAA,QACA,gBAAgB,CAAC,WAAW;AAC3B,eAAK,qBAAqB,MAAM;AAChC,yBAAe,KAAK;AAAA,QACrB;AAAA,QACA,sBAAsB,GAAG,sBAAsB;AAAA,MAChD,CAAC;AAED,qBAAe,KAAK;AAAA,IACrB;AAAA,IAEA,qBAAqB,QAAQ;AAC5B,aAAO,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,IAAI,KAAK,IAAI;AAAA,UACb,IAAI,KAAK,IAAI;AAAA,QACd;AAAA,QACA,UAAU,CAAC,MAAM;AAChB,cAAI,EAAE,SAAS;AACd,kBAAM,gBAAgB,EAAE;AAExB,0BAAc,cAAc,OAAO;AACnC,0BAAc,kBAAkB,OAAO;AACvC,0BAAc,kBAAkB,OAAO;AACvC,0BAAc,eAAe,OAAO;AACpC,0BAAc,iBAAiB,OAAO;AAEtC,gBAAI,cAAc,cAAc,cAAc,WAAW,SAAS,GAAG;AACpE,4BAAc,WAAW,GAAG,mBAAmB,OAAO;AAAA,YACvD;AAEA,mBAAO,KAAK;AAAA,cACX,QAAQ;AAAA,cACR,MAAM;AAAA,gBACL,KAAK;AAAA,cACN;AAAA,cACA,UAAU,CAAC,WAAW;AACrB,oBAAI,OAAO,SAAS;AACnB,yBAAO,KAAK;AAAA,oBACX,QAAQ;AAAA,oBACR,MAAM;AAAA,sBACL,KAAK,OAAO;AAAA,oBACb;AAAA,oBACA,UAAU,CAAC,aAAa;AACvB,0BAAI,SAAS,SAAS;AACrB,+BAAO,WAAW;AAAA,0BACjB,SAAS,GAAG,wDAAwD,CAAC,SAAS,QAAQ,IAAI,CAAC;AAAA,0BAC3F,WAAW;AAAA,wBACZ,CAAC;AAED,6BAAK,OAAO,aAAa;AAAA,sBAC1B;AAAA,oBACD;AAAA,kBACD,CAAC;AAAA,gBACF;AAAA,cACD;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,gBAAgB;AACf,YAAM,eAAe;AAErB,aAAO,MAAM;AAAA,QACZ,KAAK,IAAI;AAAA,QACT,KAAK,IAAI;AAAA,QACT;AAAA,QACA,KAAK,IAAI;AAAA,QACT,KAAK,IAAI,YAAY,OAAO,KAAK;AAAA,MAClC;AAAA,IACD;AAAA,IAGA,mBAAmB;AAClB,YAAM,aAAa,OAAO,MAAM,OAAO,IAAI,WAAM;AACjD,WAAK,mBAAmB,KAAK,YAAY,EAAE,KAAK,SAAS,GAAG,cAAc;AAC1E,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM,KAAK,mBAAmB,KAAK,YAAY,EAAE,MAAM;AAAA,QAC/D,WAAW,MACV,KAAK,WAAW,GAAG,UAAU,KAAK,KAAK,mBAAmB,KAAK,YAAY,EAAE,GAAG,UAAU;AAAA,QAC3F,aAAa,GAAG,eAAe;AAAA,QAC/B,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AACD,WAAK,mBAAmB,KAAK,UAAU,EAAE,KAAK,SAAS,GAAG,kBAAkB;AAC5E,aAAO,GAAG,KAAK,GAAG,cAAc,MAAM;AACrC,cAAM,qBAAqB,KAAK,WAAW,GAAG,UAAU;AACxD,YAAI,sBAAsB,KAAK,mBAAmB,KAAK,UAAU,EAAE,GAAG,UAAU,GAAG;AAClF,eAAK,mBAAmB,KAAK,UAAU,EAAE,MAAM;AAAA,QAChD;AAAA,MACD,CAAC;AACD,WAAK,mBAAmB,KAAK,WAAW,EAAE,KAAK,SAAS,GAAG,cAAc;AACzE,aAAO,GAAG,KAAK,aAAa;AAAA,QAC3B,UAAU;AAAA,QACV,QAAQ,MAAM,KAAK,mBAAmB,KAAK,WAAW,EAAE,MAAM;AAAA,QAC9D,WAAW,MACV,KAAK,WAAW,GAAG,UAAU,KAAK,KAAK,mBAAmB,KAAK,WAAW,EAAE,GAAG,UAAU;AAAA,QAC1F,aAAa,GAAG,cAAc;AAAA,QAC9B,MAAM,SAAS,KAAK;AAAA,MACrB,CAAC;AAAA,IACF;AAAA,IAEA,aAAa;AACZ,YAAM,MAAM,KAAK,OAAO,QAAQ;AAChC,YAAM,aAAa,KAAK,aAAa,WAAW,EAAE;AAClD,YAAM,UAAU,KAAK,aAAa,WAAW,EAAE;AAC/C,YAAM,MAAM,KAAK,OAAO,IAAI;AAC5B,YAAM,eAAe;AAErB,aAAO,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,UACL;AAAA,UACA,SAAS,GAAG,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI;AAAA,UACxC,SAAS,UAAU,UAAU,GAAG,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI;AAAA,UAC5D,SAAS,IAAI;AAAA,UACb,MAAM,IAAI;AAAA,UACV,YAAY;AAAA,UACZ;AAAA,UACA,kBAAkB,OAAO,KAAK,UAAU;AAAA,UACxC,OAAO,IAAI;AAAA,QACZ;AAAA,QACA,UAAU,CAAC,MAAM;AAChB,cAAI,CAAC,EAAE,KAAK;AACX,mBAAO,MAAM,WAAW,OAAO;AAC/B,gBAAI,EAAE,QAAQ,uBAAuB;AACpC,qBAAO;AAAA,gBACN,GAAG,mDAAmD;AAAA,kBACrD,OAAO,MAAM,YAAY,EAAE,QAAQ,qBAAqB;AAAA,gBACzD,CAAC;AAAA,cACF;AAAA,YACD,OAAO;AACN,qBAAO,WAAW;AAAA,gBACjB,SAAS,GAAG,0BAA0B;AAAA,gBACtC,WAAW;AAAA,cACZ,CAAC;AAAA,YACF;AACA,iBAAK,aAAa,KAAK;AAAA,UACxB,OAAO;AACN,mBAAO,SAAS,GAAG,0DAA0D,CAAC;AAAA,UAC/E;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,iBAAiB,KAAK;AACrB,WAAK,cAAc,KAAK,EAAE;AAC1B,UAAI,QAAQ,CAAC,MAAM;AAClB,YAAI,EAAE,WAAW;AAChB,YAAE,aAAa,QAAQ,CAAC,MAAM;AAC7B,kBAAM,aAAa,EAAE,MAAM,GAAG,EAAE,GAAG,YAAY;AAC/C,kBAAM,MAAM,GAAG,CAAC;AAChB,iBAAK,cAAc;AAAA,cAClB,2CAA2C,mBAAmB;AAAA,YAC/D;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AACD,WAAK,cAAc,SAAS,EAAE,KAAK,EAAE,YAAY,MAAM;AAGvD,cAAQ,IAAI,kBAAkB,GAAG;AACjC,cAAQ,IAAI,iBAAiB,KAAK,cAAc,KAAK,CAAC;AAAA,IACvD;AAAA,IAEA,2BAA2B,MAAM;AAChC,UAAI,MAAM;AACT,aAAK,iBAAiB,IAAI,WAAW,MAAM;AAC3C,aAAK,WAAW,KAAK,yBAAyB,EAAE,IAAI,WAAW,MAAM;AAAA,MACtE,OAAO;AACN,aAAK,iBAAiB,IAAI,WAAW,MAAM;AAC3C,aAAK,WAAW,KAAK,yBAAyB,EAAE,IAAI,WAAW,MAAM;AAAA,MACtE;AAAA,IACD;AAAA,IAEA,sBAAsB,kBAAkB;AACvC,UAAI,kBAAkB;AACrB,eAAO,CAAC;AAAA,UACP,WAAW;AAAA,UACX,cAAc,CAAC,iBAAiB,iBAAiB,iBAAiB,WAAW;AAAA,QAC9E,CAAC;AAAA,MACF;AAEA,aAAO;AAAA,QACN;AAAA,UACC,WAAW,KAAK,IAAI,cAAc;AAAA,UAClC,cAAc,CAAC,cAAc,cAAc;AAAA,QAC5C;AAAA,QACA;AAAA,UACC,WAAW,CAAC,KAAK,IAAI,aAAa,KAAK,IAAI,cAAc,KAAK,CAAC,eAAe,QAAQ,EAAE,SAAS,KAAK,IAAI,MAAM;AAAA,UAChH,cAAc,CAAC,gBAAgB,iBAAiB,iBAAiB,eAAe;AAAA,QACjF;AAAA,QACA;AAAA,UACC,WAAW,CAAC,KAAK,IAAI,aAAa,KAAK,IAAI,cAAc,KAAK,CAAC,CAAC,eAAe,QAAQ,EAAE,SAAS,KAAK,IAAI,MAAM;AAAA,UACjH,cAAc,CAAC,iBAAiB,iBAAiB,UAAU,eAAe;AAAA,QAC3E;AAAA,QACA;AAAA,UACC,WAAW,KAAK,IAAI,aAAa,KAAK,IAAI,cAAc;AAAA,UACxD,cAAc,CAAC,iBAAiB,iBAAiB,eAAe;AAAA,QACjE;AAAA,MACD;AAAA,IACD;AAAA,IAEA,gBAAgB,KAAK,mBAAmB,OAAO;AAC9C,yBACG,KAAK,WAAW,IAAI,eAAe,mBAAmB,IACtD,KAAK,WAAW,IAAI,eAAe,iBAAiB;AAEvD,WAAK,2BAA2B,KAAK;AAErC,WAAK,MAAM;AAGX,cAAQ,IAAI,4BAA4B,IAAI,MAAM,WAAW,IAAI,WAAW,qBAAqB,gBAAgB;AAEjH,WAAK,qBAAqB,GAAG;AAC7B,WAAK,kBAAkB,GAAG;AAC1B,WAAK,mBAAmB,GAAG;AAC3B,WAAK,qBAAqB,GAAG;AAE7B,YAAM,qBAAqB,KAAK,sBAAsB,gBAAgB;AACtE,WAAK,iBAAiB,kBAAkB;AAGxC,WAAK,wBAAwB;AAE7B,UAAI,oBAAoB,KAAK,iCAAiC;AAC7D,aAAK,cAAc;AAAA,MACpB;AAAA,IACD;AAAA,IAEA,0BAA0B;AAE1B,aAAO,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,SAAS;AAAA,UACT,MAAM,KAAK,IAAI;AAAA,UACf,WAAW;AAAA,QACZ;AAAA,QACA,UAAU,CAAC,MAAM;AAChB,cAAI,EAAE,WAAW,EAAE,QAAQ,oBAAoB;AAE9C,iBAAK,IAAI,qBAAqB,EAAE,QAAQ;AAAA,UACzC;AAEA,eAAK,uBAAuB;AAAA,QAC7B;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAEA,qBAAqB,KAAK;AACzB,aAAO,GAAG,UAAU,YAAY,KAAK,IAAI,UAAU,UAAU,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM;AACpF,aAAK,iBAAiB,QAAQ,YAAY;AAC1C,cAAM,oBAAoB,KAAK,uBAAuB,GAAG;AACzD,aAAK,eAAe,KAAK,iBAAiB;AAAA,MAC3C,CAAC;AAAA,IACF;AAAA,IAEA,kBAAkB,KAAK;AACtB,WAAK,iBAAiB,KAAK,EAAE;AAC7B,UAAI,MAAM,QAAQ,CAAC,SAAS;AAC3B,cAAM,WAAW,KAAK,cAAc,KAAK,IAAI;AAC7C,aAAK,iBAAiB,OAAO,QAAQ;AACrC,aAAK,8BAA8B;AAAA,MACpC,CAAC;AAAA,IACF;AAAA,IAEA,gCAAgC;AAC/B,YAAM,YAAY,MAAM,KAAK,KAAK,iBAAiB,KAAK,iBAAiB,CAAC;AAC1E,WAAK,iBAAiB,KAAK,iBAAiB,EAAE,IAAI,SAAS,EAAE;AAC7D,UAAI,YAAY,UAAU,OAAO,CAACC,YAAW,QAAQ;AACpD,YAAI,EAAE,GAAG,EAAE,MAAM,IAAIA;AAAW,UAAAA,aAAY,EAAE,GAAG,EAAE,MAAM;AACzD,eAAOA;AAAA,MACR,GAAG,CAAC;AAEJ,mBAAa;AACb,UAAI,aAAa;AAAG,oBAAY;AAEhC,WAAK,iBAAiB,KAAK,iBAAiB,EAAE,IAAI,SAAS,SAAS;AAAA,IACrE;AAAA,IAEA,qBAAqB,KAAK;AACzB,WAAK,mBAAmB,KAAK,EAAE;AAE/B,UAAI,IAAI,YAAY,IAAI,SAAS,QAAQ;AACxC,YAAI,SAAS,QAAQ,CAAC,MAAM;AAC3B,cAAI,EAAE,QAAQ;AACb,kBAAM,cAAc,KAAK,iBAAiB,KAAK,CAAC;AAChD,iBAAK,mBAAmB,OAAO,WAAW;AAAA,UAC3C;AAAA,QACD,CAAC;AAAA,MACF,WAAW,IAAI,YAAY,IAAI,SAAS,QAAQ;AAC/C,YAAI,SAAS,QAAQ,CAAC,YAAY;AACjC,cAAI,QAAQ,kBAAkB;AAC7B,kBAAM,cAAc,KAAK,iBAAiB,KAAK;AAAA,cAC9C,iBAAiB,QAAQ,iBAAiB,QAAQ,QAAQ;AAAA,cAC1D,QAAQ,QAAQ;AAAA,YACjB,CAAC;AACD,iBAAK,mBAAmB,OAAO,WAAW;AAAA,UAC3C;AAAA,QACD,CAAC;AAAA,MACF;AAEA,UAAI,IAAI,yBAAyB,IAAI,gBAAgB;AACpD,cAAM,cAAc,KAAK,iBAAiB,KAAK;AAAA,UAC9C,iBAAiB;AAAA,UACjB,QAAQ,IAAI;AAAA,QACb,CAAC;AACD,aAAK,mBAAmB,OAAO,WAAW;AAAA,MAC3C;AAEA,UAAI,IAAI,WAAW,iBAAiB,IAAI,WAAW,YAAY,IAAI,qBAAqB,GAAG;AAC1F,cAAM,kBAAkB;AAAA,+BACI,GAAG,oBAAoB;AAAA,+BACvB,gBAAgB,IAAI,oBAAoB,IAAI,QAAQ;AAAA;AAEhF,aAAK,mBAAmB,OAAO,eAAe;AAAA,MAC/C;AAAA,IACD;AAAA,IAEA,mBAAmB,KAAK;AACvB,WAAK,kBAAkB,KAAK,EAAE;AAE9B,YAAM,gBAAgB,KAAK,mBAAmB,GAAG;AACjD,YAAM,YAAY,KAAK,eAAe,GAAG;AACzC,YAAM,eAAe,KAAK,kBAAkB,GAAG;AAC/C,YAAM,kBAAkB,KAAK,qBAAqB,GAAG;AACrD,WAAK,kBAAkB,OAAO,aAAa;AAC3C,WAAK,kBAAkB,OAAO,SAAS;AACvC,WAAK,kBAAkB,OAAO,YAAY;AAC1C,WAAK,kBAAkB,OAAO,eAAe;AAAA,IAC9C;AAAA,IAEA,2BAA2B;AAC1B,WAAK,2BAA2B,IAAI;AAAA,IACrC;AAAA,IAEA,iBAAiB,MAAM;AACtB,aAAO,KAAK,WAAW,IAAI,WAAW,MAAM,IAAI,KAAK,WAAW,IAAI,WAAW,MAAM;AAAA,IACtF;AAAA,EACD;;;AC/wBA,UAAQ,iBAAiB,QAAQ,kBAAkB,CAAC;AAEpD,UAAQ,eAAe,aAAa,MAAM;AAAA,IACzC,YAAY,SAAS;AACpB,WAAK,UAAU,EAAE,OAAO,EAAE,KAAK,sBAAsB;AACrD,WAAK,OAAO,QAAQ;AACpB,WAAK,oBAAoB;AAAA,IAC1B;AAAA,IAGA,sBAAsB;AACrB,aAAO,OAAO,KAAK,wEAAwE;AAAA,QAC1F,MAAM,OAAO,QAAQ;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,IAEA,sBAAsB;AACrB,WAAK,oBAAoB,EAAE,KAAK,CAAC,MAAM;AACtC,YAAI,EAAE,QAAQ,QAAQ;AACrB,eAAK,qBAAqB,EAAE,QAAQ,EAAE;AAAA,QACvC,OAAO;AACN,eAAK,uBAAuB;AAAA,QAC7B;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,MAAM,yBAAyB;AAC/B,YAAM,KAAK;AAEX,YAAM,yBAAyB,YAAY;AAC1C,YAAI;AACH,gBAAM,SAAS,MAAM,OAAO,KAAK;AAAA,YAChC,QAAQ;AAAA,YACR,MAAM;AAAA,cACL,SAAS;AAAA,cACT,SAAS;AAAA,gBACR,MAAM,OAAO,QAAQ;AAAA,gBACrB,WAAW;AAAA,gBACX,QAAQ;AAAA,cACT;AAAA,cACA,QAAQ,CAAC,QAAQ,eAAe,SAAS;AAAA,cACzC,OAAO;AAAA,cACP,UAAU;AAAA,YACX;AAAA,UACD,CAAC;AAED,iBAAO,OAAO,WAAW,OAAO,QAAQ,SAAS,IAAI,OAAO,QAAQ,KAAK;AAAA,QAC1E,SAAS,OAAP;AACD,kBAAQ,MAAM,8CAA8C,KAAK;AACjE,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,YAAM,mBAAmB,MAAM,uBAAuB;AAEtD,UAAI,kBAAkB;AACrB,eAAO,WAAW;AAAA,UACjB,SAAS,GAAG,yCAAyC,CAAC,iBAAiB,IAAI,CAAC;AAAA,UAC5E,WAAW;AAAA,QACZ,CAAC;AAED,WAAG,qBAAqB,gBAAgB;AACxC;AAAA,MACD;AAEA,YAAM,eAAe;AAAA,QACpB;AAAA,UACC,WAAW;AAAA,UACX,WAAW;AAAA,UACX,cAAc;AAAA,UACd,OAAO,GAAG,iBAAiB;AAAA,UAC3B,SAAS;AAAA,UACT,MAAM;AAAA,QACP;AAAA,QACA;AAAA,UACC,WAAW;AAAA,UACX,WAAW;AAAA,UACX,cAAc;AAAA,UACd,OAAO,GAAG,gBAAgB;AAAA,UAC1B,SAAS;AAAA,UACT,UAAU,WAAY;AACrB,mBAAO,YAAY,gBAAgB,GAAG,KAAK,KAAK,CAAC,MAAM;AACtD,kBAAI,EAAE,OAAO,KAAK,IAAI,KAAK;AAC1B,kBAAE,iBAAiB,KAAK;AACxB,uBAAO,YAAY,gBAAgB,KAAK,QAAQ;AAChD,uBAAO;AAAA,cACR;AAAA,YACD,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD;AAIA,YAAM,4BAA4B,MAAM;AACvC,cAAM,cAAc,OAAO,YAAY,YAAY,UAAU;AAC7D,YAAI,CAAC;AAAa;AAClB,eAAO,GAAG,QAAQ,eAAe,WAAW,EAAE,KAAK,CAAC,EAAE,SAAS,MAAM;AACpE,iBAAO,YAAY,gBAAgB,GAAG,OAAO,CAAC;AAC9C,mBAAS,QAAQ,CAAC,QAAQ;AACzB,kBAAM,EAAE,gBAAgB,IAAI;AAC5B,mBAAO,YAAY,gBAAgB,GAAG,KAAK,KAAK,EAAE,iBAAiB,gBAAgB,IAAI,CAAC;AAAA,UACzF,CAAC;AACD,iBAAO,YAAY,gBAAgB,KAAK,QAAQ;AAAA,QACjD,CAAC;AAAA,MACF;AAEA,YAAM,SAAS,IAAI,OAAO,GAAG,OAAO;AAAA,QACnC,OAAO,GAAG,0BAA0B;AAAA,QACpC,QAAQ;AAAA,QACR,QAAQ;AAAA,UACP;AAAA,YACC,WAAW;AAAA,YACX,OAAO,GAAG,SAAS;AAAA,YACnB,SAAS,OAAO,SAAS,YAAY,SAAS;AAAA,YAC9C,SAAS;AAAA,YACT,WAAW;AAAA,YACX,MAAM;AAAA,UACP;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,OAAO,GAAG,aAAa;AAAA,YACvB,SAAS;AAAA,YACT,WAAW;AAAA,YACX,MAAM;AAAA,YACN,WAAW,MAAM,kBAAkB;AAAA,YACnC,UAAU,MAAM,0BAA0B;AAAA,UAC3C;AAAA,UACA;AAAA,YACC,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,GAAG,yBAAyB;AAAA,YACnC,iBAAiB;AAAA,YACjB,eAAe;AAAA,YACf,MAAM;AAAA,YACN,MAAM,CAAC;AAAA,YACP,QAAQ;AAAA,UACT;AAAA,QACD;AAAA,QACA,gBAAgB,eAAgB,EAAE,SAAS,aAAa,gBAAgB,GAAG;AAC1E,cAAI,CAAC,gBAAgB,QAAQ;AAC5B,mBAAO,WAAW;AAAA,cACjB,SAAS,GAAG,0DAA0D;AAAA,cACtE,WAAW;AAAA,YACZ,CAAC;AACD,mBAAO,OAAO,MAAM,WAAW,OAAO;AAAA,UACvC;AAEA,4BAAkB,gBAAgB,OAAO,CAAC,MAAM,EAAE,eAAe;AAEjE,gBAAM,SAAS;AACf,gBAAM,MAAM,MAAM,OAAO,KAAK;AAAA,YAC7B;AAAA,YACA,MAAM,EAAE,aAAa,SAAS,gBAAgB;AAAA,YAC9C,QAAQ;AAAA,UACT,CAAC;AACD,WAAC,IAAI,OAAO,GAAG,qBAAqB,IAAI,OAAO;AAC/C,iBAAO,KAAK;AAAA,QACb;AAAA,QACA,sBAAsB,GAAG,QAAQ;AAAA,MAClC,CAAC;AAED,aAAO,KAAK;AAEZ,YAAM,oBAAoB,MAAM;AAC/B,eAAO;AAAA,UACN,OAAO;AAAA,UACP,SAAS,EAAE,SAAS,OAAO,YAAY,QAAQ,UAAU,EAAE;AAAA,QAC5D;AAAA,MACD;AAAA,IACD;AAAA,IAEC,MAAM,qBAAqB,MAAM;AAChC,WAAK,cAAc,KAAK;AACxB,WAAK,UAAU,KAAK;AACpB,WAAK,cAAc,KAAK;AACxB,WAAK,mBAAmB,KAAK;AAC7B,WAAK,iBAAiB,CAAC;AACvB,WAAK,WAAW,CAAC;AAEjB,aAAO,GAAG,UAAU,kBAAkB,QAAW,sBAAsB,EAAE,KAAK,CAAC,EAAE,QAAQ,MAAM;AAC9F,aAAK,uBAAuB,IAAI,QAAQ,oBAAoB,KAAK;AAAA,MAClE,CAAC;AAED,aAAO,KAAK;AAAA,QACX,QAAQ;AAAA,QACR,MAAM,EAAE,aAAa,KAAK,YAAY;AAAA,QACtC,UAAU,CAAC,QAAQ;AAClB,gBAAM,UAAU,IAAI;AACpB,iBAAO,OAAO,KAAK,UAAU,OAAO;AACpC,eAAK,SAAS,kBAAkB,QAAQ,gBAAgB,IAAI,CAAC,UAAU,MAAM,IAAI;AACjF,eAAK,SAAS;AAAA,QACf;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,2BAA2B;AAC1B,WAAK,KAAK;AAAA,QACT;AAAA,8DAC2D,KAAK;AAAA,iBAClD,OAAO,SAAS,YAAY,KAAK,gBAAgB;AAAA;AAAA;AAAA,MAGhE;AAAA,IACD;AAAA,IAEA,WAAW;AACV,WAAK,YAAY;AACjB,WAAK,mBAAmB;AACxB,WAAK,aAAa;AAClB,WAAK,uBAAuB;AAC5B,WAAK,iBAAiB;AAAA,IACvB;AAAA,IAEA,cAAc;AACb,WAAK,QAAQ,OAAO,uCAAuC;AAC3D,WAAK,sBAAsB,KAAK,QAAQ,KAAK,oBAAoB;AAAA,IAClE;AAAA,IAEA,qBAAqB;AACpB,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;AACvB,WAAK,eAAe;AACpB,WAAK,cAAc;AACnB,WAAK,uBAAuB;AAC5B,WAAK,mBAAmB;AAAA,IACzB;AAAA,IAEA,eAAe;AACd,WAAK,KAAK,WAAW;AAErB,WAAK,KAAK,cAAc,GAAG,gBAAgB,GAAG,KAAK,eAAe,KAAK,IAAI,GAAG,OAAO,QAAQ;AAE7F,WAAK,KAAK;AAAA,QACT,GAAG,sBAAsB;AAAA,QACzB,KAAK,oBAAoB,KAAK,IAAI;AAAA,QAClC;AAAA,QACA;AAAA,MACD;AAEA,WAAK,KAAK,cAAc,GAAG,eAAe,GAAG,KAAK,mBAAmB,KAAK,IAAI,GAAG,OAAO,QAAQ;AAEhG,WAAK,KAAK,cAAc,GAAG,eAAe,GAAG,KAAK,UAAU,KAAK,IAAI,GAAG,OAAO,cAAc;AAAA,IAC9F;AAAA,IAEA,yBAAyB;AACxB,WAAK,KAAK,aAAa,KAAK,iBAAiB,EAAE,MAAM;AACrD,WAAK,+BAA+B;AACpC,WAAK,uBAAuB;AAE5B,WAAK,KAAK,WAAW,GAAG,aAAa,GAAG,MAAM;AAAA,QAC7C,WAAW;AAAA,MACZ,CAAC;AAED,WAAK,uBAAuB;AAAA,IAC7B;AAAA,IAEA,yBAAyB;AACxB,WAAK,KAAK,QAAQ,KAAK,kBAAkB,EAAE,OAAO;AAElD,YAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcvB,YAAM,aAAa,KAAK,KAAK,QAAQ,KAAK,gBAAgB;AAC1D,UAAI,WAAW,QAAQ;AACtB,mBAAW,MAAM,cAAc;AAAA,MAChC,OAAO;AACN,aAAK,KAAK,aAAa,OAAO,cAAc;AAAA,MAC7C;AAEA,QAAE,mBAAmB,EAAE,GAAG,SAAS,MAAM;AACxC,aAAK,uBAAuB;AAC5B,eAAO,SAAS,OAAO;AAAA,MAKxB,CAAC;AAED,QAAE,mBAAmB,EAAE,GAAG,SAAS,MAAM;AACxC,aAAK,uBAAuB;AAE5B,eAAO,SAAS,OAAO;AAAA,MAExB,CAAC;AAED,QAAE,mBAAmB,EAAE,GAAG,SAAS,MAAM;AACxC,aAAK,uBAAuB;AAE5B,eAAO,SAAS,OAAO;AAAA,MAExB,CAAC;AAED,WAAK,sBAAsB;AAAA,IAC5B;AAAA,IAEA,yBAAyB;AACxB,qBAAe,QAAQ,sBAAsB,MAAM;AACnD,qBAAe,QAAQ,mBAAmB,KAAK,IAAI,EAAE,SAAS,CAAC;AAAA,IAChE;AAAA,IAEA,wBAAwB;AACvB,YAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiElB,QAAE,wBAAwB,EAAE,OAAO;AAEnC,QAAE,MAAM,EAAE,OAAO,mCAAmC,iBAAiB;AAAA,IACtE;AAAA,IAEA,iCAAiC;AACjC,WAAK,KAAK,QAAQ,KAAK,qBAAqB,EAAE,OAAO;AAErD,YAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAqBtB,GAAG,aAAa;AAAA;AAAA;AAAA,OAGhB,GAAG,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQf,WAAK,KAAK,QAAQ,QAAQ,mBAAmB;AAE7C,QAAE,gBAAgB,EAAE,GAAG,SAAS,WAAY;AAC3C,uBAAe,WAAW,oBAAoB;AAC9C,uBAAe,WAAW,iBAAiB;AAC3C,eAAO,SAAS,OAAO;AAAA,MACxB,CAAC;AAGD,WAAK,sBAAsB;AAE3B,WAAK,oBAAoB;AAEzB,WAAK,KAAK,QAAQ,GAAG,SAAS,mBAAmB,CAAC,MAAM;AACvD,cAAM,SAAS,EAAE;AACjB,cAAM,SAAS,EAAE,MAAM,EAAE,KAAK,QAAQ;AACtC,aAAK,2BAA2B,QAAQ,MAAM;AAAA,MAC/C,CAAC;AAAA,IAED;AAAA,IAEA,wBAAwB;AAExB,YAAM,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiPf,QAAE,oBAAoB,EAAE,OAAO;AAG/B,QAAE,MAAM,EAAE,OAAO,+BAA+B,cAAc;AAAA,IAC9D;AAAA,IAGA,yBAAyB;AACxB,WAAK,kBAAkB,KAAK,KAAK,aAAa,KAAK,iBAAiB;AAEpE,WAAK,gBAAgB,GAAG,SAAS,WAAY;AAC5C,YAAI,CAAC,SAAS,mBAAmB;AAChC,mBAAS,gBAAgB,kBAAkB;AAAA,QAC5C,WAAW,SAAS,gBAAgB;AACnC,mBAAS,eAAe;AAAA,QACzB;AAAA,MACD,CAAC;AAED,QAAE,QAAQ,EAAE,GAAG,oBAAoB,KAAK,+BAA+B,KAAK,IAAI,CAAC;AAAA,IAClF;AAAA,IAEA,iCAAiC;AAChC,UAAI,0BAA0B,GAAG,aAAa;AAC9C,UAAI,wBAAwB,GAAG,kBAAkB;AAEjD,UAAI,SAAS,mBAAmB;AAC/B,aAAK,gBAAgB,GAAG,YAAY;AAAA,MACrC,OAAO;AACN,aAAK,gBAAgB,GAAG,YAAY;AAAA,MACrC;AAAA,IACD;AAAA,IAEA,iBAAiB;AAChB,aAAO,MAAM,KAAK,KAAK,IAAI,GAAG;AAC9B,aAAO,UAAU,QAAQ,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,IAAI,IAAI;AAAA,IACjE;AAAA,IAEA,2BAA2B,QAAQ,QAAQ;AAC1C,cAAQ,IAAI,0BAA0B,QAAQ;AAG9C,WAAK,qBAAqB,MAAM;AAGhC,UAAI,KAAK,sBAAsB,QAAQ;AACtC,aAAK,yBAAyB,KAAK;AACnC,aAAK,oBAAoB;AACzB,aAAK,4BAA4B;AACjC;AAAA,MACD;AAGA,WAAK,oBAAoB;AAGzB,WAAK,gCAAgC,MAAM;AAAA,IAC5C;AAAA,IAGA,qBAAqB,cAAc;AAElC,WAAK,KAAK,QAAQ,KAAK,iBAAiB,EACtC,YAAY,aAAa,EACzB,SAAS,aAAa;AAGxB,QAAE,YAAY,EAAE,YAAY,aAAa,EAAE,SAAS,aAAa;AAAA,IAClE;AAAA,IAGA,gCAAgC,QAAQ;AACvC,cAAQ,IAAI,sCAAsC,QAAQ;AAG1D,UAAI,CAAC,KAAK,mBAAmB;AAC5B,gBAAQ,MAAM,mCAAmC;AACjD;AAAA,MACD;AAGA,WAAK,kBAAkB,KAAK;AAG5B,WAAK,kBAAkB,iBAAiB,IAAI;AAC5C,WAAK,cAAc,iBAAiB,IAAI;AAGxC,iBAAW,MAAM;AAChB,YAAI,KAAK,kBAAkB,WAAW,GAAG,UAAU,GAAG;AACrD,eAAK,kBAAkB,uBAAuB,MAAM;AAAA,QACrD,OAAO;AACN,kBAAQ,MAAM,sDAAsD;AAAA,QACrE;AAAA,MACD,GAAG,GAAG;AAAA,IACP;AAAA,IAEA,sBAAsB;AACrB,YAAM,OAAO,KAAK,kBAAkB,WAAW,GAAG,SAAS;AAE3D,UAAI,CAAC,MAAM;AAEV,aAAK,oBAAoB;AACzB,aAAK,4BAA4B;AAEjC,aAAK,kBAAkB,cAAc;AAAA,MACtC;AAEA,WAAK,yBAAyB,IAAI;AAAA,IACnC;AAAA,IAEA,qBAAqB;AACpB,UAAI,CAAC,KAAK,oBAAoB,GAAG,UAAU;AAAG;AAE9C,UAAI,KAAK,IAAI,IAAI,MAAM,UAAU,GAAG;AACnC,eAAO,WAAW;AAAA,UACjB,SAAS,GAAG,oDAAoD;AAAA,UAChE,WAAW;AAAA,QACZ,CAAC;AACD,eAAO,MAAM,WAAW,OAAO;AAC/B;AAAA,MACD;AAEA,WAAK,IACH,KAAK,QAAW,QAAW,QAAW,MAAM;AAC5C,eAAO,WAAW;AAAA,UACjB,SAAS,GAAG,yCAAyC;AAAA,UACrD,WAAW;AAAA,QACZ,CAAC;AACD,eAAO,MAAM,WAAW,OAAO;AAAA,MAChC,CAAC,EACA,KAAK,MAAM;AACX,eAAO,aAAa;AAAA,UACnB,MAAM,OAAO,IAAI,OAAO;AAAA,UACxB,MAAM,KAAK,iBAAiB;AAAA,UAC5B,MAAM,OAAO,IAAI,SAAS;AAAA,QAC3B,CAAC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,YAAY;AACX,UAAI,CAAC,KAAK,oBAAoB,GAAG,UAAU;AAAG;AAE9C,UAAI,UAAU,OAAO,MAAM,YAAY,mBAAmB;AAC1D,cAAQ,cAAc,KAAK,IAAI,IAAI;AACnC,cAAQ,OAAO,OAAO,QAAQ;AAC9B,cAAQ,UAAU,KAAK,IAAI,IAAI;AAC/B,cAAQ,oBAAoB,KAAK;AACjC,cAAQ,kBAAkB,OAAO,SAAS,aAAa;AACvD,cAAQ,eAAe,OAAO,SAAS,SAAS;AAChD,cAAQ,eAAe,OAAO,SAAS,SAAS;AAChD,aAAO,UAAU,QAAQ,qBAAqB,QAAQ,IAAI;AAAA,IAC3D;AAAA,IAEA,qBAAqB;AAEpB,UAAI,CAAC,QAAQ,eAAe,cAAc;AACzC,gBAAQ,MAAM,8BAA8B;AAC5C;AAAA,MACD;AAEA,WAAK,gBAAgB,IAAI,QAAQ,eAAe,aAAa;AAAA,QAC5D,SAAS,KAAK;AAAA,QACd,aAAa,KAAK;AAAA,QAClB,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,UACP,eAAe,CAAC,SAAS,KAAK,eAAe,IAAI;AAAA,UACjD,SAAS,MAAM,KAAK,OAAO,CAAC;AAAA,QAC7B;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,iBAAiB;AAEhB,UAAI,CAAC,QAAQ,eAAe,UAAU;AACrC,gBAAQ,MAAM,0BAA0B;AACxC;AAAA,MACD;AAEA,WAAK,OAAO,IAAI,QAAQ,eAAe,SAAS;AAAA,QAC/C,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,UACP,SAAS,MAAM,KAAK;AAAA,UAEpB,mBAAmB,CAAC,SAAS;AAC5B,kBAAM,WAAW,KAAK,kBAAkB,IAAI;AAC5C,iBAAK,aAAa,4BAA4B,QAAQ;AAAA,UACvD;AAAA,UAEA,cAAc,CAAC,OAAO,WAAW,KAAK,kBAAkB,OAAO,MAAM;AAAA,UAErE,UAAU,MAAM,KAAK,kBAAkB;AAAA,UAEvC,WAAW,MAAM,KAAK,QAAQ,UAAU;AAAA,UAExC,0BAA0B,CAAC,YAAY;AACtC,iBAAK,cAAc,gBAAgB;AACnC,iBAAK,mBAAmB;AACxB,iBAAK,QAAQ,mCAAmC;AAAA,UACjD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,oBAAoB;AACnB,UAAI,CAAC,QAAQ,eAAe,aAAa;AACxC,gBAAQ,MAAM,6BAA6B;AAC3C;AAAA,MACD;AAEA,WAAK,eAAe,IAAI,QAAQ,eAAe,YAAY;AAAA,QAC1D,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,UACP,SAAS,MAAM,KAAK;AAAA,UAEpB,sBAAsB,CAAC,aAAa;AACnC,iBAAK,cAAc,iBAAiB,CAAC,QAAQ;AAC7C,iBAAK,KAAK,cAAc,QAAQ;AAAA,UACjC;AAAA,UAEA,cAAc,CAAC,MAAM,OAAO,UAAU;AACrC,kBAAM,WAAW,OAAO,MAAM,QAAQ,KAAK,SAAS,KAAK,IAAI;AAC7D,gBAAI,YAAY,SAAS,UAAU,OAAO;AACzC,oBAAM,OAAO;AAAA,gBACZ;AAAA,gBACA;AAAA,gBACA,MAAM,KAAK,aAAa;AAAA,cACzB;AACA,qBAAO,KAAK,eAAe,IAAI;AAAA,YAChC;AAEA,mBAAO,QAAQ,QAAQ;AAAA,UACxB;AAAA,UAEA,qBAAqB,CAAC,SAAS;AAC9B,kBAAM,YAAY,KAAK,KAAK,cAAc,IAAI;AAC9C,iBAAK,KAAK,sBAAsB,SAAS;AAAA,UAC1C;AAAA,UAEA,oBAAoB,CAAC,cAAc;AAClC,iBAAK,KAAK,yBAAyB,SAAS;AAAA,UAC7C;AAAA,UACA,gCAAgC,CAAC,UAAU,UAAU;AACpD,iBAAK,KAAK;AAAA,cACT;AAAA,cACA;AAAA,cACA,KAAK,aAAa;AAAA,YACnB;AAAA,UACD;AAAA,UACA,6BAA6B,CAAC,kBAAkB,SAAS;AAGxD,mBAAO,KAAK,gBAAgB,EAAE,QAAQ,CAAC,UAAU;AAChD,oBAAM,gBAAgB,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,QAAQ,KAAK,IAAI;AACxE,oBAAM,UAAU,KAAK,IAAI,UAAU,SAAS,mBAAK,cAAe;AAEhE,sBAAQ,WAAW;AACnB,sBAAQ,YAAY,iBAAiB,OAAO,KAAK;AAAA,CAAI;AACrD,sBAAQ,MAAM,iBAAiB,OAAO;AACtC,mBAAK,IAAI,IAAI,MAAM,QAAQ,CAAC,QAAQ;AACnC,oBAAI,KAAK,cAAc,IAAI,WAAW;AACrC,uBAAK,iBAAiB,GAAG;AAAA,gBAC1B;AAAA,cACD,CAAC;AAAA,YACF,CAAC;AAAA,UACF;AAAA,UACA,uBAAuB,MAAM,KAAK,sBAAsB;AAAA,UACxD,oBAAoB,MAAM,KAAK;AAAA,UAC/B,oBAAoB,MAAM;AACzB,iBAAK,aAAa,4BAA4B,IAAI;AAClD,iBAAK,KAAK,cAAc;AACxB,iBAAK,KAAK,sBAAsB;AAEhC,iBAAK,cAAc,iBAAiB,IAAI;AAAA,UACzC;AAAA,UACA,qBAAqB,CAAC,WAAW,cAAc,KAAK,oBAAoB,WAAW,SAAS;AAAA,QAC7F;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,gBAAgB;AAEf,UAAI,CAAC,QAAQ,eAAe,SAAS;AACpC,gBAAQ,MAAM,yBAAyB;AACvC;AAAA,MACD;AAEA,WAAK,UAAU,IAAI,QAAQ,eAAe,QAAQ;AAAA,QACjD,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,UACP,SAAS,MAAM,KAAK,OAAO,CAAC;AAAA,UAE5B,sBAAsB,MAAM,KAAK,oBAAoB,CAAC;AAAA,UAEtD,uBAAuB,CAAC,SAAS;AAChC,gBAAI,MAAM;AACT,mBAAK,aAAa,WAAW,GAAG,UAAU,IACvC,KAAK,aAAa,WAAW,IAAI,WAAW,MAAM,IAClD;AACH,mBAAK,cAAc,iBAAiB,KAAK;AAAA,YAC1C,OAAO;AACN,mBAAK,cAAc,iBAAiB,IAAI;AAAA,YACzC;AAAA,UACD;AAAA,UAEA,gBAAgB,MAAM;AACrB,iBAAK,IAAI,WAAW,EAAE,KAAK,CAAC,MAAM;AACjC,mBAAK,kBAAkB,KAAK;AAC5B,mBAAK,cAAc,iBAAiB,IAAI;AACxC,mBAAK,cAAc,gBAAgB,KAAK,IAAI,KAAK,IAAI;AACrD,qBAAO,WAAW;AAAA,gBACjB,WAAW;AAAA,gBACX,SAAS,GAAG,0CAA0C,CAAC,EAAE,IAAI,IAAI,CAAC;AAAA,cACnE,CAAC;AAAA,YACF,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,yBAAyB;AACxB,cAAQ,IAAI,iBAAgB,KAAK,mBAAmB;AACpD,cAAQ,IAAI,YAAW,KAAK,OAAO;AAGnC,UAAI,CAAC,QAAQ,eAAe,eAAe;AAC1C,gBAAQ,MAAM,+BAA+B;AAC7C;AAAA,MACD;AAEA,WAAK,oBAAoB,IAAI,QAAQ,eAAe,cAAc;AAAA,QACjE,SAAS,KAAK;AAAA,QACd,QAAQ;AAAA,UACP,mBAAmB,CAAC,SAAS;AAC5B,mBAAO,GAAG,QAAQ,iBAAiB,IAAI,EAAE,KAAK,CAAC,QAAQ;AACtD,mBAAK,cAAc,gBAAgB,GAAG;AAAA,YACvC,CAAC;AAAA,UACF;AAAA,UACA,eAAe,MAAM,KAAK,cAAc,2BAA2B,IAAI;AAAA,QACxE;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,qBAAqB;AACpB,UAAI,CAAC,QAAQ,eAAe,kBAAkB;AAC7C,gBAAQ,MAAM,kCAAkC;AAChD;AAAA,MACD;AAEA,WAAK,gBAAgB,IAAI,QAAQ,eAAe,iBAAiB;AAAA,QAChE,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf,QAAQ;AAAA,UACP,SAAS,MAAM,KAAK;AAAA,UAEpB,gBAAgB,CAAC,SAAS;AACzB,iBAAK,kBAAkB,iBAAiB,KAAK;AAC7C,mBAAO,GAAG,QAAQ,iBAAiB,IAAI,EAAE,KAAK,CAAC,QAAQ;AACtD,qBAAO,aAAa;AAAA,gBACnB,MAAM,KAAK,oBAAoB,GAAG;AAAA,gBAClC,MAAM,KAAK,KAAK,aAAa;AAAA,gBAC7B,MAAM,KAAK,cAAc,iBAAiB,IAAI;AAAA,cAC/C,CAAC;AAAA,YACF,CAAC;AAAA,UACF;AAAA,UACA,YAAY,CAAC,SAAS;AACrB,iBAAK,kBAAkB,iBAAiB,KAAK;AAC7C,mBAAO,aAAa;AAAA,cACnB,MAAM,KAAK,IAAI,QAAQ,IAAI;AAAA,cAC3B,MAAM,KAAK,KAAK,aAAa;AAAA,cAC7B,MAAM,KAAK,cAAc,iBAAiB,IAAI;AAAA,YAC/C,CAAC;AAAA,UACF;AAAA,UACA,cAAc,CAAC,SAAS;AACvB,mBAAO,MAAM,WAAW,KAAK,IAAI,IAAI,SAAS,MAAM,MAAM;AACzD,mBAAK,kBAAkB,aAAa;AAAA,YACrC,CAAC;AAAA,UACF;AAAA,UACA,WAAW,MAAM;AAChB,mBAAO,aAAa;AAAA,cACnB,MAAM,OAAO,IAAI,OAAO;AAAA,cACxB,MAAM,KAAK,iBAAiB;AAAA,cAC5B,MAAM,KAAK,cAAc,iBAAiB,IAAI;AAAA,cAC9C,MAAM,OAAO,IAAI,SAAS;AAAA,YAC3B,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,8BAA8B;AAC9B,WAAK,KAAK,QAAQ,KAAK,iBAAiB,EACtC,YAAY,aAAa,EACzB,SAAS,aAAa;AAAA,IACxB;AAAA,IAEA,sBAAsB,OAAO,MAAM;AACnC,UAAI,MAAM;AACT,aAAK,KAAK,QAAQ,KAAK,qBAAqB,EAAE,KAAK;AAAA,MACpD,OAAO;AACN,aAAK,KAAK,QAAQ,KAAK,qBAAqB,EAAE,KAAK;AAAA,MACpD;AAAA,IACA;AAAA,IAEA,yBAAyB,MAAM;AAC9B,WAAK,kBAAkB,CAAC,IAAI;AAE5B,UAAI,MAAM;AACT,aAAK,kBAAkB,iBAAiB,IAAI;AAC5C,aAAK,cAAc,iBAAiB,IAAI;AAAA,MACzC,OAAO;AACN,aAAK,kBAAkB,iBAAiB,KAAK;AAC7C,aAAK,cAAc,iBAAiB,KAAK;AAEzC,aAAK,oBAAoB;AACzB,aAAK,4BAA4B;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,kBAAkB,MAAM;AACvB,WAAK,KAAK,iBAAiB,IAAI;AAC/B,WAAK,cAAc,iBAAiB,IAAI;AAGxC,OAAC,OAAO,KAAK,aAAa,iBAAiB,KAAK,KAAK,KAAK,QAAQ,iBAAiB,KAAK,IAAI;AAAA,IAC7F;AAAA,IAEA,sBAAsB;AACtB,WAAK,yBAAyB,KAAK;AACnC,WAAK,oBAAoB;AACzB,WAAK,4BAA4B;AACjC,UAAI,KAAK,mBAAmB;AAC3B,aAAK,kBAAkB,cAAc;AAAA,MACtC;AAAA,IACA;AAAA,IAEA,mBAAmB;AAClB,aAAO,OAAO,aAAa;AAAA,QAC1B,MAAM,OAAO,IAAI,OAAO;AAAA,QACxB,MAAM,KAAK,uBAAuB;AAAA,QAClC,MAAM,KAAK,qBAAqB;AAAA,QAChC,MAAM,KAAK,uBAAuB;AAAA,QAClC,MAAM,KAAK,KAAK,aAAa;AAAA,QAC7B,MAAM,OAAO,IAAI,SAAS;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA,IAGA,yBAAyB;AACxB,YAAM,UAAU;AAChB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,YAAI,KAAK,KAAK;AACb,eAAK,MAAM,KAAK,YAAY,KAAK,GAAG;AACpC,eAAK,IAAI,IAAI,QAAQ,CAAC;AACtB,eAAK,IAAI,IAAI,SAAS;AACtB,kBAAQ;AAAA,QACT,OAAO;AACN,iBAAO,MAAM,aAAa,SAAS,MAAM;AACxC,iBAAK,MAAM,KAAK,YAAY;AAC5B,iBAAK,IAAI,IAAI,QAAQ,CAAC;AACtB,iBAAK,IAAI,IAAI,SAAS;AACtB,oBAAQ;AAAA,UACT,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,YAAY,MAAM;AACjB,YAAM,UAAU;AAChB,YAAM,OAAO,EAAE,OAAO;AACtB,YAAM,MAAM,QAAQ,IAAI,OAAO,GAAG,KAAK,KAAK,SAAS,MAAM,KAAK;AAChE,YAAM,OAAO,OAAO,MAAM,0BAA0B,SAAS,IAAI;AACjE,UAAI,QAAQ,IAAI;AAEhB,aAAO;AAAA,IACR;AAAA,IAEA,MAAM,oBAAoB,KAAK;AAC9B,aAAO,IAAI,OAAO;AAClB,WAAK,MAAM,KAAK,YAAY,KAAK,GAAG;AACpC,WAAK,IAAI,IAAI,QAAQ,CAAC;AACtB,aAAO,OAAO,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,aAAa,IAAI;AAAA,UACjB,YAAY,KAAK,IAAI;AAAA,QACtB;AAAA,QACA,UAAU,CAAC,MAAM;AAChB,iBAAO,MAAM,KAAK,EAAE,OAAO;AAC3B,iBAAO,QAAQ,EAAE,QAAQ,SAAS,EAAE,QAAQ,IAAI,EAAE,sBAAsB;AACxE,eAAK,qBAAqB,EAAE,KAAK,MAAM;AACtC,mBAAO,IAAI,SAAS;AAAA,UACrB,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,uBAAuB;AACtB,UAAI,KAAK,WAAW,CAAC,KAAK,IAAI,IAAI;AAAS,aAAK,IAAI,IAAI,UAAU,KAAK;AACvE,WACE,KAAK,eAAe,CAAC,KAAK,IAAI,IAAI,gBAClC,KAAK,IAAI,IAAI,aAAa,KAAK,eAAe,KAAK,IAAI,IAAI,cAC3D;AACD,aAAK,IAAI,IAAI,cAAc,KAAK;AAAA,MACjC;AAEA,UAAI,CAAC,KAAK,IAAI,IAAI;AAAS;AAE3B,aAAO,KAAK,IAAI,QAAQ,cAAc;AAAA,IACvC;AAAA,IAEA,yBAAyB;AACxB,WAAK,KAAK,cAAc,KAAK,aAAa,MAAM;AAAA,IACjD;AAAA,IAEA,MAAM,eAAe,MAAM;AAC1B,aAAO,IAAI,OAAO;AAClB,UAAI,KAAK,IAAI,IAAI,iBAAiB,KAAK,SAAS;AAC/C,aAAK,IAAI,IAAI,gBAAgB,KAAK,SAAS;AAC5C,UAAI,WAAW;AACf,UAAI;AACH,YAAI,EAAE,OAAO,OAAO,KAAK,IAAI;AAC7B,mBAAW,KAAK,kBAAkB,IAAI;AACtC,cAAM,kBAAkB,CAAC,EAAE,cAAc,QAAQ;AAEjD,cAAM,gBAAgB,UAAU,SAAS,UAAU;AACnD,YAAI;AAAe,kBAAQ,IAAI,SAAS,GAAG,IAAI,IAAI,KAAK;AAExD,YAAI,iBAAiB;AACpB,cAAI,UAAU;AAAO,oBAAQ,IAAI,KAAK;AAEtC,cAAI,CAAC,OAAO,mBAAmB,EAAE,SAAS,KAAK,KAAK,QAAQ,KAAK,CAAC,KAAK,sBAAsB;AAC5F,kBAAM,aACL,UAAU,QAAQ,QAAQ,SAAS,oBAAoB,SAAS,MAAM;AACvE,kBAAM,KAAK,yBAAyB,UAAU,YAAY,KAAK,IAAI,IAAI,aAAa;AAAA,UACrF;AAEA,cAAI,KAAK,6BAA6B,QAAQ,KAAK,eAAe;AACjE,kBAAM,OAAO,MAAM,UAAU,SAAS,SAAS,SAAS,MAAM,OAAO,KAAK;AAC1E,gBAAI,KAAK,aAAa,eAAe;AACpC,oBAAM,OAAO,MAAM;AAAA,gBAClB,SAAS;AAAA,gBACT,SAAS;AAAA,gBACT;AAAA,gBACA,SAAS,YAAY;AAAA,EAAK,KAAK;AAAA,cAChC;AAAA,YACD;AACA,iBAAK,iBAAiB,QAAQ;AAAA,UAC/B;AAAA,QACD,OAAO;AACN,cAAI,CAAC,KAAK,IAAI,IAAI;AAAU,mBAAO,KAAK,+BAA+B;AAEvE,gBAAM,EAAE,WAAW,UAAU,WAAW,MAAM,KAAK,UAAU,IAAI;AAEjE,cAAI,CAAC;AAAW;AAEhB,cAAI,QAAQ,UAAa,QAAQ,GAAG;AACnC,mBAAO,WAAW;AAAA,cACjB,SAAS,GAAG,gCAAgC;AAAA,cAC5C,WAAW;AAAA,YACZ,CAAC;AACD,mBAAO,MAAM,WAAW,OAAO;AAC/B;AAAA,UACD;AACA,gBAAM,WAAW,EAAE,WAAW,UAAU,MAAM,KAAK,CAAC,QAAQ,OAAO,UAAU;AAE7E,cAAI,WAAW;AACd,kBAAM,KAAK,6BAA6B,WAAW,KAAK,IAAI,IAAI,eAAe,SAAS;AACxF,qBAAS,eAAe;AAAA,UACzB;AAEA,mBAAS,6BAA6B;AACtC,cAAI,UAAU;AAAa,qBAAS,SAAS,MAAM,MAAM;AAAA,CAAI,EAAE,UAAU;AAEzE,qBAAW,KAAK,IAAI,UAAU,SAAS,QAAQ;AAE/C,cAAI,UAAU,SAAS,UAAU,KAAK,CAAC,KAAK,sBAAsB;AACjE,kBAAM,aAAa,QAAQ,SAAS;AACpC,kBAAM,KAAK,yBAAyB,UAAU,YAAY,KAAK,IAAI,IAAI,aAAa;AAAA,UACrF;AAEA,gBAAM,KAAK,wBAAwB,QAAQ;AAE3C,eAAK,iBAAiB,QAAQ;AAE9B,cAAI,KAAK,aAAa,WAAW,GAAG,UAAU;AAAG,iBAAK,qBAAqB,QAAQ;AAEnF,cACC,KAAK,oCAAoC,QAAQ,KACjD,CAAC,KAAK,aAAa,WAAW,GAAG,UAAU;AAE3C,iBAAK,qBAAqB,QAAQ;AAAA,QACpC;AAAA,MACD,SAAS,OAAP;AACD,gBAAQ,IAAI,KAAK;AAAA,MAClB,UAAE;AACD,eAAO,IAAI,SAAS;AACpB,eAAO;AAAA,MACR;AAAA,IACD;AAAA,IAEA,iCAAiC;AAChC,aAAO,IAAI,SAAS;AACpB,aAAO,WAAW;AAAA,QACjB,SAAS,GAAG,mDAAmD;AAAA,QAC/D,WAAW;AAAA,MACZ,CAAC;AACD,aAAO,MAAM,WAAW,OAAO;AAAA,IAChC;AAAA,IAEA,kBAAkB,EAAE,MAAM,WAAW,UAAU,KAAK,KAAK,GAAG;AAC3D,UAAI,WAAW;AACf,UAAI,MAAM;AACT,mBAAW,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,QAAQ,IAAI;AAAA,MACzD,OAAO;AAIN,cAAM,eAAe,aAAa,UAAU,aAAa;AACzD,mBAAW,KAAK,IAAI,IAAI,MAAM;AAAA,UAC7B,CAAC,MACA,EAAE,cAAc,cACf,CAAC,gBAAiB,gBAAgB,EAAE,aAAa,aAClD,EAAE,QAAQ,OACV,EAAE,oBAAoB,IAAI,IAAI;AAAA,QAChC;AAAA,MACD;AAEA,aAAO,YAAY,CAAC;AAAA,IACrB;AAAA,IAEA,qBAAqB,UAAU;AAC9B,WAAK,aAAa,4BAA4B,QAAQ;AAAA,IACvD;AAAA,IAEA,6BAA6B,UAAU;AACtC,aAAO,SAAS,QAAQ,KAAK,aAAa,aAAa;AAAA,IACxD;AAAA,IAEA,iBAAiB,UAAU,aAAa;AACvC,WAAK,KAAK,iBAAiB,UAAU,WAAW;AAChD,WAAK,KAAK,sBAAsB,KAAK,GAAG;AAAA,IACzC;AAAA,IAEA,oCAAoC,UAAU;AAG7C,YAAM,aAAa,SAAS;AAC5B,YAAM,UAAU,SAAS;AACzB,YAAM,qBAAqB,CAAC,SAAS;AACrC,YAAM,oBAAoB,CAAC,SAAS;AAEpC,UACE,cAAc,sBACd,WAAW,qBACX,cAAc,YAAY,qBAAqB,qBAC/C;AACD,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR;AAAA,IAEA,MAAM,wBAAwB,UAAU;AACvC,YAAM,KAAK,IAAI,eAAe,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI;AAClF,YAAM,KAAK,IAAI,eAAe,QAAQ,OAAO,SAAS,SAAS,SAAS,IAAI;AAAA,IAC7E;AAAA,IAEA,MAAM,yBAAyB,UAAU,YAAY,WAAW;AAC/D,YAAM,QAAQ,MAAM,KAAK,oBAAoB,SAAS,WAAW,SAAS,GAAG;AAC7E,YAAM,gBAAgB,KAAK;AAC3B,YAAM,gBAAgB,KAAK;AAE3B,aAAO,IAAI,SAAS;AACpB,YAAM,WAAW,SAAS,UAAU,KAAK;AACzC,YAAM,iBAAiB,SAAS,UAAU,KAAK;AAC/C,YAAM,iBAAiB,UAAU,KAAK;AACtC,YAAM,qBAAqB,cAAc,SAAS,EAAE,KAAK;AACzD,UAAI,EAAE,gBAAgB,IAAI;AACzB,YAAI,eAAe;AAClB,iBAAO,MAAM,UAAU,SAAS,SAAS,SAAS,IAAI;AACtD,iBAAO,MAAM;AAAA,YACZ,OAAO,GAAG,eAAe;AAAA,YACzB,SAAS,GAAG,wDAAwD;AAAA,cACnE;AAAA,cACA;AAAA,YACD,CAAC;AAAA,UACF,CAAC;AAAA,QACF,OAAO;AACN;AAAA,QACD;AAAA,MACD,WAAW,iBAAiB,gBAAgB,YAAY;AACvD,eAAO,MAAM;AAAA,UACZ,SAAS;AAAA,YACR;AAAA,YACA,CAAC,gBAAgB,gBAAgB,oBAAoB,QAAQ;AAAA,UAC9D;AAAA,UACA,WAAW;AAAA,QACZ,CAAC;AACD,eAAO,MAAM,WAAW,OAAO;AAAA,MAChC;AACA,aAAO,IAAI,OAAO;AAAA,IACnB;AAAA,IAEA,MAAM,6BAA6B,WAAW,WAAW,WAAW;AACnE,YAAM,SAAS;AACf,YAAM,OAAO,EAAE,SAAS,EAAE,WAAW,UAAU,EAAE;AACjD,YAAM,MAAM,MAAM,OAAO,KAAK,EAAE,QAAQ,KAAK,CAAC;AAE9C,UAAI,IAAI,QAAQ,SAAS,SAAS,GAAG;AACpC,eAAO,MAAM;AAAA,UACZ,OAAO,GAAG,eAAe;AAAA,UACzB,SAAS,GAAG,0EAA0E;AAAA,YACrF,UAAU,KAAK;AAAA,UAChB,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAAA,IACD;AAAA,IAEA,oBAAoB,WAAW,WAAW;AACzC,YAAM,KAAK;AACX,aAAO,OAAO,KAAK;AAAA,QAClB,QAAQ;AAAA,QACR,MAAM;AAAA,UACL;AAAA,UACA;AAAA,QACD;AAAA,QACA,SAAS,KAAK;AACb,cAAI,CAAC,GAAG,eAAe;AAAY,eAAG,eAAe,aAAa,CAAC;AACnE,aAAG,eAAe,WAAW,aAAa,IAAI;AAAA,QAC/C;AAAA,MACD,CAAC;AAAA,IACF;AAAA,IAEA,kBAAkB,OAAO,iBAAiB;AACzC,UAAI,oBAAoB,YAAY;AACnC,aAAK,aAAa,4BAA4B,IAAI;AAAA,MACnD,WAAW,oBAAoB,UAAU;AACxC,aAAK,sBAAsB;AAAA,MAC5B,OAAO;AACN,cAAM,gBAAgB,KAAK,aAAa,GAAG;AAC3C,YAAI,CAAC;AAAe;AACpB,sBAAc,UAAU;AACxB,iBAAS,MAAM,cAAc,UAAU,KAAK;AAAA,MAC7C;AAAA,IACD;AAAA,IAEA,wBAAwB;AACvB,aAAO,IAAI,OAAO;AAClB,YAAM,EAAE,SAAS,MAAM,aAAa,IAAI,KAAK;AAE7C,aAAO,OAAO,MACZ,UAAU,SAAS,MAAM,OAAO,CAAC,EACjC,KAAK,MAAM;AACX,eAAO,MAAM,UAAU,SAAS,IAAI;AACpC,aAAK,iBAAiB,cAAc,IAAI;AACxC,aAAK,aAAa,4BAA4B,IAAI;AAClD,eAAO,IAAI,SAAS;AAAA,MACrB,CAAC,EACA,MAAM,CAAC,MAAM,QAAQ,IAAI,CAAC,CAAC;AAAA,IAC9B;AAAA,IAEA,MAAM,oBAAoB;AACzB,UAAI,KAAK,IAAI,SAAS,GAAG;AACxB,YAAI,aAAa;AACjB,cAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,MAAO,aAAa,IAAK;AAE/D,SAAC,cAAc,KAAK,QAAQ,SAAS;AAErC,sBACC,WAAW,MAAM;AAChB,eAAK,KAAK,oBAAoB,IAAI;AAAA,QACnC,GAAG,GAAG;AAAA,MACR,OAAO;AACN,aAAK,QAAQ,SAAS;AAAA,MACvB;AAAA,IACD;AAAA,EACD;", "names": ["onScan", "max_width", "precision", "onScan", "$btn", "max_width"]}