import{d as c,r as f,v as o,q as e,J as i,K as p,u as n,j as s,I as l,L as u}from"./index-5LHlQTUw.js";const g={class:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50"},v={class:"w-full max-w-md p-8"},m={class:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden"},x={class:"px-8 pb-8"},b={class:"space-y-2"},y={class:"relative"},h=["type"],w={class:"absolute inset-y-0 right-0 flex items-center pr-3"},C={key:0,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},k={key:1,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},_=["disabled"],E={key:0},M={key:1,class:"flex items-center space-x-2"},j=c({__name:"Login",setup(B){const a=f(!1);function d(r){let t=new FormData(r.target);l.login.submit({email:t.get("email"),password:t.get("password")})}return(r,t)=>(s(),o("div",g,[e("div",v,[e("div",m,[t[6]||(t[6]=i('<div class="px-8 pt-8 pb-6 text-center" data-v-25efd296><div class="flex justify-center mb-6" data-v-25efd296><img src="data:image/svg+xml,%3Csvg xmlns=&#39;http://www.w3.org/2000/svg&#39; viewBox=&#39;0 0 400 300&#39;%3E%3Cdefs%3E%3ClinearGradient id=&#39;grad1&#39; x1=&#39;0%25&#39; y1=&#39;0%25&#39; x2=&#39;100%25&#39; y2=&#39;100%25&#39;%3E%3Cstop offset=&#39;0%25&#39; style=&#39;stop-color:%236366f1;stop-opacity:1&#39; /%3E%3Cstop offset=&#39;100%25&#39; style=&#39;stop-color:%238b5cf6;stop-opacity:1&#39; /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath d=&#39;M50 150 C50 90, 90 50, 150 50 C180 50, 200 60, 220 80 C240 100, 250 130, 250 150 C250 210, 210 250, 150 250 C90 250, 50 210, 50 150 Z&#39; fill=&#39;none&#39; stroke=&#39;url(%23grad1)&#39; stroke-width=&#39;8&#39;/%3E%3Cpath d=&#39;M160 80 C180 80, 200 100, 200 120 C200 140, 180 160, 160 160 C140 160, 120 140, 120 120 C120 100, 140 80, 160 80 Z&#39; fill=&#39;url(%23grad1)&#39;/%3E%3Cpath d=&#39;M160 160 L160 200 L140 220 L180 220 Z&#39; fill=&#39;url(%23grad1)&#39;/%3E%3Cpolygon points=&#39;100,70 105,85 120,85 110,95 115,110 100,100 85,110 90,95 80,85 95,85&#39; fill=&#39;%23fbbf24&#39;/%3E%3Cpolygon points=&#39;280,90 284,100 295,100 287,107 291,118 280,112 269,118 273,107 265,100 276,100&#39; fill=&#39;%23fbbf24&#39;/%3E%3Cpolygon points=&#39;320,140 323,148 332,148 326,153 329,162 320,158 311,162 314,153 308,148 317,148&#39; fill=&#39;%23a78bfa&#39;/%3E%3Ctext x=&#39;150&#39; y=&#39;280&#39; font-family=&#39;Arial, sans-serif&#39; font-size=&#39;36&#39; font-weight=&#39;bold&#39; text-anchor=&#39;middle&#39; fill=&#39;url(%23grad1)&#39;%3EMoonStar%3C/text%3E%3Ctext x=&#39;300&#39; y=&#39;260&#39; font-family=&#39;Arial, sans-serif&#39; font-size=&#39;14&#39; text-anchor=&#39;middle&#39; fill=&#39;%236b7280&#39;%3E%3C/text%3E%3C/svg%3E" alt="MoonStar Logo" class="w-24 h-24" data-v-25efd296></div><h1 class="text-2xl font-bold text-gray-800 mb-2" data-v-25efd296> Welcome Back </h1><p class="text-gray-600 text-sm" data-v-25efd296> Sign in to MoonStar </p></div>',1)),e("div",x,[e("form",{class:"space-y-6",onSubmit:p(d,["prevent"])},[t[5]||(t[5]=i('<div class="space-y-2" data-v-25efd296><label class="block text-sm font-medium text-gray-700" data-v-25efd296> User ID </label><div class="relative" data-v-25efd296><input required name="email" type="text" placeholder="<EMAIL>" class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" data-v-25efd296><div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none" data-v-25efd296><svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-25efd296><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-25efd296></path></svg></div></div></div>',1)),e("div",b,[t[3]||(t[3]=e("label",{class:"block text-sm font-medium text-gray-700"}," Password ",-1)),e("div",y,[e("input",{required:"",name:"password",type:a.value?"text":"password",placeholder:"••••••",class:"w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"},null,8,h),e("div",w,[e("button",{type:"button",onClick:t[0]||(t[0]=L=>a.value=!a.value),class:"text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors duration-200"},[a.value?(s(),o("svg",k,t[2]||(t[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)]))):(s(),o("svg",C,t[1]||(t[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))])])])]),e("button",{type:"submit",disabled:n(l).login.loading,class:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:scale-100 shadow-md hover:shadow-lg disabled:cursor-not-allowed flex items-center justify-center space-x-2"},[n(l).login.loading?(s(),o("span",M,t[4]||(t[4]=[e("svg",{class:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),e("span",null,"Signing in...",-1)]))):(s(),o("span",E,"Sign In"))],8,_)],32)])])])]))}}),S=u(j,[["__scopeId","data-v-25efd296"]]);export{S as default};
