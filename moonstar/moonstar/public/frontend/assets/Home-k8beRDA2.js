var Ft=Object.defineProperty,Pt=Object.defineProperties;var kt=Object.getOwnPropertyDescriptors;var Ee=Object.getOwnPropertySymbols;var Je=Object.prototype.hasOwnProperty,et=Object.prototype.propertyIsEnumerable;var Ze=(e,t,n)=>t in e?Ft(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,S=(e,t)=>{for(var n in t||(t={}))Je.call(t,n)&&Ze(e,n,t[n]);if(Ee)for(var n of Ee(t))et.call(t,n)&&Ze(e,n,t[n]);return e},q=(e,t)=>Pt(e,kt(t));var N=(e,t)=>{var n={};for(var l in e)Je.call(e,l)&&t.indexOf(l)<0&&(n[l]=e[l]);if(e!=null&&Ee)for(var l of Ee(e))t.indexOf(l)<0&&et.call(e,l)&&(n[l]=e[l]);return n};var tt=(e,t,n)=>new Promise((l,r)=>{var o=s=>{try{i(n.next(s))}catch(u){r(u)}},a=s=>{try{i(n.throw(s))}catch(u){r(u)}},i=s=>s.done?l(s.value):Promise.resolve(s.value).then(o,a);i((n=n.apply(e,t)).next())});import{c as At,h as T,F as ze,i as M,p as H,w as R,r as g,a as v,d as k,o as F,b as I,e as pe,T as Dt,f as ut,s as Nt,n as jt,g as ue,j as X,k as nt,l as P,m as j,q as x,u as L,t as ae,v as de,_ as Mt,x as Ne,y as ne,z as lt,A as Rt,B as Bt,C as Ht,D as It,E as ce,G as _t,H as Vt,I as rt}from"./index-5LHlQTUw.js";function _(e,t,...n){if(e in t){let r=t[e];return typeof r=="function"?r(...n):r}let l=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,_),l}var $e=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))($e||{}),K=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(K||{});function V(o){var a=o,{visible:e=!0,features:t=0,ourProps:n,theirProps:l}=a,r=N(a,["visible","features","ourProps","theirProps"]);var i;let s=ct(l,n),u=Object.assign(r,{props:s});if(e||t&2&&s.static)return je(u);if(t&1){let f=(i=s.unmount)==null||i?0:1;return _(f,{0(){return null},1(){return je(q(S({},r),{props:q(S({},s),{hidden:!0,style:{display:"none"}})}))}})}return je(u)}function je({props:e,attrs:t,slots:n,slot:l,name:r}){var o,a;let d=ft(e,["unmount","static"]),{as:i}=d,s=N(d,["as"]),u=(o=n.default)==null?void 0:o.call(n,l),f={};if(l){let c=!1,p=[];for(let[m,b]of Object.entries(l))typeof b=="boolean"&&(c=!0),b===!0&&p.push(m);c&&(f["data-headlessui-state"]=p.join(" "))}if(i==="template"){if(u=dt(u!=null?u:[]),Object.keys(s).length>0||Object.keys(t).length>0){let[c,...p]=u!=null?u:[];if(!Ut(c)||p.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${r} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(s).concat(Object.keys(t)).map(h=>h.trim()).filter((h,U,$)=>$.indexOf(h)===U).sort((h,U)=>h.localeCompare(U)).map(h=>`  - ${h}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(h=>`  - ${h}`).join(`
`)].join(`
`));let m=ct((a=c.props)!=null?a:{},s),b=At(c,m);for(let h in m)h.startsWith("on")&&(b.props||(b.props={}),b.props[h]=m[h]);return b}return Array.isArray(u)&&u.length===1?u[0]:u}return T(i,Object.assign({},s,f),{default:()=>u})}function dt(e){return e.flatMap(t=>t.type===ze?dt(t.children):[t])}function ct(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let l of e)for(let r in l)r.startsWith("on")&&typeof l[r]=="function"?(n[r]!=null||(n[r]=[]),n[r].push(l[r])):t[r]=l[r];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(l=>[l,void 0])));for(let l in n)Object.assign(t,{[l](r,...o){let a=n[l];for(let i of a){if(r instanceof Event&&r.defaultPrevented)return;i(r,...o)}}});return t}function ft(e,t=[]){let n=Object.assign({},e);for(let l of t)l in n&&delete n[l];return n}function Ut(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}let Wt=0;function zt(){return++Wt}function Oe(){return zt()}var pt=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(pt||{});function w(e){var t;return e==null||e.value==null?null:(t=e.value.$el)!=null?t:e.value}let vt=Symbol("Context");var O=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(O||{});function qt(){return qe()!==null}function qe(){return M(vt,null)}function Gt(e){H(vt,e)}var Kt=Object.defineProperty,Yt=(e,t,n)=>t in e?Kt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ot=(e,t,n)=>(Yt(e,typeof t!="symbol"?t+"":t,n),n);class Xt{constructor(){ot(this,"current",this.detect()),ot(this,"currentId",0)}set(t){this.current!==t&&(this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}}let ve=new Xt;function le(e){if(ve.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let t=w(e);if(t)return t.ownerDocument}return document}let Be=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var G=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(G||{}),mt=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(mt||{}),Qt=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(Qt||{});function Zt(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Be)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var gt=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(gt||{});function Jt(e,t=0){var n;return e===((n=le(e))==null?void 0:n.body)?!1:_(t,{0(){return e.matches(Be)},1(){let l=e;for(;l!==null;){if(l.matches(Be))return!0;l=l.parentElement}return!1}})}var en=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(en||{});typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function J(e){e==null||e.focus({preventScroll:!0})}let tn=["textarea","input"].join(",");function nn(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,tn))!=null?n:!1}function ln(e,t=n=>n){return e.slice().sort((n,l)=>{let r=t(n),o=t(l);if(r===null||o===null)return 0;let a=r.compareDocumentPosition(o);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Te(e,t,{sorted:n=!0,relativeTo:l=null,skipElements:r=[]}={}){var o;let a=(o=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?o:document,i=Array.isArray(e)?n?ln(e):e:Zt(e);r.length>0&&i.length>1&&(i=i.filter(m=>!r.includes(m))),l=l!=null?l:a.activeElement;let s=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,i.indexOf(l))-1;if(t&4)return Math.max(0,i.indexOf(l))+1;if(t&8)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=t&32?{preventScroll:!0}:{},d=0,c=i.length,p;do{if(d>=c||d+c<=0)return 0;let m=u+d;if(t&16)m=(m+c)%c;else{if(m<0)return 3;if(m>=c)return 1}p=i[m],p==null||p.focus(f),d+=s}while(p!==a.activeElement);return t&6&&nn(p)&&p.select(),2}function Se(e,t,n){ve.isServer||R(l=>{document.addEventListener(e,t,n),l(()=>document.removeEventListener(e,t,n))})}function ht(e,t,n){ve.isServer||R(l=>{window.addEventListener(e,t,n),l(()=>window.removeEventListener(e,t,n))})}function rn(e,t,n=v(()=>!0)){function l(o,a){if(!n.value||o.defaultPrevented)return;let i=a(o);if(i===null||!i.getRootNode().contains(i))return;let s=function u(f){return typeof f=="function"?u(f()):Array.isArray(f)||f instanceof Set?f:[f]}(e);for(let u of s){if(u===null)continue;let f=u instanceof HTMLElement?u:w(u);if(f!=null&&f.contains(i)||o.composed&&o.composedPath().includes(f))return}return!Jt(i,gt.Loose)&&i.tabIndex!==-1&&o.preventDefault(),t(o,i)}let r=g(null);Se("pointerdown",o=>{var a,i;n.value&&(r.value=((i=(a=o.composedPath)==null?void 0:a.call(o))==null?void 0:i[0])||o.target)},!0),Se("mousedown",o=>{var a,i;n.value&&(r.value=((i=(a=o.composedPath)==null?void 0:a.call(o))==null?void 0:i[0])||o.target)},!0),Se("click",o=>{r.value&&(l(o,()=>r.value),r.value=null)},!0),Se("touchend",o=>l(o,()=>o.target instanceof HTMLElement?o.target:null),!0),ht("blur",o=>l(o,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var Ce=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Ce||{});let He=k({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(e,{slots:t,attrs:n}){return()=>{let a=e,{features:l}=a,r=N(a,["features"]),o={"aria-hidden":(l&2)===2?!0:void 0,style:S({position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},(l&4)===4&&(l&2)!==2&&{display:"none"})};return V({ourProps:o,theirProps:r,slot:{},attrs:n,slots:t,name:"Hidden"})}}});function on(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Ge(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function me(){let e=[],t={addEventListener(n,l,r,o){return n.addEventListener(l,r,o),t.add(()=>n.removeEventListener(l,r,o))},requestAnimationFrame(...n){let l=requestAnimationFrame(...n);t.add(()=>cancelAnimationFrame(l))},nextFrame(...n){t.requestAnimationFrame(()=>{t.requestAnimationFrame(...n)})},setTimeout(...n){let l=setTimeout(...n);t.add(()=>clearTimeout(l))},microTask(...n){let l={current:!0};return Ge(()=>{l.current&&n[0]()}),t.add(()=>{l.current=!1})},style(n,l,r){let o=n.style.getPropertyValue(l);return Object.assign(n.style,{[l]:r}),this.add(()=>{Object.assign(n.style,{[l]:o})})},group(n){let l=me();return n(l),this.add(()=>l.dispose())},add(n){return e.push(n),()=>{let l=e.indexOf(n);if(l>=0)for(let r of e.splice(l,1))r()}},dispose(){for(let n of e.splice(0))n()}};return t}var fe=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(fe||{});function an(){let e=g(0);return ht("keydown",t=>{t.key==="Tab"&&(e.value=t.shiftKey?1:0)}),e}function yt(e,t,n,l){ve.isServer||R(r=>{e=e!=null?e:window,e.addEventListener(t,n,l),r(()=>e.removeEventListener(t,n,l))})}function sn(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",t),t())}function bt(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.value){let l=w(n);l instanceof HTMLElement&&t.add(l)}return t}var wt=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(wt||{});let ie=Object.assign(k({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:g(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:l}){let r=g(null);l({el:r,$el:r});let o=v(()=>le(r)),a=g(!1);F(()=>a.value=!0),I(()=>a.value=!1),dn({ownerDocument:o},v(()=>a.value&&!!(e.features&16)));let i=cn({ownerDocument:o,container:r,initialFocus:v(()=>e.initialFocus)},v(()=>a.value&&!!(e.features&2)));fn({ownerDocument:o,container:r,containers:e.containers,previousActiveElement:i},v(()=>a.value&&!!(e.features&8)));let s=an();function u(p){let m=w(r);m&&(b=>b())(()=>{_(s.value,{[fe.Forwards]:()=>{Te(m,G.First,{skipElements:[p.relatedTarget]})},[fe.Backwards]:()=>{Te(m,G.Last,{skipElements:[p.relatedTarget]})}})})}let f=g(!1);function d(p){p.key==="Tab"&&(f.value=!0,requestAnimationFrame(()=>{f.value=!1}))}function c(p){if(!a.value)return;let m=bt(e.containers);w(r)instanceof HTMLElement&&m.add(w(r));let b=p.relatedTarget;b instanceof HTMLElement&&b.dataset.headlessuiFocusGuard!=="true"&&(Et(m,b)||(f.value?Te(w(r),_(s.value,{[fe.Forwards]:()=>G.Next,[fe.Backwards]:()=>G.Previous})|G.WrapAround,{relativeTo:p.target}):p.target instanceof HTMLElement&&J(p.target)))}return()=>{let p={},m={ref:r,onKeydown:d,onFocusout:c},B=e,{features:b,initialFocus:h,containers:U}=B,$=N(B,["features","initialFocus","containers"]);return T(ze,[!!(b&4)&&T(He,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:Ce.Focusable}),V({ourProps:m,theirProps:S(S({},t),$),slot:p,attrs:t,slots:n,name:"FocusTrap"}),!!(b&4)&&T(He,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:Ce.Focusable})])}}}),{features:wt}),Q=[];sn(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&Q[0]!==t.target&&(Q.unshift(t.target),Q=Q.filter(n=>n!=null&&n.isConnected),Q.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function un(e){let t=g(Q.slice());return pe([e],([n],[l])=>{l===!0&&n===!1?Ge(()=>{t.value.splice(0)}):l===!1&&n===!0&&(t.value=Q.slice())},{flush:"post"}),()=>{var n;return(n=t.value.find(l=>l!=null&&l.isConnected))!=null?n:null}}function dn({ownerDocument:e},t){let n=un(t);F(()=>{R(()=>{var l,r;t.value||((l=e.value)==null?void 0:l.activeElement)===((r=e.value)==null?void 0:r.body)&&J(n())},{flush:"post"})}),I(()=>{t.value&&J(n())})}function cn({ownerDocument:e,container:t,initialFocus:n},l){let r=g(null),o=g(!1);return F(()=>o.value=!0),I(()=>o.value=!1),F(()=>{pe([t,n,l],(a,i)=>{if(a.every((u,f)=>(i==null?void 0:i[f])===u)||!l.value)return;let s=w(t);s&&Ge(()=>{var u,f;if(!o.value)return;let d=w(n),c=(u=e.value)==null?void 0:u.activeElement;if(d){if(d===c){r.value=c;return}}else if(s.contains(c)){r.value=c;return}d?J(d):Te(s,G.First|G.NoScroll)===mt.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),r.value=(f=e.value)==null?void 0:f.activeElement})},{immediate:!0,flush:"post"})}),r}function fn({ownerDocument:e,container:t,containers:n,previousActiveElement:l},r){var o;yt((o=e.value)==null?void 0:o.defaultView,"focus",a=>{if(!r.value)return;let i=bt(n);w(t)instanceof HTMLElement&&i.add(w(t));let s=l.value;if(!s)return;let u=a.target;u&&u instanceof HTMLElement?Et(i,u)?(l.value=u,J(u)):(a.preventDefault(),a.stopPropagation(),J(s)):J(l.value)},!0)}function Et(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let Me=new Map,se=new Map;function at(e,t=g(!0)){R(n=>{var l;if(!t.value)return;let r=w(e);if(!r)return;n(function(){var a;if(!r)return;let i=(a=se.get(r))!=null?a:1;if(i===1?se.delete(r):se.set(r,i-1),i!==1)return;let s=Me.get(r);s&&(s["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",s["aria-hidden"]),r.inert=s.inert,Me.delete(r))});let o=(l=se.get(r))!=null?l:0;se.set(r,o+1),o===0&&(Me.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0)})}let St=Symbol("ForcePortalRootContext");function pn(){return M(St,!1)}let it=k({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:t,attrs:n}){return H(St,e.force),()=>{let o=e,{force:l}=o,r=N(o,["force"]);return V({theirProps:r,ourProps:{},slot:{},slots:t,attrs:n,name:"ForcePortalRoot"})}}});function vn(e){let t=le(e);if(!t){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let n=t.getElementById("headlessui-portal-root");if(n)return n;let l=t.createElement("div");return l.setAttribute("id","headlessui-portal-root"),t.body.appendChild(l)}let mn=k({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n}){let l=g(null),r=v(()=>le(l)),o=pn(),a=M(xt,null),i=g(o===!0||a==null?vn(l.value):a.resolveTarget());R(()=>{o||a!=null&&(i.value=a.resolveTarget())});let s=M(Ie,null);return F(()=>{let u=w(l);u&&s&&I(s.register(u))}),I(()=>{var u,f;let d=(u=r.value)==null?void 0:u.getElementById("headlessui-portal-root");d&&i.value===d&&i.value.children.length<=0&&((f=i.value.parentElement)==null||f.removeChild(i.value))}),()=>{if(i.value===null)return null;let u={ref:l,"data-headlessui-portal":""};return T(Dt,{to:i.value},V({ourProps:u,theirProps:e,slot:{},attrs:n,slots:t,name:"Portal"}))}}}),Ie=Symbol("PortalParentContext");function gn(){let e=M(Ie,null),t=g([]);function n(o){return t.value.push(o),e&&e.register(o),()=>l(o)}function l(o){let a=t.value.indexOf(o);a!==-1&&t.value.splice(a,1),e&&e.unregister(o)}let r={register:n,unregister:l,portals:t};return[t,k({name:"PortalWrapper",setup(o,{slots:a}){return H(Ie,r),()=>{var i;return(i=a.default)==null?void 0:i.call(a)}}})]}let xt=Symbol("PortalGroupContext"),hn=k({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:n}){let l=ut({resolveTarget(){return e.target}});return H(xt,l),()=>{let a=e,{target:r}=a,o=N(a,["target"]);return V({theirProps:o,ourProps:{},slot:{},attrs:t,slots:n,name:"PortalGroup"})}}}),Lt=Symbol("StackContext");var _e=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(_e||{});function yn(){return M(Lt,()=>{})}function bn({type:e,enabled:t,element:n,onUpdate:l}){let r=yn();function o(...a){l==null||l(...a),r(...a)}F(()=>{pe(t,(a,i)=>{a?o(0,e,n):i===!0&&o(1,e,n)},{immediate:!0,flush:"sync"})}),I(()=>{t.value&&o(1,e,n)}),H(Lt,o)}let wn=Symbol("DescriptionContext");function En({slot:e=g({}),name:t="Description",props:n={}}={}){let l=g([]);function r(o){return l.value.push(o),()=>{let a=l.value.indexOf(o);a!==-1&&l.value.splice(a,1)}}return H(wn,{register:r,slot:e,name:t,props:n}),v(()=>l.value.length>0?l.value.join(" "):void 0)}function Sn(e){let t=Nt(e.getSnapshot());return I(e.subscribe(()=>{t.value=e.getSnapshot()})),t}function xn(e,t){let n=e(),l=new Set;return{getSnapshot(){return n},subscribe(r){return l.add(r),()=>l.delete(r)},dispatch(r,...o){let a=t[r].call(n,...o);a&&(n=a,l.forEach(i=>i()))}}}function Ln(){let e;return{before({doc:t}){var n;let l=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-l.clientWidth},after({doc:t,d:n}){let l=t.documentElement,r=l.clientWidth-l.offsetWidth,o=e-r;n.style(l,"paddingRight",`${o}px`)}}}function Tn(){if(!on())return{};let e;return{before(){e=window.pageYOffset},after({doc:t,d:n,meta:l}){function r(a){return l.containers.flatMap(i=>i()).some(i=>i.contains(a))}if(window.getComputedStyle(t.documentElement).scrollBehavior!=="auto"){let a=me();a.style(t.documentElement,"scroll-behavior","auto"),n.add(()=>n.microTask(()=>a.dispose()))}n.style(t.body,"marginTop",`-${e}px`),window.scrollTo(0,0);let o=null;n.addEventListener(t,"click",a=>{if(a.target instanceof HTMLElement)try{let i=a.target.closest("a");if(!i)return;let{hash:s}=new URL(i.href),u=t.querySelector(s);u&&!r(u)&&(o=u)}catch(i){}},!0),n.addEventListener(t,"touchmove",a=>{a.target instanceof HTMLElement&&!r(a.target)&&a.preventDefault()},{passive:!1}),n.add(()=>{window.scrollTo(0,window.pageYOffset+e),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})}}}function $n(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function Cn(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Z=xn(()=>new Map,{PUSH(e,t){var n;let l=(n=this.get(e))!=null?n:{doc:e,count:0,d:me(),meta:new Set};return l.count++,l.meta.add(t),this.set(e,l),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let l={doc:e,d:t,meta:Cn(n)},r=[Tn(),Ln(),$n()];r.forEach(({before:o})=>o==null?void 0:o(l)),r.forEach(({after:o})=>o==null?void 0:o(l))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});Z.subscribe(()=>{let e=Z.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let l=t.get(n.doc)==="hidden",r=n.count!==0;(r&&!l||!r&&l)&&Z.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&Z.dispatch("TEARDOWN",n)}});function On(e,t,n){let l=Sn(Z),r=v(()=>{let o=e.value?l.value.get(e.value):void 0;return o?o.count>0:!1});return pe([e,t],([o,a],[i],s)=>{if(!o||!a)return;Z.dispatch("PUSH",o,n);let u=!1;s(()=>{u||(Z.dispatch("POP",i!=null?i:o,n),u=!0)})},{immediate:!0}),r}function Fn({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){let l=g(null),r=le(l);function o(){var a;let i=[];for(let s of e)s!==null&&(s instanceof HTMLElement?i.push(s):"value"in s&&s.value instanceof HTMLElement&&i.push(s.value));if(t!=null&&t.value)for(let s of t.value)i.push(s);for(let s of(a=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?a:[])s!==document.body&&s!==document.head&&s instanceof HTMLElement&&s.id!=="headlessui-portal-root"&&(s.contains(w(l))||i.some(u=>s.contains(u))||i.push(s));return i}return{resolveContainers:o,contains(a){return o().some(i=>i.contains(a))},mainTreeNodeRef:l,MainTreeNode(){return n!=null?null:T(He,{features:Ce.Hidden,ref:l})}}}var Pn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Pn||{});let Ve=Symbol("DialogContext");function Ke(e){let t=M(Ve,null);if(t===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Ke),n}return t}let xe="DC8F892D-2EBD-447C-A4C8-A03058436FF4",kn=k({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:xe},initialFocus:{type:Object,default:null},id:{type:String,default:()=>`headlessui-dialog-${Oe()}`}},emits:{close:e=>!0},setup(e,{emit:t,attrs:n,slots:l,expose:r}){var o;let a=g(!1);F(()=>{a.value=!0});let i=g(0),s=qe(),u=v(()=>e.open===xe&&s!==null?(s.value&O.Open)===O.Open:e.open),f=g(null),d=v(()=>le(f));if(r({el:f,$el:f}),!(e.open!==xe||s!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof u.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${u.value===xe?void 0:e.open}`);let c=v(()=>a.value&&u.value?0:1),p=v(()=>c.value===0),m=v(()=>i.value>1),b=M(Ve,null)!==null,[h,U]=gn(),{resolveContainers:$,mainTreeNodeRef:B,MainTreeNode:ge}=Fn({portals:h,defaultContainers:[v(()=>{var y;return(y=C.panelRef.value)!=null?y:f.value})]}),he=v(()=>m.value?"parent":"leaf"),re=v(()=>s!==null?(s.value&O.Closing)===O.Closing:!1),Pe=v(()=>b||re.value?!1:p.value),ye=v(()=>{var y,E,A;return(A=Array.from((E=(y=d.value)==null?void 0:y.querySelectorAll("body > *"))!=null?E:[]).find(D=>D.id==="headlessui-portal-root"?!1:D.contains(w(B))&&D instanceof HTMLElement))!=null?A:null});at(ye,Pe);let ke=v(()=>m.value?!0:p.value),Ae=v(()=>{var y,E,A;return(A=Array.from((E=(y=d.value)==null?void 0:y.querySelectorAll("[data-headlessui-portal]"))!=null?E:[]).find(D=>D.contains(w(B))&&D instanceof HTMLElement))!=null?A:null});at(Ae,ke),bn({type:"Dialog",enabled:v(()=>c.value===0),element:f,onUpdate:(y,E)=>{if(E==="Dialog")return _(y,{[_e.Add]:()=>i.value+=1,[_e.Remove]:()=>i.value-=1})}});let De=En({name:"DialogDescription",slot:v(()=>({open:u.value}))}),ee=g(null),C={titleId:ee,panelRef:g(null),dialogState:c,setTitleId(y){ee.value!==y&&(ee.value=y)},close(){t("close",!1)}};H(Ve,C);let oe=v(()=>!(!p.value||m.value));rn($,(y,E)=>{C.close(),jt(()=>E==null?void 0:E.focus())},oe);let W=v(()=>!(m.value||c.value!==0));yt((o=d.value)==null?void 0:o.defaultView,"keydown",y=>{W.value&&(y.defaultPrevented||y.key===pt.Escape&&(y.preventDefault(),y.stopPropagation(),C.close()))});let te=v(()=>!(re.value||c.value!==0||b));return On(d,te,y=>{var E;return{containers:[...(E=y.containers)!=null?E:[],$]}}),R(y=>{if(c.value!==0)return;let E=w(f);if(!E)return;let A=new ResizeObserver(D=>{for(let be of D){let z=be.target.getBoundingClientRect();z.x===0&&z.y===0&&z.width===0&&z.height===0&&C.close()}});A.observe(E),y(()=>A.disconnect())}),()=>{let we=e,{id:y,open:E,initialFocus:A}=we,D=N(we,["id","open","initialFocus"]),be=q(S({},n),{ref:f,id:y,role:"dialog","aria-modal":c.value===0?!0:void 0,"aria-labelledby":ee.value,"aria-describedby":De.value}),z={open:c.value===0};return T(it,{force:!0},()=>[T(mn,()=>T(hn,{target:f.value},()=>T(it,{force:!1},()=>T(ie,{initialFocus:A,containers:$,features:p.value?_(he.value,{parent:ie.features.RestoreFocus,leaf:ie.features.All&~ie.features.FocusLock}):ie.features.None},()=>T(U,{},()=>V({ourProps:be,theirProps:S(S({},D),n),slot:z,attrs:n,slots:l,visible:c.value===0,features:$e.RenderStrategy|$e.Static,name:"Dialog"})))))),T(ge)])}}}),An=k({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:()=>`headlessui-dialog-panel-${Oe()}`}},setup(e,{attrs:t,slots:n,expose:l}){let r=Ke("DialogPanel");l({el:r.panelRef,$el:r.panelRef});function o(a){a.stopPropagation()}return()=>{let u=e,{id:a}=u,i=N(u,["id"]),s={id:a,ref:r.panelRef,onClick:o};return V({ourProps:s,theirProps:i,slot:{open:r.dialogState.value===0},attrs:t,slots:n,name:"DialogPanel"})}}}),Dn=k({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:()=>`headlessui-dialog-title-${Oe()}`}},setup(e,{attrs:t,slots:n}){let l=Ke("DialogTitle");return F(()=>{l.setTitleId(e.id),I(()=>l.setTitleId(null))}),()=>{let a=e,{id:r}=a,o=N(a,["id"]);return V({ourProps:{id:r},theirProps:o,slot:{open:l.dialogState.value===0},attrs:t,slots:n,name:"DialogTitle"})}}});function Nn(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function Re(e,...t){e&&t.length>0&&e.classList.add(...t)}function Le(e,...t){e&&t.length>0&&e.classList.remove(...t)}var Ue=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(Ue||{});function jn(e,t){let n=me();if(!e)return n.dispose;let{transitionDuration:l,transitionDelay:r}=getComputedStyle(e),[o,a]=[l,r].map(i=>{let[s=0]=i.split(",").filter(Boolean).map(u=>u.includes("ms")?parseFloat(u):parseFloat(u)*1e3).sort((u,f)=>f-u);return s});return o!==0?n.setTimeout(()=>t("finished"),o+a):t("finished"),n.add(()=>t("cancelled")),n.dispose}function st(e,t,n,l,r,o){let a=me(),i=o!==void 0?Nn(o):()=>{};return Le(e,...r),Re(e,...t,...n),a.nextFrame(()=>{Le(e,...n),Re(e,...l),a.add(jn(e,s=>(Le(e,...l,...t),Re(e,...r),i(s))))}),a.add(()=>Le(e,...t,...n,...l,...r)),a.add(()=>i("cancelled")),a.dispose}function Y(e=""){return e.split(" ").filter(t=>t.trim().length>1)}let Ye=Symbol("TransitionContext");var Mn=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Mn||{});function Rn(){return M(Ye,null)!==null}function Bn(){let e=M(Ye,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function Hn(){let e=M(Xe,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}let Xe=Symbol("NestingContext");function Fe(e){return"children"in e?Fe(e.children):e.value.filter(({state:t})=>t==="visible").length>0}function Tt(e){let t=g([]),n=g(!1);F(()=>n.value=!0),I(()=>n.value=!1);function l(o,a=K.Hidden){let i=t.value.findIndex(({id:s})=>s===o);i!==-1&&(_(a,{[K.Unmount](){t.value.splice(i,1)},[K.Hidden](){t.value[i].state="hidden"}}),!Fe(t)&&n.value&&(e==null||e()))}function r(o){let a=t.value.find(({id:i})=>i===o);return a?a.state!=="visible"&&(a.state="visible"):t.value.push({id:o,state:"visible"}),()=>l(o,K.Unmount)}return{children:t,register:r,unregister:l}}let $t=$e.RenderStrategy,We=k({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:l,expose:r}){let o=g(0);function a(){o.value|=O.Opening,t("beforeEnter")}function i(){o.value&=~O.Opening,t("afterEnter")}function s(){o.value|=O.Closing,t("beforeLeave")}function u(){o.value&=~O.Closing,t("afterLeave")}if(!Rn()&&qt())return()=>T(Ct,q(S({},e),{onBeforeEnter:a,onAfterEnter:i,onBeforeLeave:s,onAfterLeave:u}),l);let f=g(null),d=v(()=>e.unmount?K.Unmount:K.Hidden);r({el:f,$el:f});let{show:c,appear:p}=Bn(),{register:m,unregister:b}=Hn(),h=g(c.value?"visible":"hidden"),U={value:!0},$=Oe(),B={value:!1},ge=Tt(()=>{!B.value&&h.value!=="hidden"&&(h.value="hidden",b($),u())});F(()=>{let C=m($);I(C)}),R(()=>{if(d.value===K.Hidden&&$){if(c.value&&h.value!=="visible"){h.value="visible";return}_(h.value,{hidden:()=>b($),visible:()=>m($)})}});let he=Y(e.enter),re=Y(e.enterFrom),Pe=Y(e.enterTo),ye=Y(e.entered),ke=Y(e.leave),Ae=Y(e.leaveFrom),De=Y(e.leaveTo);F(()=>{R(()=>{if(h.value==="visible"){let C=w(f);if(C instanceof Comment&&C.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function ee(C){let oe=U.value&&!p.value,W=w(f);!W||!(W instanceof HTMLElement)||oe||(B.value=!0,c.value&&a(),c.value||s(),C(c.value?st(W,he,re,Pe,ye,te=>{B.value=!1,te===Ue.Finished&&i()}):st(W,ke,Ae,De,ye,te=>{B.value=!1,te===Ue.Finished&&(Fe(ge)||(h.value="hidden",b($),u()))})))}return F(()=>{pe([c],(C,oe,W)=>{ee(W),U.value=!1},{immediate:!0})}),H(Xe,ge),Gt(v(()=>_(h.value,{visible:O.Open,hidden:O.Closed})|o.value)),()=>{let Qe=e,{appear:C,show:oe,enter:W,enterFrom:te,enterTo:y,entered:E,leave:A,leaveFrom:D,leaveTo:be}=Qe,z=N(Qe,["appear","show","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"]),we={ref:f},Ot=S(S({},z),p.value&&c.value&&ve.isServer?{class:ue([n.class,z.class,...he,...re])}:{});return V({theirProps:Ot,ourProps:we,slot:{},slots:l,attrs:n,features:$t,visible:h.value==="visible",name:"TransitionChild"})}}}),In=We,Ct=k({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:l}){let r=qe(),o=v(()=>e.show===null&&r!==null?(r.value&O.Open)===O.Open:e.show);R(()=>{if(![!0,!1].includes(o.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let a=g(o.value?"visible":"hidden"),i=Tt(()=>{a.value="hidden"}),s=g(!0),u={show:o,appear:v(()=>e.appear||!s.value)};return F(()=>{R(()=>{s.value=!1,o.value?a.value="visible":Fe(i)||(a.value="hidden")})}),H(Xe,i),H(Ye,u),()=>{let f=ft(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),d={unmount:e.unmount};return V({ourProps:q(S({},d),{as:"template"}),theirProps:{},slot:{},slots:q(S({},l),{default:()=>[T(In,S(S(S({onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave")},n),d),f),l.default)]}),attrs:{},features:$t,visible:a.value==="visible",name:"Transition"})}}});const _n=["data-dialog"],Vn={class:"bg-surface-modal px-4 pb-6 pt-5 sm:px-6"},Un={class:"flex"},Wn={class:"w-full flex-1"},zn={class:"mb-6 flex items-center justify-between"},qn={class:"flex items-center space-x-2"},Gn={class:"text-2xl font-semibold leading-6 text-ink-gray-9"},Kn={key:0,class:"text-p-base text-ink-gray-7"},Yn={key:0,class:"px-4 pb-7 pt-4 sm:px-6"},Xn={class:"space-y-2"},Qn=k({__name:"Dialog",props:{modelValue:{type:Boolean},options:{default:()=>({})},disableOutsideClickToClose:{type:Boolean,default:!1}},emits:["update:modelValue","close","after-leave"],setup(e,{emit:t}){const n=e,l=t,r=v(()=>{let d=n.options.actions;return d!=null&&d.length?d.map(c=>{let p=ut(q(S({},c),{loading:!1,onClick:c.onClick?()=>tt(this,null,function*(){p.loading=!0;try{if(c.onClick){let m=function(){console.warn("Value passed to onClick is a context object. Please use context.close() instead of context() to close the dialog."),a()};m.close=a,yield c.onClick(m)}}finally{p.loading=!1}}):a}));return p}):[]}),o=v({get(){return n.modelValue},set(d){l("update:modelValue",d),d||l("close")}});function a(){o.value=!1}const i=v(()=>{var c;if(!((c=n.options)!=null&&c.icon))return null;let d=n.options.icon;return typeof d=="string"&&(d={name:d}),d}),s=v(()=>{var c;const d=((c=n.options)==null?void 0:c.position)||"center";return{center:"justify-center",top:"pt-[20vh]"}[d]}),u=v(()=>{var c;const d=(c=i.value)==null?void 0:c.appearance;return d?{warning:"bg-surface-amber-2",info:"bg-surface-blue-2",danger:"bg-surface-red-2",success:"bg-surface-green-2"}[d]:"bg-surface-gray-2"}),f=v(()=>{var c;const d=(c=i.value)==null?void 0:c.appearance;return d?{warning:"text-ink-amber-3",info:"text-ink-blue-3",danger:"text-ink-red-4",success:"text-ink-green-3"}[d]:"text-ink-gray-5"});return(d,c)=>(X(),nt(L(Ct),{as:"template",show:o.value,onAfterLeave:c[1]||(c[1]=p=>d.$emit("after-leave"))},{default:P(()=>[j(L(kn),{as:"div",class:"fixed inset-0 z-10 overflow-y-auto",onClose:c[0]||(c[0]=p=>!d.disableOutsideClickToClose&&a())},{default:P(()=>[x("div",{class:ue(["flex min-h-screen flex-col items-center px-4 py-4 text-center",s.value])},[j(L(We),{as:"template",enter:"ease-out duration-150","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-150","leave-from":"opacity-100","leave-to":"opacity-0"},{default:P(()=>[x("div",{class:"fixed inset-0 bg-black-overlay-200 transition-opacity dark:backdrop-filter dark:backdrop-blur-[1px]","data-dialog":d.options.title},null,8,_n)]),_:1}),j(L(We),{as:"template",enter:"ease-out duration-150","enter-from":"opacity-50 translate-y-2 scale-95","enter-to":"opacity-100 translate-y-0 scale-100",leave:"ease-in duration-150","leave-from":"opacity-100 translate-y-0 scale-100","leave-to":"opacity-50 translate-y-4 translate-y-4 scale-95"},{default:P(()=>[j(L(An),{class:ue(["my-8 inline-block w-full transform overflow-hidden rounded-xl bg-surface-modal text-left align-middle shadow-xl transition-all",{"max-w-7xl":d.options.size==="7xl","max-w-6xl":d.options.size==="6xl","max-w-5xl":d.options.size==="5xl","max-w-4xl":d.options.size==="4xl","max-w-3xl":d.options.size==="3xl","max-w-2xl":d.options.size==="2xl","max-w-xl":d.options.size==="xl","max-w-lg":d.options.size==="lg"||!d.options.size,"max-w-md":d.options.size==="md","max-w-sm":d.options.size==="sm","max-w-xs":d.options.size==="xs"}])},{default:P(()=>[ae(d.$slots,"body",{},()=>[ae(d.$slots,"body-main",{},()=>[x("div",Vn,[x("div",Un,[x("div",Wn,[x("div",zn,[x("div",qn,[i.value?(X(),de("div",{key:0,class:ue(["flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-full",u.value])},[j(Mt,{name:i.value.name,class:ue(["h-4 w-4",f.value]),"aria-hidden":"true"},null,8,["name","class"])],2)):Ne("",!0),j(L(Dn),{as:"header"},{default:P(()=>[ae(d.$slots,"body-title",{},()=>[x("h3",Gn,ne(d.options.title||"Untitled"),1)])]),_:3})]),j(L(lt),{variant:"ghost",onClick:a},{icon:P(()=>c[2]||(c[2]=[x("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"text-ink-gray-9"},[x("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12.8567 3.85355C13.052 3.65829 13.052 3.34171 12.8567 3.14645C12.6615 2.95118 12.3449 2.95118 12.1496 3.14645L8.00201 7.29405L3.85441 3.14645C3.65914 2.95118 3.34256 2.95118 3.1473 3.14645C2.95204 3.34171 2.95204 3.65829 3.1473 3.85355L7.29491 8.00116L3.14645 12.1496C2.95118 12.3449 2.95118 12.6615 3.14645 12.8567C3.34171 13.052 3.65829 13.052 3.85355 12.8567L8.00201 8.70827L12.1505 12.8567C12.3457 13.052 12.6623 13.052 12.8576 12.8567C13.0528 12.6615 13.0528 12.3449 12.8576 12.1496L8.70912 8.00116L12.8567 3.85355Z",fill:"currentColor"})],-1)])),_:1})]),ae(d.$slots,"body-content",{},()=>[d.options.message?(X(),de("p",Kn,ne(d.options.message),1)):Ne("",!0)])])])])]),r.value.length||d.$slots.actions?(X(),de("div",Yn,[ae(d.$slots,"actions",Rt(Bt({close:a})),()=>[x("div",Xn,[(X(!0),de(ze,null,Ht(r.value,p=>(X(),nt(L(lt),It({class:"w-full",key:p.label,ref_for:!0},p),{default:P(()=>[ce(ne(p.label),1)]),_:2},1040))),128))])])])):Ne("",!0)])]),_:3},8,["class"])]),_:3})],2)]),_:3})]),_:3},8,["show"]))}}),Zn={class:"max-w-3xl py-12 mx-auto"},Jn={class:"font-bold text-lg text-gray-600 mb-4"},el={class:"flex flex-row space-x-2 mt-4"},ll={__name:"Home",setup(e){const t=_t({url:"ping",auto:!0}),n=g(!1);return(l,r)=>{const o=Vt("Button");return X(),de("div",Zn,[x("h2",Jn," Welcome "+ne(L(rt).user)+"! ",1),j(o,{theme:"gray",variant:"solid","icon-left":"code",onClick:L(t).fetch,loading:L(t).loading},{default:P(()=>r[3]||(r[3]=[ce(" Click to send 'ping' request ")])),_:1},8,["onClick","loading"]),x("div",null,ne(L(t).data),1),x("pre",null,ne(L(t)),1),x("div",el,[j(o,{onClick:r[0]||(r[0]=a=>n.value=!0)},{default:P(()=>r[4]||(r[4]=[ce("Open Dialog")])),_:1}),j(o,{onClick:r[1]||(r[1]=a=>L(rt).logout.submit())},{default:P(()=>r[5]||(r[5]=[ce("Logout")])),_:1})]),j(L(Qn),{title:"Title",modelValue:n.value,"onUpdate:modelValue":r[2]||(r[2]=a=>n.value=a)},{default:P(()=>r[6]||(r[6]=[ce(" Dialog content ")])),_:1},8,["modelValue"])])}}};export{ll as default};
