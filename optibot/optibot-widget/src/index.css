/* @import 'bootstrap/dist/css/bootstrap.min.css'; */



:root {
  --optibot-primary: #007bff;
  --optibot-primary-dark: #0056b3;
  --optibot-secondary: #6c757d;
  --optibot-success: #28a745;
  --optibot-danger: #dc3545;
  --optibot-warning: #ffc107;
  --optibot-info: #17a2b8;
  --optibot-light: #f8f9fa;
  --optibot-dark: #343a40;
  --optibot-white: #ffffff;
  --optibot-border: #dee2e6;
  --optibot-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --optibot-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.optibot-chat-container {
  display: flex;
  flex-direction: column;
  height: 500px;
  max-width: 400px;
  width: 100%;
  background: var(--optibot-white);
  border-radius: 12px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
}

.optibot-header {
  background: rgb(77, 73, 73);
  color: white;
  padding: 16px 20px;
  border-bottom: 1px solid var(--optibot-border);
}

.optibot-header h5 {
  font-size: 16px;
  margin: 0;
}

.optibot-header small {
  opacity: 0.9;
  font-size: 12px;
}

.optibot-messages-container {
  flex: 1;
  overflow: hidden;
  background: #f7f8fc;
}

.optibot-messages {
  height: 100%;
  overflow-y: auto;
  padding: 16px 16px 8px 16px;
  scroll-behavior: smooth;
}

.optibot-messages::-webkit-scrollbar {
  width: 4px;
}

.optibot-messages::-webkit-scrollbar-track {
  background: transparent;
}

.optibot-messages::-webkit-scrollbar-thumb {
  background: var(--optibot-border);
  border-radius: 2px;
}

.optibot-messages::-webkit-scrollbar-thumb:hover {
  background: var(--optibot-secondary);
}

.optibot-message {
  margin-bottom: 16px;
}

.optibot-message:last-child {
  margin-bottom: 8px;
}

.optibot-message-avatar {
  flex-shrink: 0;
}

.optibot-ai-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--optibot-primary), var(--optibot-info));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--optibot-shadow);
}

.optibot-human-avatar {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optibot-avatar-fallback {
  width: 36px;
  height: 36px;
  background: var(--optibot-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.optibot-user-avatar .optibot-avatar-fallback {
  width: 32px;
  height: 32px;
}

.optibot-message-bubble {
  max-width: 280px;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
  box-shadow: var(--optibot-shadow);
}

.optibot-bubble-ai {
  background: var(--optibot-white);
  border: 1px solid var(--optibot-border);
  color: var(--optibot-dark);
}

.optibot-bubble-human {
  background: linear-gradient(135deg, var(--optibot-primary), var(--optibot-primary-dark));
  color: white;
  margin-left: auto;
}

.optibot-bubble-error {
  background: #fee;
  border-color: var(--optibot-danger);
  color: var(--optibot-danger);
}

.optibot-message-content {
  line-height: 1.4;
  font-size: 14px;
}

.optibot-typing-indicator {
  display: flex;
  align-items: center;
  color: var(--optibot-secondary);
}

.optibot-typing-dots {
  display: flex;
  gap: 3px;
}

.optibot-typing-dots span {
  width: 6px;
  height: 6px;
  background: var(--optibot-secondary);
  border-radius: 50%;
  animation: optibot-bounce 1.4s infinite ease-in-out both;
}

.optibot-typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.optibot-typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes optibot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.optibot-input-container {
  padding: 12px;
  background: var(--optibot-white);
  border-top: 1px solid var(--optibot-border);
}

.optibot-input-container .card {
  background: #f8f9fa;
  border: 1px solid var(--optibot-border);
}

.optibot-input-container .form-control {
  background: transparent;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
}

.optibot-input-container .form-control:focus {
  background: transparent;
  border-color: transparent;
  box-shadow: none;
}

.optibot-toast {
  position: absolute;
  top: 10px;
  left: 16px;
  right: 16px;
  z-index: 1000;
  font-size: 13px;
}

.optibot-fab-container {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.optibot-fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--optibot-primary), var(--optibot-primary-dark));
  color: white;
  border: none;
  box-shadow: var(--optibot-shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.optibot-fab:hover {
  box-shadow: 0 1.5rem 4rem rgba(0, 123, 255, 0.4);
  transform: translateY(-2px);
}

.optibot-fab:active {
  transform: translateY(0);
}

.optibot-fab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s;
}

.optibot-fab:hover::before {
  opacity: 1;
}

.optibot-modal-overlay {
  position: fixed;
  bottom: 90px;
  right: 24px;
  z-index: 999;
  animation: optibot-slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.optibot-modal-container {
  background: var(--optibot-white);
  border-radius: 16px;
  box-shadow: var(--optibot-shadow-lg);
  overflow: hidden;
  width: 380px;
  max-height: 600px;
  border: 1px solid var(--optibot-border);
}

.optibot-modal-header {
  background: var(--optibot-light);
  padding: 12px 16px;
  border-bottom: 1px solid var(--optibot-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.optibot-modal-body {
  padding: 0;
}

@keyframes optibot-slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.optibot-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  animation: optibot-fadeIn 0.3s ease;
}

.optibot-modal-content {
  background: var(--optibot-white);
  border-radius: 16px;
  box-shadow: var(--optibot-shadow-lg);
  overflow: hidden;
  width: 90%;
  max-width: 420px;
  max-height: 90vh;
  animation: optibot-slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.optibot-modal-header {
  background: var(--optibot-light);
  padding: 16px 20px;
  border-bottom: 1px solid var(--optibot-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.optibot-modal-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.optibot-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--optibot-secondary);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.optibot-close:hover {
  background: var(--optibot-border);
  color: var(--optibot-dark);
}

.optibot-modal-body {
  padding: 0;
  height: 500px;
}

@keyframes optibot-fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (max-width: 768px) {
  .optibot-fab-container {
    bottom: 20px;
    right: 20px;
  }
  
  .optibot-modal-overlay {
    bottom: 86px;
    right: 20px;
  }
  
  .optibot-modal-container {
    width: calc(100vw - 40px);
    max-width: 350px;
  }
  
  .optibot-chat-container {
    height: 450px;
  }
  
  .optibot-message-bubble {
    max-width: 240px;
  }
  
  .optibot-modal-content {
    width: 95%;
    max-width: 350px;
  }
}

@media (max-width: 480px) {
  .optibot-fab-container {
    bottom: 16px;
    right: 16px;
  }
  
  .optibot-fab {
    width: 48px;
    height: 48px;
  }
  
  .optibot-modal-overlay {
    bottom: 70px;
    right: 16px;
    left: 16px;
  }
  
  .optibot-modal-container {
    width: 100%;
    max-width: none;
  }
  
  .optibot-chat-container {
    height: 400px;
  }
}