import React from "react";
import { Bo<PERSON>, User } from "lucide-react";
import MessageBubble from "./MessageBubble";

const Message = ({ message, userImageURL, userFullname }) => {
  const isAI = message.from === "ai";
  const isHuman = message.from === "human";

  return (
    <div className="comment mb-4">
      <div className="d-flex align-items-start gap-3">
        {/* Avatar */}
        <div className="avatar avatar-sm">
          {isAI ? (
            <div className="avatar-frame bg-primary text-white d-flex align-items-center justify-content-center">
              <Bot size={16} />
            </div>
          ) : (
            <>
              {userImageURL ? (
                <div className="avatar-frame">
                  <img src={userImageURL} alt={userFullname || "User"} />
                </div>
              ) : (
                <div className="avatar-frame bg-light text-muted d-flex align-items-center justify-content-center">
                  <User size={16} />
                </div>
              )}
            </>
          )}
        </div>

        {/* Content */}
        <div className="flex-grow-1">
          <div className="d-flex justify-content-between mb-1">
            <small className="text-muted fw-bold">
              {isAI ? "Connect 24/7" : userFullname || "You"}
            </small>
            <small className="text-muted">
              {message.timestamp
                ? new Date(message.timestamp).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                : ""}
            </small>
          </div>

          <MessageBubble message={message} isAI={isAI} />
        </div>
      </div>
    </div>
  );
};

export default Message;
