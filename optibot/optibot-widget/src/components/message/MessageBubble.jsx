import React from "react";

const MessageBubble = ({ message, isAI }) => {
  const { content, isLoading, isError } = message;

  if (isLoading) {
    return (
      <div className="optibot-message-bubble optibot-bubble-ai">
        <div className="optibot-typing-indicator">
          <div className="optibot-typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <small className="text-muted ml-2">Connect 24/7 is typing...</small>
        </div>
      </div>
    );
  }

  return (
    <div className={`optibot-message-bubble ${isAI ? 'optibot-bubble-ai' : 'optibot-bubble-human'} ${isError ? 'optibot-bubble-error' : ''}`}>
      <div className="optibot-message-content">
        {content.split('\n').map((line, index) => (
          <React.Fragment key={index}>
            {line}
            {index < content.split('\n').length - 1 && <br />}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default MessageBubble;