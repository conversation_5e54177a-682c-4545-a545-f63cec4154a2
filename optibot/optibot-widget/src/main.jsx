import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import App from './App.jsx'
import './index.css'

function initRootApp() {
  const rootElement = document.getElementById('root');
  if (rootElement) {
    const root = ReactDOM.createRoot(rootElement);
    root.render(<App />);
  }
}

window.OptiBotChatbot = {
  init(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
      const root = ReactDOM.createRoot(container);
      root.render(<App />);
    }
  },

  createFloatingChat() {
    if (document.getElementById('optibot-floating-chat')) {
      return;
    }

    const chatContainer = document.createElement('div');
    chatContainer.id = 'optibot-floating-chat';
    document.body.appendChild(chatContainer);

    const root = ReactDOM.createRoot(chatContainer);
    root.render(<App />);
  }
}

document.addEventListener('DOMContentLoaded', () => {
  if (typeof frappe !== 'undefined') {
    window.OptiBotChatbot.createFloatingChat();
  } else {
    initRootApp();
  }
});