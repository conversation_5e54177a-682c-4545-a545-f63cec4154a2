import React, { useState, useRef, useEffect } from "react";
import { Send, Bot, User, MessageCircle, X, Minimize2 } from "lucide-react";
import Message from "./components/message/Message";

const ChatView = ({ sessionID }) => {
  const userImageURL = typeof frappe !== 'undefined' ? frappe.user?.image() : null;
  const userFullname = typeof frappe !== 'undefined' ? frappe.user?.full_name() : 'User';

  const [promptMessage, setPromptMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const messagesEndRef = useRef(null);

  const [messages, setMessages] = useState([
    {
      from: "ai",
      isLoading: false,
      content: "Hello! I'm Connect 24/7. How can I help you today?",
      timestamp: new Date()
    },
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const showToast = (message, type = 'error') => {
    setError({ message, type });
    setTimeout(() => setError(null), 5000);
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  const handleSendMessage = async () => {
    if (!promptMessage.trim().length || isLoading) {
      return;
    }

    const userMessage = promptMessage;
    setPromptMessage("");
    setIsLoading(true);

    setMessages((old) => [
      ...old,
      { 
        from: "human", 
        content: userMessage, 
        isLoading: false,
        timestamp: new Date()
      },
      { 
        from: "ai", 
        content: "", 
        isLoading: true,
        timestamp: new Date()
      },
    ]);

    try {
      let url = `${import.meta.env.VITE_BASE_URI}/api/method/optibot.api.get_chatbot_response?session_id=${sessionID}&prompt_message=${encodeURIComponent(userMessage)}`;

      const res = await fetch(url);
      if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);

      const data = await res.json();
      const message = data.message || "No response received.";

      setMessages((old) => {
        const newMessages = [...old];
        newMessages[newMessages.length - 1] = {
          from: "ai",
          content: message,
          isLoading: false,
          timestamp: new Date()
        };
        return newMessages;
      });
    } catch (e) {
      console.error(e);
      setMessages((old) => {
        const newMessages = [...old];
        newMessages[newMessages.length - 1] = {
          from: "ai",
          content: "Sorry, I encountered an error. Please try again.",
          isLoading: false,
          timestamp: new Date(),
          isError: true
        };
        return newMessages;
      });
      showToast("Something went wrong. Please check the console for details.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter" && (event.ctrlKey || event.metaKey) && promptMessage.trim()) {
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Floating Chat Button */}
      <div 
        className="optibot-floating-button"
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 1000,
          cursor: 'pointer'
        }}
      >
        <button
          className="btn btn-primary rounded-circle shadow-lg d-flex align-items-center justify-content-center"
          onClick={toggleChat}
          style={{
            width: '60px',
            height: '60px',
            border: 'none',
            transition: 'all 0.3s ease',
            transform: isChatOpen ? 'rotate(180deg)' : 'rotate(0deg)'
          }}
          title={isChatOpen ? 'Close Chat' : 'Open Chat'}
        >
          {isChatOpen ? <X size={24} /> : <MessageCircle size={24} />}
        </button>
      </div>

      {/* Chat Container */}
      <div 
        className={`optibot-chat-wrapper ${isChatOpen ? 'optibot-chat-open' : 'optibot-chat-closed'}`}
        style={{
          position: 'fixed',
          bottom: '90px',
          right: '20px',
          width: '450px',
          height: '520px',
          zIndex: 999,
          transition: 'all 0.3s ease',
          transform: isChatOpen ? 'translateY(0)' : 'translateY(100%)',
          opacity: isChatOpen ? 1 : 0,
          visibility: isChatOpen ? 'visible' : 'hidden'
        }}
      >
        <div 
          className="optibot-chat-container shadow-lg" 
          style={{
            height: '100%',
            backgroundColor: 'white',
            borderRadius: '12px',
            border: '1px solid #e0e0e0',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* Chat Header */}
          <div 
            className="optibot-header" 
            style={{
              padding: '16px 20px',
              borderBottom: '1px solid #e0e0e0',
              borderRadius: '12px 12px 0 0',
              backgroundColor: '#f8f9fa'
            }}
          >
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <Bot size={24} className="text-primary mr-2" />
                <div>
                  <h6 className="mb-0 font-weight-bold text-dark">Connect 24/7</h6>
                  <small className="text-muted">Ask me anything about your ERP system</small>
                </div>
              </div>
              <button
                className="btn btn-link p-0 text-muted"
                onClick={toggleChat}
                style={{ lineHeight: 1 }}
              >
                <Minimize2 size={18} />
              </button>
            </div>
          </div>

          {/* Error Toast */}
          {error && (
            <div className={`alert alert-${error.type === 'error' ? 'danger' : 'success'} alert-dismissible fade show optibot-toast m-3 mb-0`}>
              {error.message}
              <button 
                type="button" 
                className="close" 
                onClick={() => setError(null)}
              >
                <span>&times;</span>
              </button>
            </div>
          )}

          {/* Messages Container */}
          <div 
            className="optibot-messages-container" 
            style={{
              flex: 1,
              overflowY: 'auto',
              padding: '0 20px'
            }}
          >
            <div className="optibot-messages py-3">
              {messages.map((message, index) => (
                <Message 
                  key={`${message.content}-${index}`} 
                  message={message}
                  userImageURL={userImageURL}
                  userFullname={userFullname}
                />
              ))}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Input Container */}
          <div 
            className="optibot-input-container" 
            style={{
              padding: '16px 20px',
              borderTop: '1px solid #e0e0e0',
              borderRadius: '0 0 12px 12px'
            }}
          >
            <div className="card border-0 shadow-sm">
              <div className="card-body p-3">
                <div className="d-flex align-items-start gap-2">
                  <div className="optibot-user-avatar">
                    {userImageURL ? (
                      <img 
                        src={userImageURL} 
                        alt={userFullname}
                        className="rounded-circle"
                        width="32"
                        height="32"
                      />
                    ) : (
                      <div 
                        className="optibot-avatar-fallback d-flex align-items-center justify-content-center rounded-circle bg-secondary text-white"
                        style={{ width: '32px', height: '32px' }}
                      >
                        <User size={16} />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-grow-1">
                    <textarea
                      className="form-control border-0 resize-none"
                      rows="2"
                      value={promptMessage}
                      onChange={(event) => setPromptMessage(event.target.value)}
                      onKeyDown={handleKeyPress}
                      placeholder="Type your message here... (Ctrl/Cmd + Enter to send)"
                      disabled={isLoading}
                      style={{ outline: 'none', boxShadow: 'none' }}
                    />
                  </div>

                  <button
                    className="btn btn-primary btn-sm d-flex align-items-center justify-content-center"
                    onClick={handleSendMessage}
                    disabled={!promptMessage.trim() || isLoading}
                    style={{ width: '40px', height: '40px' }}
                  >
                    {isLoading ? (
                      <div className="spinner-border spinner-border-sm" role="status">
                        <span className="sr-only">Loading...</span>
                      </div>
                    ) : (
                      <Send size={16} />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChatView;