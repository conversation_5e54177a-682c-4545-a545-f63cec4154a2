{"name": "optibot-widget", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && cp dist/*.js ../optibot/public/js/dist/optibot-widget.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^3.21.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "bootstrap": "^5.3.7", "framer-motion": "^12.19.1", "jsdom": "^26.1.0", "lucide-react": "^0.522.0", "nanoid": "^5.1.5", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}