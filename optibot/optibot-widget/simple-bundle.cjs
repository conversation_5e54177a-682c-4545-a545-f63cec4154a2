const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// Read all three files
const htmlContent = fs.readFileSync(path.join(__dirname, 'dist/index.html'), 'utf8');
const cssContent = fs.readFileSync(path.join(__dirname, 'dist/optibot-Clz9_Cex.css'), 'utf8');
const jsContent = fs.readFileSync(path.join(__dirname, 'dist/optibot.js'), 'utf8');

// Parse HTML to extract any meta information or additional elements
const dom = new JSDOM(htmlContent);
const document = dom.window.document;
const title = document.querySelector('title')?.textContent || 'OptiBot';

// Create a bundled file that includes everything
const bundledContent = `
/**
 * OptiBot Complete Bundle
 * Generated from: index.html, optibot-Clz9_Cex.css, optibot.js
 * Title: ${title}
 */

(function() {
  'use strict';
  
  // Configuration
  const OPTIBOT_CONFIG = {
    containerId: 'optibot-widget-container',
    rootId: 'optibot-root',
    title: '${title}',
    position: 'bottom-right' // bottom-right, bottom-left, top-right, top-left
  };
  
  // Inject CSS styles
  function injectStyles() {
    if (document.getElementById('optibot-styles')) return; // Prevent duplicate injection
    
    const style = document.createElement('style');
    style.id = 'optibot-styles';
    style.textContent = \`
      /* OptiBot Styles */
      ${cssContent.replace(/`/g, '\\`').replace(/\${/g, '\\${')}
      
      /* Additional container styles for Frappe integration */
      #\${OPTIBOT_CONFIG.containerId} {
        position: fixed;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }
      
      #\${OPTIBOT_CONFIG.containerId}.bottom-right {
        bottom: 20px;
        right: 20px;
      }
      
      #\${OPTIBOT_CONFIG.containerId}.bottom-left {
        bottom: 20px;
        left: 20px;
      }
      
      #\${OPTIBOT_CONFIG.containerId}.top-right {
        top: 20px;
        right: 20px;
      }
      
      #\${OPTIBOT_CONFIG.containerId}.top-left {
        top: 20px;
        left: 20px;
      }
      
      /* Ensure it doesn't interfere with Frappe UI */
      #\${OPTIBOT_CONFIG.containerId} * {
        box-sizing: border-box;
      }
    \`;
    document.head.appendChild(style);
  }
  
  // Create the container structure
  function createContainer() {
    // Remove existing container if present
    const existingContainer = document.getElementById(OPTIBOT_CONFIG.containerId);
    if (existingContainer) {
      existingContainer.remove();
    }
    
    // Create main container
    const container = document.createElement('div');
    container.id = OPTIBOT_CONFIG.containerId;
    container.className = OPTIBOT_CONFIG.position;
    
    // Create root div for React app
    const rootDiv = document.createElement('div');
    rootDiv.id = OPTIBOT_CONFIG.rootId;
    
    container.appendChild(rootDiv);
    document.body.appendChild(container);
    
    return rootDiv;
  }
  
  // Initialize OptiBot
  function initOptiBot() {
    try {
      // Inject styles first
      injectStyles();
      
      // Create container
      const rootElement = createContainer();
      
      // Execute the main JavaScript code
      ${jsContent}
      
      console.log('OptiBot initialized successfully');
      
      // Expose global functions for external control
      window.OptiBot = {
        show: function() {
          const container = document.getElementById(OPTIBOT_CONFIG.containerId);
          if (container) container.style.display = 'block';
        },
        hide: function() {
          const container = document.getElementById(OPTIBOT_CONFIG.containerId);
          if (container) container.style.display = 'none';
        },
        toggle: function() {
          const container = document.getElementById(OPTIBOT_CONFIG.containerId);
          if (container) {
            container.style.display = container.style.display === 'none' ? 'block' : 'none';
          }
        },
        destroy: function() {
          const container = document.getElementById(OPTIBOT_CONFIG.containerId);
          if (container) container.remove();
          const styles = document.getElementById('optibot-styles');
          if (styles) styles.remove();
        },
        reinit: function() {
          this.destroy();
          setTimeout(initOptiBot, 100);
        }
      };
      
    } catch (error) {
      console.error('OptiBot initialization failed:', error);
    }
  }
  
  // Auto-initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initOptiBot);
  } else {
    // DOM is already ready
    initOptiBot();
  }
  
  // Expose init function globally for manual initialization
  window.initOptiBot = initOptiBot;
  
})();
`;

// Write the bundled file
fs.writeFileSync(path.join(__dirname, 'dist/optibot-bundle.js'), bundledContent);
console.log('Bundle created successfully: dist/optibot-bundle.js');