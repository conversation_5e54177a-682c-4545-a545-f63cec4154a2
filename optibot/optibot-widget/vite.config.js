import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        entryFileNames: 'optibot.js',
        chunkFileNames: 'optibot-[hash].js',
        assetFileNames: 'optibot-[hash].[ext]'
      }
    }
  },
  define: {
    global: 'globalThis',
  }
})