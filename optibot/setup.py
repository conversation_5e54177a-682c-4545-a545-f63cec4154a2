from setuptools import setup, find_packages

with open("requirements.txt") as f:
	install_requires = f.read().strip().split("\n")

from optibot import __version__ as version

setup(
	name="optibot",
	version=version,
	description="ChatGPT in the Desk, powered by React & OpenAI API",
	author="Geofinity Solutions",
	author_email="<EMAIL>",
	packages=find_packages(),
	zip_safe=False,
	include_package_data=True,
	install_requires=install_requires
)