import frappe
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain.memory import ConversationBufferMemory
from langchain.chains import ConversationChain, RetrievalQA
from langchain_core.prompts import PromptTemplate
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import (
    RecursiveUrlLoader, 
    SitemapLoader,
    WebBaseLoader
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor
import json
import os
import hashlib
from datetime import datetime
from urllib.parse import urlparse
import re
from enum import Enum
from datetime import date
from frappe.utils import getdate, nowdate
from optibot.methods import (
    get_monthly_attendance_summary,
    get_employee_salary_slip,
    get_employee_shift_details,
    extract_date_from_query,
)

general_prompt_template = PromptTemplate(
    input_variables=["history", "input"],
    template="""
The following is a friendly conversation between a human and an AI.
The AI is talkative and provides lots of specific details from its context. The AI's name is OptERPBot and it's birth date is 24th June, 2025.
If the AI does not know the answer to a question, it truthfully says it does not know.
Any programming code should be output in a github flavored markdown code block mentioning the programming language.

IMPORTANT: This is a general conversation mode. The AI cannot access any employee-specific information or company data.
If asked about employee information or website content, politely explain that the user needs to login first.

Current conversation:
{history}
Human: {input}
AI:""",
)

class EnhancedRAGManager:
    def __init__(self, openai_api_key: str):
        self.embeddings = OpenAIEmbeddings(api_key=openai_api_key)
        self.llm = ChatOpenAI(api_key=openai_api_key, temperature=0)
        self.vector_stores = {}
        self.cache_dir = frappe.get_site_path("private", "website_cache")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ". ", " ", ""],
            length_function=len,
        )
    
    def get_cache_key(self, website_url: str) -> str:
        """Generate cache key for website"""
        return hashlib.md5(website_url.encode()).hexdigest()
    
    def is_cache_valid(self, cache_key: str, max_age_hours: int = 24) -> bool:
        """Check if cached vector store is still valid"""
        vector_store_dir = os.path.join(self.cache_dir, f"{cache_key}_vectorstore")
        metadata_file = os.path.join(self.cache_dir, f"{cache_key}_metadata.json")
        
        if not (os.path.exists(vector_store_dir) and os.path.exists(metadata_file)):
            return False
        
        index_file = os.path.join(vector_store_dir, "index.faiss")
        pkl_file = os.path.join(vector_store_dir, "index.pkl")
        
        if not (os.path.exists(index_file) and os.path.exists(pkl_file)):
            return False
        
        try:
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            cache_time = datetime.fromisoformat(metadata.get('timestamp', ''))
            age_hours = (datetime.now() - cache_time).total_seconds() / 3600
            return age_hours < max_age_hours
        
        except Exception as e:
            print(f"Cache validation error: {e}")
            return False

    
    def save_vector_store(self, cache_key: str, vector_store: FAISS, metadata: dict):
        """Save vector store and metadata to cache using FAISS native methods"""
        vector_store_dir = os.path.join(self.cache_dir, f"{cache_key}_vectorstore")
        metadata_file = os.path.join(self.cache_dir, f"{cache_key}_metadata.json")
        
        os.makedirs(vector_store_dir, exist_ok=True)
        
        vector_store.save_local(vector_store_dir)
        
        metadata['timestamp'] = datetime.now().isoformat()
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f)
    
    def load_vector_store(self, cache_key: str) -> FAISS:
        """Load vector store from cache using FAISS native methods"""
        vector_store_dir = os.path.join(self.cache_dir, f"{cache_key}_vectorstore")
        
        return FAISS.load_local(vector_store_dir, self.embeddings, allow_dangerous_deserialization=True)
    
    def crawl_website_with_loader(self, website_url: str, max_depth: int = 2) -> list:
        documents = []
        domain = urlparse(website_url).netloc
        
        try:
            sitemap_urls = [
                f"https://{domain}/sitemap.xml",
                f"https://{domain}/sitemap_index.xml",
                f"{website_url}/sitemap.xml"
            ]
            
            for sitemap_url in sitemap_urls:
                try:
                    frappe.log_error(f"Trying sitemap: {sitemap_url}", "RAG Manager")
                    sitemap_loader = SitemapLoader(web_path=sitemap_url)
                    sitemap_docs = sitemap_loader.load()
                    if sitemap_docs:
                        frappe.log_error(f"Successfully loaded {len(sitemap_docs)} docs from sitemap", "RAG Manager")
                        documents.extend(sitemap_docs)
                        break
                except Exception as e:
                    frappe.log_error(f"Sitemap failed: {str(e)}", "RAG Manager")
                    continue
            
            if not documents:
                frappe.log_error(f"Using RecursiveUrlLoader for {website_url}", "RAG Manager")
                
                def simple_extractor(html: str) -> str:
                    """Simple content extractor"""
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    for element in soup(['script', 'style', 'nav', 'footer', 'aside', 'header']):
                        element.decompose()
                    
                    return soup.get_text(separator='\n', strip=True)
                
                recursive_loader = RecursiveUrlLoader(
                    url=website_url,
                    max_depth=max_depth,
                    extractor=simple_extractor,
                    prevent_outside_domain=True,
                    use_async=False,
                    timeout=10,
                    exclude_dirs=["/wp-admin/", "/admin/", "/login/"],
                )
                
                try:
                    documents = recursive_loader.load()
                    frappe.log_error(f"RecursiveUrlLoader loaded {len(documents)} documents", "RAG Manager")
                except Exception as e:
                    frappe.log_error(f"RecursiveUrlLoader failed: {str(e)}", "RAG Manager")
            
            if not documents:
                frappe.log_error(f"Using WebBaseLoader fallback for {website_url}", "RAG Manager")
                try:
                    web_loader = WebBaseLoader([website_url])
                    documents = web_loader.load()
                    frappe.log_error(f"WebBaseLoader loaded {len(documents)} documents", "RAG Manager")
                except Exception as e:
                    frappe.log_error(f"WebBaseLoader failed: {str(e)}", "RAG Manager")
                    raise Exception(f"All loading methods failed for {website_url}")
        
        except Exception as e:
            frappe.log_error(f"Website crawling failed: {str(e)}", "RAG Manager Error")
            raise e
        
        return documents
    
    def create_vector_store(self, website_url: str, force_refresh: bool = False) -> FAISS:
        """Create or retrieve vector store for website using standard libraries"""
        cache_key = self.get_cache_key(website_url)
        
        if cache_key in self.vector_stores and not force_refresh:
            return self.vector_stores[cache_key]
        
        if not force_refresh and self.is_cache_valid(cache_key):
            frappe.log_error(f"Loading vector store from cache: {website_url}", "RAG Manager")
            vector_store = self.load_vector_store(cache_key)
            self.vector_stores[cache_key] = vector_store
            return vector_store
        
        frappe.log_error(f"Creating new vector store for: {website_url}", "RAG Manager")
        raw_documents = self.crawl_website_with_loader(website_url)
        
        if not raw_documents:
            raise Exception(f"No content found for website: {website_url}")
        
        split_documents = self.text_splitter.split_documents(raw_documents)
        
        filtered_documents = [doc for doc in split_documents if len(doc.page_content.strip()) > 100]
        
        if not filtered_documents:
            raise Exception(f"No meaningful content found for website: {website_url}")
        
        vector_store = FAISS.from_documents(filtered_documents, self.embeddings)
        
        metadata = {
            'url': website_url,
            'document_count': len(filtered_documents),
            'created_at': datetime.now().isoformat()
        }
        self.save_vector_store(cache_key, vector_store, metadata)
        
        self.vector_stores[cache_key] = vector_store
        
        frappe.log_error(f"Created vector store with {len(filtered_documents)} chunks for {website_url}", "RAG Manager")
        return vector_store
    
    def create_enhanced_retriever(self, vector_store: FAISS, search_kwargs: dict = None):
        """Create an enhanced retriever with compression"""
        if search_kwargs is None:
            search_kwargs = {"k": 6, "fetch_k": 20}
        
        base_retriever = vector_store.as_retriever(search_kwargs=search_kwargs)
        
        compressor = LLMChainExtractor.from_llm(self.llm)
        compression_retriever = ContextualCompressionRetriever(
            base_compressor=compressor,
            base_retriever=base_retriever
        )
        
        return compression_retriever
    
    def query_website(self, website_url: str, query: str) -> str:
        """Query website content using enhanced RAG with better retrieval"""
        try:
            vector_store = self.create_vector_store(website_url)
            
            retriever = self.create_enhanced_retriever(vector_store)
            
            qa_prompt = PromptTemplate(
                template="""Use the following pieces of context to answer the question at the end. 
                If you don't know the answer based on the context, just say that you don't know, don't try to make up an answer.
                Always cite the source URLs when possible.

                Context:
                {context}

                Question: {question}

                Answer:""",
                input_variables=["context", "question"]
            )
            
            qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",
                retriever=retriever,
                return_source_documents=True,
                chain_type_kwargs={"prompt": qa_prompt}
            )
            
            result = qa_chain({"query": query})
            
            response = result['result']
            
            if result.get('source_documents'):
                sources = []
                for doc in result['source_documents']:
                    if hasattr(doc, 'metadata') and 'source' in doc.metadata:
                        sources.append(doc.metadata['source'])
                
                unique_sources = list(set(sources))[:3]  
                if unique_sources:
                    response += f"\n\nSources:\n" + "\n".join([f"- {url}" for url in unique_sources])
            
            return response
            
        except Exception as e:
            frappe.log_error(f"Enhanced RAG Query Error: {str(e)}", "RAG Manager Error")
            return f"I encountered an error while searching the website content: {str(e)}"

class BookingState(Enum):
    NONE = "none"
    COLLECTING_FIRST_NAME = "collecting_first_name"
    COLLECTING_LAST_NAME = "collecting_last_name"
    COLLECTING_EMAIL = "collecting_email"
    COLLECTING_PHONE = "collecting_phone"
    COLLECTING_MESSAGE = "collecting_message"
    CONFIRMING = "confirming"
    COMPLETED = "completed"


class AppointmentBookingManager:
    def __init__(self):
        self.booking_keywords = [
            'appointment', 'booking',  'book', 'schedule', 'reserve',
            'contact', 'get in touch', 'need help',  'assistance',
            'support',  'consultation', 'meeting', 'call', 'reach out'
        ]

        self.booking_data_fields =  {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email Address',
            'phone': 'Mobile Phone',
            'message': 'Message - How can we help? Tell us about your condition/requirement'
        }

    def extract_first_name(self, message: str) -> str:
        """Extract first name from user input"""
        message = message.strip()

        patterns = [
            r'(?:my )?(?:first )?name is (\w+)',
            r'(?:i\'?m |i am )?(\w+)',
            r'(?:it\'?s |its )?(\w+)',
            r'call me (\w+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, message.lower())
            if match:
                return match.group(1).title()

        words = message.split()
        if len(words) == 1 and words[0].isalpha():
            return words[0].title()
        elif len(words) > 1:
            for word in words:
                if word.isalpha() and len(word) >= 2:
                    return word.title()

        return message.strip().title()

    def extract_last_name(self, message: str) -> str:
        """Extract last name from user input"""
        message = message.strip()

        patterns = [
            r'(?:my )?(?:last |sur)?name is (\w+)',
            r'(?:it\'?s |its )?(\w+)',
            r'(?:my )?surname is (\w+)',
            r'family name is (\w+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, message.lower())
            if match:
                return match.group(1).title()

        words = message.split()
        if len(words) == 1 and words[0].isalpha():
            return words[0].title()
        elif len(words) > 1:
            for word in words:
                if word.isalpha() and len(word) >= 2:
                    return word.title()

        return message.strip().title()

    def extract_email(self, message: str) -> str:
        """Extract email from user input"""
        message = message.strip()

        email_pattern = r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b'
        match = re.search(email_pattern, message)

        if match:
            return match.group(0).lower()

        if '@' in message and '.' in message:
            clean_message = re.sub(r'(?:my )?(?:email (?:address )?is |email:|it\'?s )', '', message.lower())
            clean_message = clean_message.strip()

            match = re.search(email_pattern, clean_message)
            if match:
                return match.group(0).lower()

        return message.strip().lower()

    def extract_phone(self, message: str) -> str:
        """Extract phone number from user input"""
        message = message.strip()

        clean_message = re.sub(r'(?:my )?(?:phone (?:number )?is |number is |it\'?s |mobile is )', '', message.lower())
        clean_message = clean_message.strip()

        phone_patterns = [
            r'(\+?\d{1,3}[-.\s]?\(?\d{3,4}\)?[-.\s]?\d{3,4}[-.\s]?\d{3,4})', 
            r'(\(?\d{3,4}\)?[-.\s]?\d{3,4}[-.\s]?\d{3,4})', 
            r'(\d{10,15})', 
        ]

        for pattern in phone_patterns:
            match = re.search(pattern, clean_message)
            if match:
                phone = re.sub(r'[^\d+]', '', match.group(1))
                return phone

        digits_only = re.sub(r'[^\d+]', '', clean_message)
        if len(digits_only) >= 10:
            return digits_only

        return message.strip()

    def detect_booking_intent(self, message:str) -> bool:
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in self.booking_keywords)

    def get_booking_state(self, session_id: str) -> str:
        try:
            import redis
            redis_client = redis.from_url(
                frappe.conf.get("redis_cache") or 
                frappe.conf.get("redis_url") or 
                "redis://localhost:6379/0"
            )
            state = redis_client.get(f"booking_state_{session_id}")
            return state.decode() if state else BookingState.NONE.value
        except:
            return BookingState.NONE.value

    def set_booking_state(self, session_id: str, state: str):
        try:
            import redis
            redis_client = redis.from_url(
                frappe.conf.get("redis_cache") or 
                frappe.conf.get("redis_url") or 
                "redis://localhost:6379/0"
            )
            redis_client.setex(f"booking_state_{session_id}", 1800, state)
        except Exception as e:
            frappe.log_error(f"Error setting booking state: {str(e)}", "Booking Manager")

    def get_booking_data(self, session_id: str) -> dict:
        try:
            import redis
            redis_client = redis.from_url(
                frappe.conf.get("redis_cache") or 
                frappe.conf.get("redis_url") or 
                "redis://localhost:6379/0"
            )
            data = redis_client.get(f"booking_data_{session_id}")
            return json.loads(data.decode()) if data else {}
        except:
            return {}

    def set_booking_data(self, session_id: str, data: dict):
        try:
            import redis
            redis_client = redis.from_url(
                frappe.conf.get("redis_cache") or 
                frappe.conf.get("redis_url") or 
                "redis://localhost:6379/0"
            )
            redis_client.setex(f"booking_data_{session_id}", 1800, json.dumps(data))
        except Exception as e:
            frappe.log_error(f"Error setting booking data: {str(e)}", "Booking Manager")

    def validate_email(self, email: str) -> bool:
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def validate_phone(self, phone: str) -> bool:
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        return len(clean_phone) >= 10 and clean_phone.replace('+', '').isdigit()

    def create_contact_record(self, booking_data:dict) -> str:
        try:
            contact_doc = frappe.get_doc({
                'doctype': 'Get In Touch',  
                'first_name': booking_data.get('first_name'),
                'last_name': booking_data.get('last_name'),
                'email_address': booking_data.get('email'),
                'mobile_phone': booking_data.get('phone'),
                'message': booking_data.get('message'),

            })
            contact_doc.insert()
            frappe.db.commit()

            return contact_doc.name

        except Exception as e:
            frappe.log_error(f"Error creating contact record: {str(e)}", "Booking Manager")
            return None

    def clear_booking_session(self, session_id: str):
        try:
            import redis
            redis_client = redis.from_url(
                frappe.conf.get("redis_cache") or 
                frappe.conf.get("redis_url") or 
                "redis://localhost:6379/0"
            )
            redis_client.delete(f"booking_state_{session_id}")
            redis_client.delete(f"booking_data_{session_id}")
        except Exception as e:
            frappe.log_error(f"Error clearing booking session: {str(e)}", "Booking Manager")

    def process_booking_conversation(self, session_id: str, message: str) -> str:
        current_state = self.get_booking_state(session_id)
        booking_data = self.get_booking_data(session_id)

        if current_state == BookingState.NONE.value and self.detect_booking_intent(message):
            self.set_booking_state(session_id, BookingState.COLLECTING_FIRST_NAME.value)
            return "Great! I'd be happy to help you get in touch with our team. Let me collect some information from you.\n\nFirst, could you please tell me your first name?"

        if (
            message.lower()
            in [
                "cancel",
                "stop",
                "exit",
                "quit",
                "cancel appointment",
                "cancel booking",
                "cancel appointment",
                "cancel booking",
                "stop appointment",
                "stop booking",
                "exit booking"
            ]
            and current_state != BookingState.NONE.value
        ):
            self.clear_booking_session(session_id)
            return "Booking process cancelled. Is there anything else I can help you with?"

        if current_state == BookingState.COLLECTING_FIRST_NAME.value:
            first_name = self.extract_first_name(message)

            if len(first_name.strip()) < 2 or not first_name.isalpha():
                return "Please enter a valid first name (at least 2 characters, letters only)."

            booking_data['first_name'] = first_name
            self.set_booking_data(session_id, booking_data)
            self.set_booking_state(session_id, BookingState.COLLECTING_LAST_NAME.value)
            return f"Thank you, {booking_data['first_name']}! Now, what's your last name?"

        elif current_state == BookingState.COLLECTING_LAST_NAME.value:
            last_name = self.extract_last_name(message)

            if len(last_name.strip()) < 2 or not last_name.isalpha():
                return "Please enter a valid last name (at least 2 characters, letters only)."

            booking_data['last_name'] = last_name
            self.set_booking_data(session_id, booking_data)
            self.set_booking_state(session_id, BookingState.COLLECTING_EMAIL.value)
            return "Perfect! Now I need your email address so we can reach out to you."

        elif current_state == BookingState.COLLECTING_EMAIL.value:
            email = self.extract_email(message)

            if not self.validate_email(email):
                return "Please enter a valid email address (e.g., <EMAIL>)."

            booking_data['email'] = email
            self.set_booking_data(session_id, booking_data)
            self.set_booking_state(session_id, BookingState.COLLECTING_PHONE.value)
            return "Great! What's your mobile phone number?"

        elif current_state == BookingState.COLLECTING_PHONE.value:
            phone = self.extract_phone(message)

            if not self.validate_phone(phone):
                return "Please enter a valid phone number (at least 10 digits)."

            booking_data['phone'] = phone
            self.set_booking_data(session_id, booking_data)
            self.set_booking_state(session_id, BookingState.COLLECTING_MESSAGE.value)
            return "Excellent! Finally, please tell me how we can help you. What's your condition or requirement?"

        elif current_state == BookingState.COLLECTING_MESSAGE.value:
            if len(message.strip()) < 10:
                return "Please provide more details about how we can help you (at least 10 characters)."

            booking_data['message'] = message.strip()
            self.set_booking_data(session_id, booking_data)
            self.set_booking_state(session_id, BookingState.CONFIRMING.value)

            confirmation_message = f"""
                Perfect! Let me confirm your details:

                Contact Information:
                • Name: {booking_data['first_name']} {booking_data['last_name']}
                • Email: {booking_data['email']}
                • Phone: {booking_data['phone']}
                • Message: {booking_data['message']}

                Is this information correct? Please reply with 'yes' to submit or 'no' to make changes.
                """
            return confirmation_message

        elif current_state == BookingState.CONFIRMING.value:
            if message.lower() in ['yes', 'y', 'correct', 'submit', 'confirm']:
                record_id = self.create_contact_record(booking_data)

                if record_id:
                    self.set_booking_state(session_id, BookingState.COMPLETED.value)
                    success_message = f"""
                            Booking Submitted Successfully!

                            Thank you, {booking_data['first_name']}! Your contact request has been submitted with reference ID: {record_id}

                            Our team will get back to you shortly at {booking_data['email']} or {booking_data['phone']}.

                            Is there anything else I can help you with today?
                            """
                    frappe.enqueue(
                        self.clear_booking_session,
                        session_id=session_id,
                        queue='short',
                        timeout=10
                    )
                    return success_message
                else:
                    return "I'm sorry, there was an error submitting your request. Please try again or contact us directly."

            elif message.lower() in ['no', 'n', 'incorrect', 'change']:
                self.clear_booking_session(session_id)
                self.set_booking_state(session_id, BookingState.COLLECTING_FIRST_NAME.value)
                return "No problem! Let's start over. What's your first name?"

            else:
                return "Please reply with 'yes' to confirm and submit, or 'no' to make changes."

        return None


@frappe.whitelist(allow_guest=True)
def get_chatbot_response(session_id: str, prompt_message: str) -> str:
    try:
        booking_manager = AppointmentBookingManager()
        booking_response = booking_manager.process_booking_conversation(session_id, prompt_message)
        if booking_response:
            return booking_response

        current_user = frappe.session.user
        is_authenticated = current_user and current_user != "Guest"

        openai_api_key = frappe.conf.get("openai_api_key")
        if not openai_api_key:
            frappe.throw("Please set `openai_api_key` in site config")
        
        openai_model = get_model_from_settings()
        llm = ChatOpenAI(model=openai_model, temperature=0, api_key=openai_api_key)

        user_session_id = f"{session_id}_{current_user}"
        message_history = RedisChatMessageHistory(
            session_id=user_session_id,
            url=frappe.conf.get("redis_cache") or frappe.conf.get("redis_url") or "redis://localhost:6379/0",
        )
        memory = ConversationBufferMemory(memory_key="history", chat_memory=message_history)

        employee_info = None
        if is_authenticated:
            employee_info = get_employee_info(current_user)
            if not employee_info:
                is_authenticated = False

        if is_authenticated and employee_info:
            employee_id = employee_info.get('employee_id')
            website_query = detect_website_query(prompt_message)
            hr_query = detect_hr_query(prompt_message)
            prompt = None

            if website_query:
                rag_manager = EnhancedRAGManager(openai_api_key)
                website_url = website_query.get('url', 'https://spark.geofinity.com.np')
                query = website_query.get('query', prompt_message)
                rag_response = rag_manager.query_website(website_url, query)
                employee_context = format_employee_context(employee_info)
                
                template = f"""
                    The following is a friendly conversation between a human and an AI.
                    The AI is talkative and provides lots of specific details from its context. The AI's name is OptERPBot.
                    If the AI does not know the answer, it truthfully says it does not know.
                    Any programming code should be in a markdown code block.

                    {employee_context}

                    WEBSITE INFORMATION:
                    {rag_response}

                    Based on the context, answer the user's question.
                    Current conversation:
                    {{history}}
                    Human: {{input}}
                    AI:"""
                prompt = PromptTemplate(input_variables=["history", "input"], template=template)

            elif hr_query:
                topic = hr_query.get('topic')
                
                target_date = extract_date_from_query(prompt_message, nowdate())
                hr_data = None
                
                if topic == 'attendance':
                    hr_data = get_monthly_attendance_summary(employee_id, target_date)
                elif topic == 'salary':
                    hr_data = get_employee_salary_slip(employee_id, target_date)
                elif topic == 'shift':
                    hr_data = get_employee_shift_details(employee_id, target_date)
                
                employee_context = format_employee_context(employee_info)
                hr_context = format_hr_context(topic, hr_data)
                
                template = f"""
                    The following is a friendly conversation between a human and an AI.
                    The AI is talkative and provides lots of specific details from its context. The AI's name is OptERPBot.
                    If the AI does not know the answer, it truthfully says it does not know.

                    EMPLOYEE CONTEXT:
                    {employee_context}

                    SPECIFIC INFORMATION REQUESTED ({topic.title()}) for {target_date}:
                    {hr_context}

                    Based on the context above, answer the user's question about their {topic}.
                    If the requested information is not available in the context, inform the user that you couldn't find the details for that period.

                    Current conversation:
                    {{history}}
                    Human: {{input}}
                    AI:"""
                
                prompt = PromptTemplate(input_variables=["history", "input"], template=template)

            else:
                employee_context = format_employee_context(employee_info)
                template = f"""
                    The following is a friendly conversation between a human and an AI.
                    The AI is talkative and provides lots of specific details from its context. The AI's name is OptERPBot.
                    If the AI does not know the answer to a question, it truthfully says it does not know.

                    EMPLOYEE CONTEXT:
                    {employee_context}

                    IMPORTANT: You can only provide information about the logged-in employee. Never share other employees' information.
                    You can help with:
                    - Employee's own basic details, attendance, salary, and shift.
                    - Website content queries (e.g., "search website for...").
                    - Booking appointments or contact requests.
                    - General questions.

                    Current conversation:
                    {{history}}
                    Human: {{input}}
                    AI:"""
                prompt = PromptTemplate(input_variables=["history", "input"], template=template)
            
            conversation_chain = ConversationChain(llm=llm, memory=memory, prompt=prompt)
            response = conversation_chain.predict(input=prompt_message)

        else:
            enhanced_general_prompt = PromptTemplate(
                input_variables=["history", "input"],
                template="""
                The following is a friendly conversation between a human and an AI.
                The AI is talkative and provides lots of specific details from its context. The AI's name is OptERPBot.
                If the AI does not know the answer to a question, it truthfully says it does not know.

                BOOKING ASSISTANCE:
                If someone asks about appointments, bookings, or contact, you can assist them by saying:
                "I can help you get in touch with our team! Just say 'book appointment' and I'll guide you through the process."

                IMPORTANT: This is a general conversation mode. The AI cannot access any employee-specific information or company data.
                If asked about employee information or website content, politely explain that the user needs to login first.

                Current conversation:
                {history}
                Human: {input}
                AI:""",
            )
            conversation_chain = ConversationChain(llm=llm, memory=memory, prompt=enhanced_general_prompt)
            response = conversation_chain.predict(input=prompt_message)
        
        return response
        
    except Exception as e:
        frappe.log_error(f"Chatbot Error: {str(e)}", "OptERPBot Error")
        return f"I'm sorry, I encountered an error while processing your request. Error: {str(e)}"


def detect_website_query(message: str) -> dict:
    """
    Detect if the user is asking about website content
    """
    website_keywords = [
        'website', 'spark.geofinity.com.np', 'geofinity', 'spark',
        'search website', 'website content', 'site information',
        'web content', 'online content'
    ]
    
    message_lower = message.lower()
    
    for keyword in website_keywords:
        if keyword in message_lower:
            return {
                'url': 'https://spark.geofinity.com.np',
                'query': message
            }
    
    return None

def detect_hr_query(message: str) -> dict:
    """
    Detect if the user is asking about HR-related information.
    """
    message_lower = message.lower()
    
    attendance_keywords = ['attendance', 'present', 'absent', 'leave', 'late']
    salary_keywords = ['salary', 'payslip', 'pay slip', 'remuneration', 'earnings']
    shift_keywords = ['shift', 'timing', 'schedule', 'roster', 'work hours']
    
    if any(keyword in message_lower for keyword in attendance_keywords):
        return {'topic': 'attendance'}
    
    if any(keyword in message_lower for keyword in salary_keywords):
        return {'topic': 'salary'}
        
    if any(keyword in message_lower for keyword in shift_keywords):
        return {'topic': 'shift'}
        
    return None

def format_hr_context(topic: str, data: dict) -> str:
    """
    Format HR information for the AI prompt.
    """
    if not data:
        return "No specific HR information found for your query."

    context_parts = []
    
    if topic == 'attendance' and data.get('total_days', 0) > 0:
        context_parts.append("=== Your Monthly Attendance Summary ===")
        context_parts.append(f"Total Days Recorded: {data.get('total_days')}")
        context_parts.append(f"Present: {data.get('present')} days")
        context_parts.append(f"Absent: {data.get('absent')} days")
        context_parts.append(f"Half Days: {data.get('half_day')} days")

    elif topic == 'salary' and data.get('salary_slip_name'):
        context_parts.append(f"=== Your Salary Slip for {data.get('month_year')} ===")
        context_parts.append(f"Net Pay: {data.get('net_pay')}")
        context_parts.append(f"Gross Pay: {data.get('gross_pay')}")
        context_parts.append(f"Total Deduction: {data.get('total_deduction')}")
        context_parts.append(f"Payment Days: {data.get('payment_days')} days")
        context_parts.append(f"Absent Days: {data.get('absent_days')} days")

    elif topic == 'shift' and data.get('shift_type'):
        context_parts.append("=== Your Current Shift Information ===")
        context_parts.append(f"Shift Type: {data.get('shift_type')}")
        context_parts.append(f"Start Time: {data.get('start_time')}")
        context_parts.append(f"End Time: {data.get('end_time')}")
        context_parts.append(f"Assigned from {data.get('from_date')} to {data.get('to_date')}")
    
    if not context_parts:
        return f"I could not find any information regarding your {topic} for the specified period."

    return "\n".join(context_parts)

@frappe.whitelist()
def refresh_website_cache(website_url: str = None) -> str:
    """
    Manually refresh website cache - useful for admins
    """
    try:
        if not website_url:
            website_url = 'https://spark.geofinity.com.np'
        
        openai_api_key = frappe.conf.get("openai_api_key")
        if not openai_api_key:
            return "OpenAI API key not configured"
        
        rag_manager = EnhancedRAGManager(openai_api_key)
        rag_manager.create_vector_store(website_url, force_refresh=True)
        
        return f"Website cache refreshed successfully for {website_url}"
        
    except Exception as e:
        frappe.log_error(f"Cache Refresh Error: {str(e)}", "Website Cache Error")
        return f"Error refreshing cache: {str(e)}"

def get_employee_info(user_email: str) -> dict:
    """
    Get employee information for the logged-in user
    """
    try:
        frappe.log_error(f"Debug: Searching for employee with user_id: {user_email}", "OptERPBot Debug")
        
        employee_exists = frappe.db.exists("Employee", {"user_id": user_email})
        frappe.log_error(f"Debug: Employee exists check: {employee_exists}", "OptERPBot Debug")
        
        if not employee_exists:
            frappe.log_error(f"Debug: No employee found with user_id {user_email}, trying other fields", "OptERPBot Debug")
            
            employee_by_company_email = frappe.db.exists("Employee", {"company_email": user_email})
            employee_by_personal_email = frappe.db.exists("Employee", {"personal_email": user_email})
            
            frappe.log_error(f"Debug: Employee by company_email: {employee_by_company_email}", "OptERPBot Debug")
            frappe.log_error(f"Debug: Employee by personal_email: {employee_by_personal_email}", "OptERPBot Debug")
            
            if employee_by_company_email:
                employee = frappe.get_doc("Employee", {"company_email": user_email})
            elif employee_by_personal_email:
                employee = frappe.get_doc("Employee", {"personal_email": user_email})
            else:
                frappe.log_error(f"Debug: No employee found for user {user_email} in any field", "OptERPBot Debug")
                return None
        else:
            employee = frappe.get_doc("Employee", {"user_id": user_email})
        
        frappe.log_error(f"Debug: Employee found: {employee.name}", "OptERPBot Debug")
        
        employee_data = {
            "employee_id": employee.name,
            "employee_name": employee.employee_name,
            "first_name": employee.first_name,
            "last_name": employee.last_name,
            "designation": employee.designation,
            "department": employee.department,
            "company": employee.company,
            "date_of_joining": employee.date_of_joining.strftime("%B %d, %Y") if employee.date_of_joining else None,
            "employment_type": employee.employment_type,
            "status": employee.status,
            "gender": employee.gender,
            "date_of_birth": employee.date_of_birth.strftime("%B %d, %Y") if employee.date_of_birth else None,
            "personal_email": employee.personal_email,
            "company_email": employee.company_email,
            "cell_number": employee.cell_number,
        }
        
        return employee_data
        
    except frappe.DoesNotExistError:
        frappe.log_error(f"Employee not found for user: {user_email}", "OptERPBot")
        return None
    except Exception as e:
        frappe.log_error(f"Error fetching employee info: {str(e)}", "OptERPBot")
        return None

def format_employee_context(employee_info: dict) -> str:
    """
    Format employee information for the AI prompt
    """
    if not employee_info:
        return "No employee information available."
    
    context_parts = []
    
    context_parts.append("=== EMPLOYEE INFORMATION ===")
    context_parts.append(f"Employee ID: {employee_info.get('employee_id', 'N/A')}")
    context_parts.append(f"Name: {employee_info.get('employee_name', 'N/A')}")
    context_parts.append(f"Designation: {employee_info.get('designation', 'N/A')}")
    context_parts.append(f"Department: {employee_info.get('department', 'N/A')}")
    context_parts.append(f"Company: {employee_info.get('company', 'N/A')}")
    
    if employee_info.get('date_of_joining'):
        context_parts.append(f"Date of Joining: {employee_info['date_of_joining']}")
    
    if employee_info.get('employment_type'):
        context_parts.append(f"Employment Type: {employee_info['employment_type']}")
    
    if employee_info.get('status'):
        context_parts.append(f"Status: {employee_info['status']}")
    
    if employee_info.get('gender'):
        context_parts.append(f"Gender: {employee_info['gender']}")
    
    if employee_info.get('date_of_birth'):
        context_parts.append(f"Date of Birth: {employee_info['date_of_birth']}")
    
    if employee_info.get('company_email'):
        context_parts.append(f"Company Email: {employee_info['company_email']}")
    
    if employee_info.get('cell_number'):
        context_parts.append(f"Phone: {employee_info['cell_number']}")
    
    return "\n".join(context_parts)

def get_model_from_settings():
    """
    Get OpenAI model from settings
    """
    return (
        frappe.db.get_single_value("OptiBot Settings", "openai_model") or "gpt-3.5-turbo"
    )
