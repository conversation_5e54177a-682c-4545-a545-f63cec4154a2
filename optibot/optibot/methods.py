import frappe
import re
from datetime import datetime, date
from frappe.utils import getdate, get_first_day, get_last_day
from dateutil import parser
from dateutil.relativedelta import relativedelta

@frappe.whitelist()
def get_monthly_attendance_summary(employee_id, target_date):
    """
    Calculate total present, absent, and half-day attendance for an employee
    from the start of the month up to the given date.
    
    Args:
        employee_id (str): Employee ID
        target_date (str or date): Target date (format: 'YYYY-MM-DD' or date object)
    
    Returns:
        dict: Dictionary containing attendance summary with keys:
              - present: Total present days
              - absent: Total absent days  
              - half_day: Total half days
              - total_days: Total days calculated
    """
    
    if isinstance(target_date, str):
        target_date = getdate(target_date)
    elif isinstance(target_date, datetime):
        target_date = target_date.date()
    
    month_start = get_first_day(target_date)
    
    attendance_summary = {
        'present': 0,
        'absent': 0,
        'half_day': 0,
        'total_days': 0
    }
    
    try:
        attendance_records = frappe.get_all(
            'Attendance',
            filters={
                'employee': employee_id,
                'attendance_date': ['between', [month_start, target_date]],
                'docstatus': 1  
            },
            fields=['status', 'attendance_date']
        )
        
        for record in attendance_records:
            status = record.get('status', '').lower()
            
            if status == 'present':
                attendance_summary['present'] += 1
            elif status == 'absent':
                attendance_summary['absent'] += 1
            elif status == 'half day':
                attendance_summary['half_day'] += 1
        
        attendance_summary['total_days'] = len(attendance_records)
        
        return attendance_summary
        
    except Exception as e:
        frappe.log_error(f"Error calculating attendance for employee {employee_id}: {str(e)}")
        return attendance_summary

@frappe.whitelist()
def get_employee_salary_slip(employee_id, target_date=None):
    """
    Get salary slip information for an employee for a specific month
    
    Args:
        employee_id (str): Employee ID
        target_date (str or date): Target date to get salary slip for that month

    Returns:
        dict: Dictionary containing salary slip information
    """
    if not target_date:
        target_date = datetime.now().date()
    elif isinstance(target_date, str):
        target_date = getdate(target_date)
    elif isinstance(target_date, datetime):
        target_date = target_date.date()
    
    month_start = get_first_day(target_date)
    month_end = get_last_day(target_date)
    
    salary_slip_info = {
        'net_pay': 0,
        'total_working_days': 0,
        'payment_days': 0,
        'absent_days': 0,
        'gross_pay': 0,
        'total_deduction': 0,
        'salary_slip_name': None,
        'posting_date': None,
        'month_year': None
    }
    
    try:
        salary_slip = frappe.get_all(
            'Salary Slip',
            filters={
                'employee': employee_id,
                'start_date': ['>=', month_start],
                'end_date': ['<=', month_end],
                'docstatus': 1
            },
            fields=[
                'name', 'net_pay', 'total_working_days', 'payment_days', 
                'absent_days', 'gross_pay', 'total_deduction', 'posting_date'
            ],
            order_by='creation desc',
            limit=1
        )
        
        if salary_slip:
            slip = salary_slip[0]
            salary_slip_info.update({
                'net_pay': slip.get('net_pay', 0),
                'total_working_days': slip.get('total_working_days', 0),
                'payment_days': slip.get('payment_days', 0),
                'absent_days': slip.get('absent_days', 0),
                'gross_pay': slip.get('gross_pay', 0),
                'total_deduction': slip.get('total_deduction', 0),
                'salary_slip_name': slip.get('name'),
                'posting_date': slip.get('posting_date'),
                'month_year': f"{target_date.strftime('%B %Y')}"
            })
        
        return salary_slip_info
        
    except Exception as e:
        frappe.log_error(f"Error getting salary slip for employee {employee_id}: {str(e)}")
        return salary_slip_info

@frappe.whitelist()
def get_employee_shift_details(employee_id, target_date=None):
    """
    Get shift assignment and shift type details for an employee
    
    Args:
        employee_id (str): Employee ID
        target_date (str or date): Target date (not used currently, kept for future use)
    
    Returns:
        dict: Dictionary containing shift information
    """
    shift_info = {
        'shift_type': None,
        'start_time': None,
        'end_time': None,
        'shift_assignment_name': None,
        'from_date': None,
        'to_date': None,
        'status': None
    }
    
    try:
        shift_assignment = frappe.get_all(
            'Shift Assignment',
            filters={
                'employee': employee_id,
                'docstatus': 1
            },
            fields=['name', 'shift_type', 'start_date', 'end_date', 'status'],
            limit=1
        )
        
        if shift_assignment:
            assignment = shift_assignment[0]
            shift_type_name = assignment.get('shift_type')
            
            if shift_type_name:
                shift_type = frappe.get_doc('Shift Type', shift_type_name)
                
                shift_info.update({
                    'shift_type': shift_type_name,
                    'start_time': shift_type.start_time,
                    'end_time': shift_type.end_time,
                    'shift_assignment_name': assignment.get('name'),
                    'from_date': assignment.get('start_date'),
                    'to_date': assignment.get('end_date'),
                    'status': assignment.get('status')
                })
        
        return shift_info
        
    except Exception as e:
        frappe.log_error(f"Error getting shift details for employee {employee_id}: {str(e)}")
        return shift_info
    
@frappe.whitelist()
def get_comprehensive_employee_info(employee_id, target_date=None):
    """
    Get comprehensive employee information including basic details, attendance, salary, and shift
    
    Args:
        employee_id (str): Employee ID
        target_date (str or date): Target date for calculations
    
    Returns:
        dict: Comprehensive employee information
    """
    if not target_date:
        target_date = datetime.now().date()
    elif isinstance(target_date, str):
        target_date = getdate(target_date)
    elif isinstance(target_date, datetime):
        target_date = target_date.date()
    
    try:
        employee = frappe.get_doc('Employee', employee_id)
        
        attendance = get_monthly_attendance_summary(employee_id, target_date)
        
        salary_info = get_employee_salary_slip(employee_id, target_date)
        
        shift_info = get_employee_shift_details(employee_id, target_date)
        
        comprehensive_info = {
            'basic_info': {
                'employee_id': employee.name,
                'employee_name': employee.employee_name,
                'first_name': employee.first_name,
                'last_name': employee.last_name,
                'designation': employee.designation,
                'department': employee.department,
                'company': employee.company,
                'date_of_joining': employee.date_of_joining.strftime("%B %d, %Y") if employee.date_of_joining else None,
                'employment_type': employee.employment_type,
                'status': employee.status,
                'gender': employee.gender,
                'date_of_birth': employee.date_of_birth.strftime("%B %d, %Y") if employee.date_of_birth else None,
                'personal_email': employee.personal_email,
                'company_email': employee.company_email,
                'cell_number': employee.cell_number,
            },
            'attendance': attendance,
            'salary': salary_info,
            'shift': shift_info,
            'query_date': target_date.strftime("%B %d, %Y")
        }
        
        return comprehensive_info
        
    except Exception as e:
        frappe.log_error(f"Error getting comprehensive employee info for {employee_id}: {str(e)}")
        return None
    

@frappe.whitelist()
def extract_date_from_query(user_input, current_date=None):
    """
    Extract date from user query and return in YYYY-MM-DD format.
    If no date is mentioned, returns current date.
    """
    if current_date is None:
        current_date = nowdate()
    
    if isinstance(current_date, str):
        current_date = datetime.strptime(current_date, '%Y-%m-%d').date()
    elif isinstance(current_date, datetime):
        current_date = current_date.date()
    
    user_input_lower = user_input.lower()
    
    date_keywords = ['january', 'february', 'march', 'april', 'may', 'june',
                    'july', 'august', 'september', 'october', 'november', 'december',
                    'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec',
                    'this year', 'last year', 'this month', 'last month', 'today', 'yesterday']
    
    if not any(keyword in user_input_lower for keyword in date_keywords) and not re.search(r'\d{4}|\d{1,2}th|\d{1,2}st|\d{1,2}nd|\d{1,2}rd', user_input_lower):
        return current_date.strftime('%Y-%m-%d')
    
    try:
        
        month_year_pattern = r'(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s*(?:of\s*)?(?:(\d{4})|(this\s+year)|(last\s+year))'
        match = re.search(month_year_pattern, user_input_lower)
        
        if match:
            month_str = match.group(1)
            year_str = match.group(2)
            this_year = match.group(3)
            last_year = match.group(4)
            
            month_map = {
                'january': 1, 'jan': 1, 'february': 2, 'feb': 2, 'march': 3, 'mar': 3,
                'april': 4, 'apr': 4, 'may': 5, 'june': 6, 'jun': 6,
                'july': 7, 'jul': 7, 'august': 8, 'aug': 8, 'september': 9, 'sep': 9,
                'october': 10, 'oct': 10, 'november': 11, 'nov': 11, 'december': 12, 'dec': 12
            }
            
            month = month_map.get(month_str)
            
            if year_str:
                year = int(year_str)
            elif this_year:
                year = current_date.year
            elif last_year:
                year = current_date.year - 1
            else:
                year = current_date.year
            
            return f"{year}-{month:02d}-01"
        
        day_month_year_pattern = r'(\d{1,2})(?:st|nd|rd|th)?\s*(?:of\s*)?(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s*(?:of\s*)?(?:(\d{4})|(this\s+year)|(last\s+year))?'
        match = re.search(day_month_year_pattern, user_input_lower)
        
        if match:
            day = int(match.group(1))
            month_str = match.group(2)
            year_str = match.group(3)
            this_year = match.group(4)
            last_year = match.group(5)
            
            month_map = {
                'january': 1, 'jan': 1, 'february': 2, 'feb': 2, 'march': 3, 'mar': 3,
                'april': 4, 'apr': 4, 'may': 5, 'june': 6, 'jun': 6,
                'july': 7, 'jul': 7, 'august': 8, 'aug': 8, 'september': 9, 'sep': 9,
                'october': 10, 'oct': 10, 'november': 11, 'nov': 11, 'december': 12, 'dec': 12
            }
            
            month = month_map.get(month_str)
            
            if year_str:
                year = int(year_str)
            elif this_year:
                year = current_date.year
            elif last_year:
                year = current_date.year - 1
            else:
                year = current_date.year
            
            return f"{year}-{month:02d}-{day:02d}"
        
        if 'this month' in user_input_lower:
            return f"{current_date.year}-{current_date.month:02d}-01"
        elif 'last month' in user_input_lower:
            last_month_date = current_date - relativedelta(months=1)
            return f"{last_month_date.year}-{last_month_date.month:02d}-01"
        
        if 'today' in user_input_lower:
            return current_date.strftime('%Y-%m-%d')
        
        if 'yesterday' in user_input_lower:
            yesterday = current_date - relativedelta(days=1)
            return yesterday.strftime('%Y-%m-%d')
        
        try:
            parsed_date = parser.parse(user_input, fuzzy=True, default=datetime(current_date.year, current_date.month, current_date.day))
            return parsed_date.strftime('%Y-%m-%d')
        except:
            pass
            
    except Exception as e:
        print(f"Date parsing error: {e}")
    
    return current_date.strftime('%Y-%m-%d')