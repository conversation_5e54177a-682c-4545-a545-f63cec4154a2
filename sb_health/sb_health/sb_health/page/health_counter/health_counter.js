class CounterWorkStation extends BaseWorkStation {
    constructor(wrapper, templatesPath, templates, layout_html, page_name) {
        super(wrapper, templatesPath, templates, page_name);
        this.layout_html = layout_html;
        this.profiler=false;
        this.selectedPatient = "";
        this.checkCounterStatus();
    }

    checkCounterStatus() {
        frappe.call({
            method: "opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.check_opening_entry",
            args: {
                cashier: frappe.session.user
            },
            callback: (r) => {
                if (r.message && r.message.length) {
                    this.initializeWorkstation();
                } else {
                    this.showOpeningDialog();
                }
            }
        });
    }

    showOpeningDialog() {
		const me = this;
		const table_fields = [
			{
				fieldname: "mode_of_payment",
				fieldtype: "Link",
				in_list_view: 1,
				label: __("Mode of Payment"),
				options: "Mode of Payment",
				reqd: 1,
			},
			{
				fieldname: "opening_amount",
				fieldtype: "Currency",
				in_list_view: 1,
				label: __("Opening Amount"),
				options: "company:company_currency",
				onchange: function () {
					dialog.fields_dict.balance_details.df.data.some((d) => {
						if (d.idx == this.doc.idx) {
							d.opening_amount = this.value;
							dialog.fields_dict.balance_details.grid.refresh();
							return true;
						}
					});
				},
			},
		];
		const fetch_pos_payment_methods = () => {
			const pos_profile = dialog.fields_dict.pos_profile.get_value();
			if (!pos_profile) return;
			frappe.db.get_doc("POS Profile", pos_profile).then(({ payments }) => {
				dialog.fields_dict.balance_details.df.data = [];
				payments.forEach((pay) => {
					const { mode_of_payment } = pay;
					dialog.fields_dict.balance_details.df.data.push({ mode_of_payment, opening_amount: "0" });
				});
				dialog.fields_dict.balance_details.grid.refresh();
			});
		};
        const pos_profile_query = () => {
			return {
				query: "erpnext.accounts.doctype.pos_profile.pos_profile.pos_profile_query",
				filters: { company: dialog.fields_dict.company.get_value() },
			};
		};
		const dialog = new frappe.ui.Dialog({
			title: __("Create Counter Opening Entry"),
			static: true,
			fields: [
				{
					fieldtype: "Link",
					label: __("Company"),
					default: frappe.defaults.get_default("company"),
					options: "Company",
					fieldname: "company",
					reqd: 1,
				},
                {
					fieldtype: "Link",
					label: __("POS Profile"),
					options: "POS Profile",
					fieldname: "pos_profile",
					reqd: 1,
					get_query: () => pos_profile_query(),
					onchange: () => fetch_pos_payment_methods(),
				},
				{
					fieldname: "balance_details",
					fieldtype: "Table",
					label: __("Opening Balance Details"),
					cannot_add_rows: false,
					in_place_edit: true,
					reqd: 1,
					data: [],
					fields: table_fields,
				},
			],
			primary_action: async function ({ pos_profile, company, balance_details }) {
				if (!balance_details.length) {
					frappe.show_alert({
						message: __("Please add Mode of payments and opening balance details."),
						indicator: "red",
					});
					return frappe.utils.play_sound("error");
				}

				// Filter balance details for empty rows
				balance_details = balance_details.filter((d) => d.mode_of_payment);
                
				frappe.call({
					method: "opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.create_opening_entry",
					args: { pos_profile, company, balance_details },
					callback: (r) => {
						if (r.message) {
							frappe.call({
								method: "frappe.client.submit",
								args: {
									doc: r.message,
								},
								callback: (r) => {
									if (r.message) {
										frappe.show_alert({
											message: __("Counter opened successfully"),
											indicator: "green",
										});

										// Initialize the workstation after counter is opened
										me.initializeWorkstation();
									}
								},
							});
						}
					},
				});
				dialog.hide();
			},
			primary_action_label: __("Submit"),
		});

		dialog.show();

		// Don't allow closing this dialog
		dialog.$wrapper.find(".modal-header .btn-modal-close").hide();
    }

    async initializeWorkstation() {
        this.actions = {
			"#show-new-patient": () => this.renderNewForm("Patient", "#content-section"),
			"#book-appointment": () =>
				this.renderNewForm("Patient Appointment", "#content-section"),
			"#quick-patient-appointment": () =>
				this.renderNewForm("Quick Patient Appointment", "#content-section", {
					updates: { appointment_type: "specialist" },
				}),
			"#show-appointment": () =>
				this.loadSection("appointment_list", "#content-section", {
					doctype: "Patient Appointment",
					filters: { patient: this.selectedPatient },
				}),
			"#new-emergency-ipd": () => {
				this.renderNewForm("Emergency", "#content-section");
			},
            "#show-emergency": () =>
				this.loadSection("emergency_list", "#content-section", {
					doctype: "Emergency",
					filters: { patient: this.selectedPatient },
			}),
			"#new-billing": () => {
				this.renderNewForm("Sales Invoice", "#content-section");
			},
            "#new-self-referral-service": () => {
				this.renderNewForm("Self Referral Service", "#content-section");
			},
            "#all-service-requests": () =>
                this.loadSection("service_request_list", "#content-section", {
                    doctype: "Service Request",
                    filters: { patient: this.selectedPatient },
            }),
            "#new-return": () => {
				this.renderNewForm("Sales Invoice", "#content-section", 
                    {doc_vals: {is_return: 1, patient: this.selectedPatient}}
                );
			},
			"#show-invoices": () =>
				this.loadSection("invoice_list", "#content-section", {
					doctype: "Sales Invoice",
					filters: { patient: this.selectedPatient },
				}),
			"#show-payments": () =>
				this.loadSection("payment_list", "#content-section", {
					doctype: "Payment Entry",
					filters: {"party": this.selectedPatient},
				}),
			"#show-booking-requests": () =>
				this.loadSection("booking_request_list", "#content-section", {
					doctype: "Patient Appointment Request",
					filters: {},
				}),
			"#show-patients": () =>
				this.loadSection("patient_list", "#content-section", {
					doctype: "Patient",
					filters: {},
				}),
			"#close-counter": () => this.closeCounter(),
		};

        this.set_up_key_bindings();
        this.initPage("Counter Work Station");
        this.initSelectedPatient();
        this.initPatientField();
        this.renderNewForm("Sales Invoice", "#content-section", {
            company: frappe.defaults.get_default("company"),
        });
        // this.loadSection("default","#content-section")
        this.attachGlobalEventHandlers();
    }

    closeCounter() {
        frappe.call({
            method: "opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.check_opening_entry",
            args: {
                cashier: frappe.session.user
            },
            callback: (r) => {
                if (r.message && r.message.length) {
                    frappe.call({
                        method: "opterp_app.opterp_app.doctype.counter_opening_entry.counter_opening_entry.make_closing_entry",
                        args: {
                            source_name: r.message[0].name
                        },
                        callback: (r) => {
                            if (r.message) {
                                frappe.model.sync(r.message);
                                frappe.set_route("Form", "Counter Closing Entry", r.message.name);
                            }
                        }
                    });
                } else {
                    frappe.msgprint(__("No open counter found for the current user"));
                }
            }
        });
    }

    renderLayout() {
        super.renderLayout(this.layout_html);
        this.loadSection("button_container", "#button-section");
    }

    initSelectedPatient() {
        if (!$("#selectedPatientValue").length) {
            $("<div>", {
                id: "selectedPatientValue",
                text: "No patient selected",
                "data-value": "",
                style: "display: none;"
            }).appendTo("body");
        }
    }

    initPatientField() {
        const formSection = document.getElementById("form-section");
        if (!formSection) return console.error("Error: form-section not found");

        this.patientField = frappe.ui.form.make_control({
            parent: formSection,
            df: {
                fieldtype: "Link",
                options: "Patient",
                label: "Select Patient",
                fieldname: "patient",
                change: () => this.setSelectedPatient(this.patientField.get_value()),
            },
            render_input: true
        });
        this.patientField.refresh();
    }

    setSelectedPatient(patient) {
        if (this.selectedPatient === patient) return;
    
            this.selectedPatient = patient;
            $("#selectedPatientValue").text(patient).attr("data-value", patient);

        if (this.patientField && this.patientField.get_value() !== patient) {
            this.patientField.set_value(patient);
        }
    
        this.updateSectionsAfterSelection();
    }

    enableProfiler(){
        this.profiler = true;
    }

    updateSectionsAfterSelection() {
        let balance = 0;
        const me = this;

        frappe.call({
            method: "sb_health.methods.patient.patient_balance",
            args: { patient_id: this.selectedPatient },
            callback: function(response) {
                if (response.message) {
                    balance = response.message;
                }
                me.loadSection("patient_preview", "#patient-preview-section", {
                    selectedPatient: me.selectedPatient,
                    balance: balance
                });

            if (me.currentForm){
                me.currentForm.set_value("patient", me.selectedPatient);
            }
            else {
                if (me.lastRenderingFunction) me.lastRenderingFunction();
                }
            }
        });
    }

    applyFormContext(doc, doctype, context) {
        if (context) {
            Object.assign(doc, context);
        }
        return doc;
    }

    apply_doc_data(targetDoc, doc_data) {
        if (!doc_data || !targetDoc) return;
    
        const meta = frappe.get_meta(targetDoc.doctype);
        
        for (let field in doc_data) {
            if (field === "doctype" || field === "name") continue;
    
            const field_meta = meta.fields.find(f => f.fieldname === field);
    
            if (Array.isArray(doc_data[field]) && field_meta && field_meta.fieldtype === "Table") {
                targetDoc[field] = [];
    
                doc_data[field].forEach(child_row_data => {
                    const child = frappe.model.add_child(targetDoc, field_meta.options, field);
                    Object.assign(child, child_row_data);
                });
    
            } else {
                targetDoc[field] = doc_data[field];
            }
        }
    }

    async renderListView(doctype, section) {
        $(section).empty();
    
        const tempWrapper = $('<div></div>').appendTo(section);
    
        const page = frappe.ui.make_app_page({
            parent: tempWrapper[0],
            title: `${doctype} List`,
            single_column: true,
        });
    
        page.page = page.wrapper;
        page.container = page.wrapper;
    
        await frappe.model.with_doctype(doctype);
    
        const listView = new frappe.views.WorkstationListView({
            doctype: doctype,
            parent: page.wrapper,
            custom_setting: "some_value"
        });
    
        await listView.show();
    }
}


const page_name = "health-counter";

frappe.pages[page_name].on_page_load = function (wrapper) {

    $("#navbar-search").hide();
    $(".search-icon").hide();

	const templates = {
		default: "health_counter.html",
		button_container: "button_container.html",
		patient_list: "patient_list.html",
		appointment_list: "appointment_list.html",
		invoice_list: "invoice_list.html",
		emergency_list: "emergency_list.html",
		payment_list: "payment_list.html",
		patient_preview: "patient_preview.html",
		booking_request_list: "booking_request_list.html",
        service_request_list: "service_request_list.html",
	};
	const templatesPath = "sb_health/templates/pages/counter/";
	const layout_html = `
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 col-md-2" id="button-section-container">
                    <div 
                        id="button-section" 
                        class="d-flex flex-column justify-content-start align-items-start p-1"
                        style="
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(12px);
                            border-left: 1px solid rgba(255, 255, 255, 0.2);
                            height: auto;
                        "
                    >
                    </div>
                </div>

                <div class="col-12 col-md-7">
                    <div class="card p-3">
                        <div class="health-sections" id="content-section"></div>
                    </div>
                </div>

                <div class="col-12 col-md-3">
                    <div class="col">
                        <div class="section-buttons" id="form-section"></div>
                        <div class="section-buttons" id="patient-preview-section"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    window.workstationInstance = new CounterWorkStation(
        wrapper, 
        templatesPath, 
        templates,
        layout_html,
        page_name
    );

    window.workstationInstance.beforeRouteChange = function(...route) {
        window.workstationInstance.removePageStyles();
    };
};

frappe.pages[page_name].on_page_show = function(wrapper){
    window.workstationInstance.renderLayout();
}

const originalSetRoute = frappe.set_route;

frappe.set_route = function (...args) {
    console.log("frappe.set_route called with args:", args);
	try {
		if (typeof window.workstationInstance?.beforeRouteChange === "function") {
			// window.workstationInstance.beforeRouteChange(...args);
			if (args.length === 1) {

				const [, raw_doctype, docname] = args[0].split("/").filter(Boolean); 
				const doctype = raw_doctype
					.split("-")
					.map(part => part.charAt(0).toUpperCase() + part.slice(1))
					.join(" ");

				window.workstationInstance.renderForm(doctype, decodeURIComponent(docname), "#content-section");
				return;
			}
			else if (args.length === 3 && args[0] === "List") {
				window.workstationInstance.loadSection(args[1], "#content-section");
				return;
			}
			else if (args.length === 3 && args[0] === "Form") {
				window.workstationInstance.renderNewForm(args[1], "#content-section");
				return;
			} else {
				console.warn("Unsupported route format", args);
			}
		}
	} catch (e) {
		console.warn("beforeRouteChange threw an error", e);
	}

	return originalSetRoute.apply(this, args);
};
