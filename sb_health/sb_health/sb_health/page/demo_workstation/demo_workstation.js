class DemoWorkStation extends BaseWorkStation {
	constructor(wrapper, templatesPath, templates, layout_html, page_name) {
		super(wrapper, templatesPath, templates, page_name);
		this.layout_html = layout_html;
		this.selectedPatient = "";

		this.actions = {
	"#billing": () => this.renderList("Sales Invoice", "#content-section"),
	"#patient_info": () => this.renderNewForm("Patient", "#content-section"),
};


		this.initPage("Demo Workstation");
		this.attachGlobalEventHandlers();
	}

	initPage(title) {
	this.page = frappe.ui.make_app_page({
		parent: this.wrapper,
		title: title,
		single_column: false,
	});

	// Immediately detach the page-head
	const $pageHead = $(this.wrapper).find('.page-head');
	if ($pageHead.length) {
		$pageHead.detach();
		this.detachedPageHead = $pageHead; // save it for later use
	}

	this.bindNotification();
	this.renderLayout();
}

	renderLayout() {
		super.renderLayout(this.layout_html);
		this.loadSection("button_container", "#button-section");
	}

	renderList(doctype, sectionSelector = "#content-section", filters = {}) {
		const $section = $(sectionSelector);

		if (!$section.length) {
			console.error(`Section "${sectionSelector}" not found`);
			return;
		}

		$section.empty();
		const $listContainer = $('<div id="list-view-container" style="min-height: 400px;"></div>');
		$section.append($listContainer);

		if (this.detachedPageHead?.length) {
			this.detachedPageHead.detach();
			$section.before(this.detachedPageHead);
		}

		const page = this.page;
		if (!page.sidebar) {
			page.sidebar = $(
				'<div class="layout-side-section" style="width: 250px; border-left: 1px solid #ddd;"></div>'
			).appendTo(page.wrapper);
		}
		page.sidebar.css({
			background: '#fff',
			overflowY: 'auto',
			height: '100%',
		});

		$listContainer[0].page = page;

		frappe.model.with_doctype(doctype, () => {
			const list_view = new frappe.views.ListView({
				doctype,
				parent: $listContainer[0],
				page,
				filters,
			});
			list_view.show();
		});
	}


}

const page_name = "demo-workstation";

frappe.pages[page_name].on_page_load = function (wrapper) {
	const templates = {
		"button_container": "button_container.html",
		"patient_preview": "patient_preview.html",
	};
	const templatesPath = "sb_health/templates/pages/demo_workstation/";
	const layout_html = (`
		<div class="container-fluid">
			<div class="row">
				<div class="col-12 col-md-2" id="button-section-container">
					<div id="button-section" class="d-flex flex-column justify-content-start align-items-start p-1"
						style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(12px);
						border-left: 1px solid rgba(255, 255, 255, 0.2); height: auto;">
					</div>
				</div>

				<div class="col-12 col-md-9" id="content-section"></div>

				<div class="col-12 col-md-1">
					<div class="col">
						<div class="section-buttons" id="form-section"></div>
						<div class="section-buttons" id="patient-preview-section"></div>
					</div>
				</div>
			</div>
		</div>
	`);

	window.workstationInstance = new DemoWorkStation(
		wrapper,
		templatesPath,
		templates,
		layout_html,
		page_name
	);

	const original_set_route = frappe.set_route;
	frappe.set_route = async function (...args) {
		removeCustomLayoutStyles();
		return await original_set_route.apply(this, args);
	};

	function removeCustomLayoutStyles() {
		const styleIds = ["page-body", "layout-main"];
		styleIds.forEach((id) => {
			const styleTag = document.getElementById(id);
			if (styleTag) styleTag.remove();
		});
	}

	window.workstationInstance.beforeRouteChange = function (...route) {
		window.workstationInstance.removePageStyles();
	};
};


frappe.pages[page_name].on_page_show = function (wrapper) {
	window.workstationInstance.renderLayout();
};

const originalSetRoute = frappe.set_route;

frappe.set_route = function (...args) {
	try {
		if (typeof window.workstationInstance?.beforeRouteChange === "function") {
			if (args.length === 1) {
				const [, raw_doctype, docname] = args[0].split("/").filter(Boolean);
				const doctype = raw_doctype
					.split("-")
					.map(part => part.charAt(0).toUpperCase() + part.slice(1))
					.join(" ");

				window.workstationInstance.renderForm(doctype, decodeURIComponent(docname), "#content-section");
				return;
			} else if (args.length === 3 && args[0] === "List") {
				window.workstationInstance.renderList(args[1], "#content-section");
				return;
			} else if (args.length === 3 && args[0] === "Form") {
				window.workstationInstance.renderNewForm(args[1], "#content-section", {});
				return;
			} else {
				console.warn("Unsupported route format", args);
			}
		}
	} catch (e) {
		console.warn("beforeRouteChange threw an error", e);
	}

	return originalSetRoute.apply(this, args);
};


