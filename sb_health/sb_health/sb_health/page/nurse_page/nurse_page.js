
class NurseWorkStation extends BaseWorkStation {
	constructor(wrapper, templatesPath, templates, layout_html, page_name) {
		super(wrapper, templatesPath, templates, page_name);
		this.layout_html = layout_html;
		this.selectedPatient = "";
        this.preloadDoctypes = ["Sales Invoice", "Payment Entry"];
		this.formManager = new FormManager(this.preloadDoctypes, "#content-section");

		this.actions = {
			"#take-vitals": () => this.renderNewForm("Vital Signs", "#content-section"),
			"#list-vitals": () =>
				this.loadSection("vitals_list", "#content-section", {
					doctype: "Vital Signs",
					filters: { patient: this.selectedPatient },
				}),
			"#make-encounter": () => this.renderNewForm("Patient Encounter", "#content-section"),
			"#list-encounters": () =>
				this.loadSection("encounter_list", "#content-section", {
					doctype: "Patient Encounter",
					filters: { 
						patient: this.selectedPatient, 
						medical_department: this.medical_department
					},
				}),
			"#make-appointment": () =>
				this.renderNewForm("Patient Appointment", "#content-section"),
			"#show-appointment": () =>
				this.loadSection("patient_appointment_list", "#content-section", {
					doctype: "Patient Appointment",
					filters: { 
						patient: this.selectedPatient,
						department: this.medical_department
					 },
					 med: this.medical_department
				}),
			"#new-emergency-ipd": () => {
				this.renderNewForm("Emergency", "#content-section");
			},
            "#show-emergency": () =>
				this.loadSection("emergency_list", "#content-section", {
					doctype: "Emergency",
					filters: { patient: this.selectedPatient },
			}),
			"#make-ipr": () => this.renderNewForm("Inpatient Record", "#content-section"),
			"#list-iprs": () =>
				this.loadSection("ipr_list", "#content-section", {
					doctype: "Inpatient Record",
					filters: {medical_department: this.medical_department},
				}),
			"#new-material-request": () =>
				this.renderNewForm("Material Request", "#content-section"),
			"#new-delivery-note": () => this.renderNewForm("Delivery Note", "#content-section"),
			"#ipd-transfer": () => this.renderNewForm("Transfer Request", "#content-section"),
			"#all-ipd-transfer": () =>
				this.loadSection("ipd_transfer_list", "#content-section", {
					doctype: "Transfer Request",
					filters: {},
				}),
			"#set-medical-department": () => sb_health.nurse.openNurseOpeningDialog().then(
				(dep)=>{
					this.setMedicalDepartment(dep)
				})
		};

		sb_health.nurse.selectNursingProfile((medical_department) => {
			this.initPage("Nurse Work Station");
			this.initSelectedPatient();
			this.initPatientField();
			this.setMedicalDepartment(medical_department)
			this.attachGlobalEventHandlers();
		});
	}

	setMedicalDepartment(medical_department) {
		this.medical_department = medical_department;

		frappe.show_alert({
			message: __("Currently in <b>{0}</b> department.", [medical_department]),
			indicator: "green",
		});

		const pageHead = document.querySelector('.page-head');

		if (pageHead) {
			pageHead.removeAttribute('style');
			pageHead.style.display = 'flex';
			pageHead.style.flexDirection = 'column';
			pageHead.style.alignItems = 'center';

			let deptDiv = pageHead.querySelector('.selected-medical-department');

			if (!deptDiv) {
				deptDiv = document.createElement('div');
				deptDiv.className = 'selected-medical-department p-2 m-2';
				pageHead.appendChild(deptDiv);
			}

			deptDiv.innerHTML = `
				<div class="text-muted" style="font-size: 18px; display: flex; align-items: center;">
					<i class="fa fa-map-marker m-1" aria-hidden="true"></i>
					<span>${frappe.utils.escape_html(medical_department)}</span>
				</div>
			`;
		}
	}

	renderLayout() {
		super.renderLayout(this.layout_html);
		this.loadSection("button_container", "#button-section");
	}

	initSelectedPatient() {
		if (!$("#selectedPatientValue").length) {
			$("<div>", {
				id: "selectedPatientValue",
				text: "No patient selected",
				"data-value": "",
				style: "display: none;",
			}).appendTo("body");
		}
	}

	initPatientField() {
		const formSection = document.getElementById("form-section");
		if (!formSection) return console.error("Error: form-section not found");

		this.patientField = frappe.ui.form.make_control({
			parent: formSection,
			df: {
				fieldtype: "Link",
				options: "Patient",
				label: "Select Patient",
				fieldname: "patient",
				change: () => this.setSelectedPatient(this.patientField.get_value()),
			},
			render_input: true,
		});
		this.patientField.refresh();
	}


	setSelectedPatient(patient) {
		if (this.selectedPatient === patient) return;

		this.selectedPatient = patient;
		$("#selectedPatientValue").text(patient).attr("data-value", patient);

		if (this.patientField && this.patientField.get_value() !== patient) {
			this.patientField.set_value(patient);
		}

		this.updateSectionsAfterSelection();
	}

	updateSectionsAfterSelection() {
		this.loadSection("patient_preview", "#patient-preview-section", {
			selectedPatient: this.selectedPatient,
		});
		
		if (this.currentForm){
			// this is bad approach 
			// Check for valid field type using Link
			// And have a map of all the field names keyed by the doctype(allowing only req fields to be updated)
			this.currentForm.set_value("patient", this.selectedPatient);
			this.currentForm.refresh_field("patient");
        }
        else {
            if (this.lastRenderingFunction) this.lastRenderingFunction();
        }
	}

}


const page_name = "nurse-page";
frappe.pages[page_name].on_page_load = function (wrapper) {

	$("#navbar-search").hide();
    $(".search-icon").hide();
	
    const templates = {
        "button_container": "button_container.html",
        "patient_preview": "patient_preview.html",
        "encounter_list": "encounter_list.html",
        "vitals_list": "vitals_list.html",
		"emergency_list": "emergency_list.html",
        "patient_appointment_list": "patient_appointment_list.html",
        "material_request_list": "material_request_list.html",
        "ipd_transfer_list": "ipd_transfers_list.html",
        "ipr_list": "ipr_list.html",
        "patient_preview_appointment_list": "components/appointment_list.html",

    };
    const templatesPath = "sb_health/templates/pages/nurse/";
    const layout_html = (`
        <div class="container-fluid">
            <div class="row justify-content-center">
            <div class="col-12 col-md-2" id="button-section-container">
                    <div 
                        id="button-section" 
                        class="d-flex flex-column justify-content-start align-items-start p-1"
                        style="
                            background: rgba(255, 255, 255, 0.1);
                            backdrop-filter: blur(12px);
                            border-left: 1px solid rgba(255, 255, 255, 0.2);
                            height: auto;
                        "
                    >
                    </div>
                </div>
                
                <div class="col-12 col-md-7" id="content-section-container">
                    <div class="card p-3">
                        <div class="health-sections" id="content-section"></div>
                    </div>
                </div>

                 <div class="col-12 col-md-3" id="side-section-container">
                    <div class="col">
                        <div class="section-buttons" id="form-section"></div>
                        <div class="section-buttons" id="patient-preview-section"></div>
                    </div>
                </div>
    
            </div>
        </div>
    `);

	frappe.provide("sb_health.nurse");
    window.workstationInstance = new NurseWorkStation(
        wrapper, 
        templatesPath, 
        templates,
        layout_html,
        page_name
	);
	
	const original_set_route = frappe.set_route;

	frappe.set_route = async function (...args) {
		removeCustomLayoutStyles();
		return await original_set_route.apply(this, args);
	};

	function removeCustomLayoutStyles() {
		const styleIds = ["page-body", "layout-main"];
		styleIds.forEach((id) => {
			const styleTag = document.getElementById(id);
			if (styleTag) {
				styleTag.remove();
			}
		});
	};
		
    window.workstationInstance.beforeRouteChange = function(...route) {
        window.workstationInstance.removePageStyles();
    };
};

frappe.pages[page_name].on_page_show = function(wrapper){
    window.workstationInstance.renderLayout();
}

const originalSetRoute = frappe.set_route;

frappe.set_route = function (...args) {
	try {
		if (typeof window.workstationInstance?.beforeRouteChange === "function") {
			// window.workstationInstance.beforeRouteChange(...args);
			if (args.length === 1) {

				const [, raw_doctype, docname] = args[0].split("/").filter(Boolean); 
				const doctype = raw_doctype
					.split("-")
					.map(part => part.charAt(0).toUpperCase() + part.slice(1))
					.join(" ");

				window.workstationInstance.renderForm(doctype, decodeURIComponent(docname), "#content-section");
				return;
			}
			else if (args.length === 3 && args[0] === "List") {
				window.workstationInstance.loadSection(args[1], "#content-section");
				return;
			}
			else if (args.length === 3 && args[0] === "Form") {
				window.workstationInstance.renderNewForm(args[1], "#content-section",{});
				return;
			} else {
				console.warn("Unsupported route format", args);
			}
		}
	} catch (e) {
		console.warn("beforeRouteChange threw an error", e);
	}

	return originalSetRoute.apply(this, args);
};



