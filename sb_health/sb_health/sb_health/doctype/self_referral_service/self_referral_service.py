# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from datetime import date
from dateutil.relativedelta import relativedelta

def get_dob_from_age(age):
    try:
        age = int(age)
        if age < 0 or age > 125:
            raise ValueError("Age must be between 0 and 125.")
    except (ValueError, TypeError):
        frappe.throw("Invalid age. Please enter a number between 0 and 125.")

    today = date.today()
    dob = today - relativedelta(years=age)
    return dob


class SelfReferralService(Document):
	def delete(self):
		pass
	
	def db_insert(self, *args, **kwargs):
		pass

	def load_from_db(self):
		pass

	def db_update(self):
		pass

	@staticmethod
	def get_list(args):
		pass

	@staticmethod
	def get_count(args):
		pass

	@staticmethod
	def get_stats(args):
		pass


@frappe.whitelist()
def quick_request(data):
    try:
        data = frappe._dict(frappe.parse_json(data))
    except Exception as e:
        frappe.throw(_("Invalid data format: {0}").format(str(e)))
    
    if not data.patient:
        missing = []

        if not data.patient_name:
            missing.append("Patient Name")
        if not data.sex:
            missing.append("Sex")
        if not data.age:
            missing.append("Age")

        if missing:
            frappe.throw(_("The following fields are mandatory: {0}").format(", ".join(missing)))

        try:
            name_parts = data.patient_name.strip().split()
            first_name = name_parts[0]
            
            if len(name_parts) == 2:
                middle_name = ""
                last_name = name_parts[1]
            elif len(name_parts) >= 3:
                middle_name = " ".join(name_parts[1:-1])
                last_name = name_parts[-1]
            else:
                middle_name = ""
                last_name = ""
            
            if data.age:
                try:
                    age = int(data.age)
                except (ValueError, TypeError):
                    frappe.throw("Age must be a number.")

                if age < 0 or age > 125:
                    frappe.throw("Age must be between 0 and 125.")
                
        except Exception as e:
            frappe.throw(_("Error parsing patient name: {0}").format(str(e)))

        try:
            patient = frappe.get_doc({
                "doctype": "Patient",
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "sex": data.sex,
                "mobile": data.get("mobile_no"),
                "dob": get_dob_from_age(data.age)
            })
            patient.insert(ignore_permissions=True)
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Quick Admit - Patient Insert Error")
            frappe.throw(_("Failed to create patient: {0}").format(str(e)))
    else:
        patient = frappe.get_doc("Patient", data.patient)

    try:
        status = frappe.db.get_value(
			"Code Value",
			{
				"code_system": "Request Status",
				"code_value": "Active"
			},
			"name"
		)
        
        service_request = frappe.get_doc({
            "doctype": "Service Request",
            "patient": patient.name,
            "order_date": data.get("order_date"),
            "order_time": data.get("order_time"),
            "service_unit": data.get("service_unit"),
            "status": status,
            "company": data.get("company") or frappe.defaults.get_user_default("Company"),
            "template_dt": data.get("template_dt"),
            "template_dn": data.get("template_dn")
        })
        service_request.insert(ignore_permissions=True)
        service_request.submit()
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Quick Admit - Appointment Insert Error")
        frappe.throw(_("Failed to request service: {0}").format(str(e)))

    return {"patient": patient.name, "service_request": service_request.name}

