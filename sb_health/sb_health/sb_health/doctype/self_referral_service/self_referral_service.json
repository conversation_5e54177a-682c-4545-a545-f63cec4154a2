{"actions": [], "allow_rename": 1, "creation": "2025-06-17 11:22:49.145423", "doctype": "DocType", "engine": "InnoDB", "field_order": ["patient", "patient_name", "sex", "mobile_no", "age", "column_break_ajsi", "order_time", "order_date", "template_dt", "template_dn"], "fields": [{"fieldname": "patient", "fieldtype": "Link", "label": "Patient", "options": "Patient"}, {"fieldname": "sex", "fieldtype": "Link", "in_list_view": 1, "label": "Gender", "options": "Gender", "reqd": 1}, {"fieldname": "mobile_no", "fieldtype": "Phone", "label": "Mobile"}, {"fieldname": "patient_name", "fieldtype": "Data", "in_list_view": 1, "label": "Patient Name", "reqd": 1}, {"fieldname": "column_break_ajsi", "fieldtype": "Column Break"}, {"fieldname": "order_time", "fieldtype": "Time", "label": "Order Time"}, {"fieldname": "order_date", "fieldtype": "Date", "label": "Order Date"}, {"fieldname": "age", "fieldtype": "Int", "label": "Age", "non_negative": 1}, {"fieldname": "template_dt", "fieldtype": "Link", "label": "Order Template Type", "options": "DocType", "reqd": 1, "search_index": 1}, {"fieldname": "template_dn", "fieldtype": "Dynamic Link", "label": "Order Template", "options": "template_dt", "reqd": 1, "search_index": 1}], "index_web_pages_for_search": 1, "is_virtual": 1, "links": [], "modified": "2025-06-17 14:12:59.859868", "modified_by": "Administrator", "module": "SB Health", "name": "Self Referral Service", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}