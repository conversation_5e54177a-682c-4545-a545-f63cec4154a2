{"actions": [], "allow_rename": 1, "autoname": "format: PAR-{healthcare_practitioner}-{patient_first_name}-{####}", "creation": "2025-05-08 10:15:45.864546", "doctype": "DocType", "engine": "InnoDB", "field_order": ["healthcare_practitioner", "practitioner_name", "status", "service_unit", "column_break_vwnk", "department", "appointment_date", "appointment_time", "section_break_vzoo", "patient_first_name", "patient_last_name", "mobile", "patient_sex", "patient_age", "dob", "email_id", "column_break_zgiu", "patient", "appointment"], "fields": [{"fieldname": "healthcare_practitioner", "fieldtype": "Link", "in_standard_filter": 1, "label": "Health Practitioner", "options": "Healthcare Practitioner", "reqd": 1}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Medical Department"}, {"fieldname": "appointment_date", "fieldtype": "Date", "in_list_view": 1, "label": "Appointment Date", "reqd": 1}, {"fieldname": "patient_first_name", "fieldtype": "Data", "ignore_user_permissions": 1, "in_standard_filter": 1, "label": "Patient First Name", "search_index": 1}, {"fieldname": "patient_last_name", "fieldtype": "Data", "ignore_user_permissions": 1, "in_standard_filter": 1, "label": "Patient Last Name"}, {"fieldname": "patient_sex", "fieldtype": "Link", "label": "Gender", "options": "Gender"}, {"fieldname": "patient_age", "fieldtype": "Int", "label": "Patient Age", "non_negative": 1}, {"fieldname": "email_id", "fieldtype": "Data", "label": "Email Address"}, {"fieldname": "mobile", "fieldtype": "Phone", "in_list_view": 1, "label": "Mobile"}, {"default": "Open", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Open\nBooked\nClosed", "read_only": 1, "reqd": 1}, {"fieldname": "appointment_time", "fieldtype": "Time", "in_list_view": 1, "label": "Appointment Time", "reqd": 1}, {"fieldname": "practitioner_name", "fieldtype": "Data", "label": "Practitioner Name", "read_only": 1}, {"fieldname": "column_break_vwnk", "fieldtype": "Column Break"}, {"fieldname": "service_unit", "fieldtype": "Link", "label": "Service Unit", "options": "Healthcare Service Unit"}, {"fieldname": "section_break_vzoo", "fieldtype": "Section Break", "label": "Patient Details"}, {"fieldname": "dob", "fieldtype": "Date", "label": "Date of Birth"}, {"fieldname": "patient", "fieldtype": "Link", "label": "Patient", "options": "Patient", "read_only": 1}, {"fieldname": "appointment", "fieldtype": "Link", "label": "Appointment", "options": "Patient Appointment"}, {"fieldname": "column_break_zgiu", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-01 12:03:59.589209", "modified_by": "Administrator", "module": "SB Health", "name": "Patient Appointment Request", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}