import frappe
from frappe.model.document import Document
import re

def clean_title(title):
    """
    Clean title by removing unsafe URL characters but preserve spaces, dashes, dots, and parentheses.
    """
    return re.sub(r'[^\w\s\-().]', '', title)

class News(Document):
    def before_insert(self):
        self.route = self.generate_unique_route()

    def before_save(self):
        if self.has_value_changed("title") or not self.route:
            self.route = self.generate_unique_route()

    def generate_unique_route(self):
        cleaned_title = clean_title(self.title.strip())
        base_route = f"news/{cleaned_title}"
        route = base_route
        i = 1
        while frappe.db.exists("News", {"route": route, "name": ["!=", self.name]}):
            route = f"{base_route} ({i})"
            i += 1
        return route
