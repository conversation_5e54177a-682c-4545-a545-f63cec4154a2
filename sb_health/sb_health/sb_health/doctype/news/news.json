{"actions": [], "allow_rename": 1, "autoname": "field:title", "creation": "2025-07-03 12:03:26.748655", "doctype": "DocType", "engine": "InnoDB", "field_order": ["title", "content", "published_date", "route", "image"], "fields": [{"fieldname": "title", "fieldtype": "Data", "label": "Title", "unique": 1}, {"fieldname": "content", "fieldtype": "Text Editor", "label": "Content"}, {"fieldname": "published_date", "fieldtype": "Datetime", "label": "Published Date"}, {"fieldname": "route", "fieldtype": "Data", "hidden": 1, "label": "Route", "read_only": 1, "unique": 1}, {"fieldname": "image", "fieldtype": "Attach", "label": "image"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-03 14:42:12.247971", "modified_by": "Administrator", "module": "SB Health", "name": "News", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}