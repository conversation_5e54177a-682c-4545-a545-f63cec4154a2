{% block filter %}
    <meta id="data"
          data-filters="{{ all_filters }}"
          data-no-of-pages="{{ no_of_pages }}" />
    <button class="button-filter-bottom"
            onclick="showPopup()"
            style="position: fixed !important;
                   bottom: 200px;
                   right: 20px;
                   width: 70px;
                   height: 70px;
                   border-radius: 50%;
                   border: none;
                   background-color: #01810c;
                   color: white;
                   display: flex;
                   align-items: center;
                   justify-content: center;
                   z-index: 1000">
        <svg xmlns="http://www.w3.org/2000/svg"
             class="icon"
             width="24"
             height="24"
             viewBox="0 0 24 24"
             fill="white"
             style="background-color: #eee">
            <path d="M3 5h18v2H3V5zm4 6h10v2H7v-2zm2 6h6v2h-6v-2z" />
        </svg>
    </button>
    <div id="filters-drawer"
         class="filters-drawer position-fixed bg-white w-100 d-lg-none"
         style="z-index: 999;
                display: none">
        <div class="flex align-items-center py-4 px-6 border-bottom">
            <p class="text-18 font-weight-bold mb-0">{{ _("Filters") }}</p>
            <div name="close-filters-drawer" class="ml-auto" onclick="closePopup()">
                <svg class="icon icon-lg">
                    <use href="#icon-close"></use>
                </svg>
            </div>
        </div>
        <div class="px-6 pt-6 flex-grow-1 overflow-auto">
            {% for name, values in all_filters.items() %}
                <div class="mb-6">
                    <p class="font-weight-bold mb-4">{{ name.title() | replace('_', ' ') }}</p>
                    {% for value in values %}
                        <div class="form-group form-check">
                            <input id="{{ 'mobile-' ~ value }}"
                                   name="{{ name }}"
                                   value="{{ value }}"
                                   class="form-check-input mobile-filters"
                                   type="checkbox"
                                   role="button" />
                            <label class="form-check-label align-top"
                                   for="{{ 'mobile-' ~ value }}"
                                   role="button">{{ value }}</label>
                        </div>
                    {% endfor %}
                </div>
            {% endfor %}
        </div>
        <div class="flex align-items-center py-4 border-top">
            <a name="clear-filters"
               class="text-17 text-center w-50 mx-6"
               role="button">{{ _("Clear All") }}</a>
            <a id="apply-filters" class="btn btn-primary btn-lg w-50 mx-6">{{ _("Apply") }}</a>
        </div>
    </div>
    <div id="overlay"
         class="overlay d-lg-none"
         onclick="closePopup()"
         style="display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 998"></div>
    <div class="row no-gutters">
        <div class="col-lg-2 text-15 d-none d-lg-block px-4"
             style="border-right: 1px solid #019414;
                    padding: 0 20px">
            <div class="flex align-items-center">
                <p class="text-18 font-weight-bold mb-0">{{ _("Filters") }}</p>
                <a name="clear-filters" class="ml-auto" role="button">{{ _("Clear All") }}</a>
            </div>
            <hr class="mb-6" style="background: #0fc200" />
            {% for name, values in all_filters.items() %}
                <div class="mb-6 px-4">
                    {% if name == 'health_practitioner_type' %}
                        <p class="font-weight-bold mb-4"
                           style="font-size: 1.2rem;
                                  text-transform: capitalize">Health Practitioner Type</p>
                    {% else %}
                        <p class="font-weight-bold mb-4"
                           style="font-size: 1.2rem;
                                  text-transform: capitalize">{{ name }}</p>
                    {% endif %}
                    {% for value in values %}
                        <div class="form-group form-check"
                             style="align-items: center;
                                    display: flex;
                                    gap: 10px">
                            <input id="{{ 'desktop-' ~ value }}"
                                   name="{{ name }}"
                                   value="{{ value }}"
                                   class="form-check-input desktop-filters"
                                   type="checkbox"
                                   role="button" />
                            <label class="form-check-label align-top"
                                   for="{{ 'desktop-' ~ value }}"
                                   role="button"
                                   style="font-size: 1.3rem">{{ value }}</label>
                        </div>
                    {% endfor %}
                </div>
            {% endfor %}
        </div>
    {% endblock %}
    <script>
		function showPopup() {
			document.getElementById("filters-drawer").style.display = "block";
			document.getElementById("overlay").style.display = "block";
		}

		function closePopup() {
			document.getElementById("filters-drawer").style.display = "none";
			document.getElementById("overlay").style.display = "none";
		}
    </script>
</div>
