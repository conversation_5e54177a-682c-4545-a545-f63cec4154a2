{% set actions = [
    {
        'id': 'quick-patient-appointment',
        'text': 'Appointment',
        'key': 'Ctrl+Shift+H',
        'iconClass': 'fa fa-calendar-plus-o',
        'group': 'Appointments'
    },
    {
        'id': 'show-appointment',
        'text': 'All Appointments',
        'key': 'Ctrl+Shift+H',
        'iconClass': 'fa fa-list',
        'group': 'Appointments'
    },
    {
        'id': 'show-booking-requests',
        'text': 'Appointment Requests',
        'key': 'Ctrl+Shift+B',
        'iconClass': 'fa fa-envelope',
        'group': 'Appointments'
    },
    {
        'id': 'new-emergency-ipd',
        'text': 'Emergency',
        'key': 'Ctrl+Shift+I',
        'iconClass': 'fa fa-ambulance',
        'group': 'Admissions'
    },
        {
        'id': 'show-emergency',
        'text': 'Emergency List',
        'key': 'Ctrl+Shift+I',
        'iconClass': 'fa fa-list',
        'group': 'Admissions'
    },
    {
        'id': 'new-billing',
        'text': 'Billing',
        'key': 'Ctrl+Shift+I',
        'iconClass': 'fa fa-credit-card',
        'group': 'Billing'
    },
     {
        'id': 'new-return',
        'text': 'Return',
        'key': 'Ctrl+Shift+D',
        'iconClass': 'fa fa-reply',
        'group': 'Billing'
    },
    {
        'id': 'show-invoices',
        'text': 'Invoices',
        'key': 'Ctrl+Shift+S',
        'iconClass': 'fa fa-files-o',
        'group': 'Billing'
    },
    {
        'id': 'show-payments',
        'text': 'Payments',
        'key': 'Ctrl+Shift+D',
        'iconClass': 'fa fa-dollar',
        'group': 'Billing'
    }
    ,
    {
        'id': 'new-self-referral-service',
        'text': 'Patient Request',
        'key': 'Ctrl+Shift+D',
        'iconClass': 'fa fa-male',
        'group': 'Requests'
    },
    {
        'id': 'all-service-requests',
        'text': 'All Patient Requests',
        'key': 'Ctrl+Shift+D',
        'iconClass': 'fa fa-list',
        'group': 'Requests'
    }
] %}

{% include "templates/pages/nav_bar_button_container_template.html" %}


    
