{% set patient = frappe.get_doc("Patient", selectedPatient) if selectedPatient else None %}
{% set balance = balance | float %}
<div class="card p-3">
   {% if patient %}
   <div>
      <div class="d-flex justify-content-between align-items-center mb-1">
         <div class="d-flex align-items-center">
            {% if patient.image %}
               <img src="{{ patient.image }}" class="img-fluid rounded-circle" alt="Patient Photo"
                     style="width: 30px; height: 30px; margin-right:10px;">
            {% else %}
               <div class="rounded-circle d-flex align-items-center justify-content-center bg-secondary text-white"
                     style="width: 30px; height: 30px; font-size: 10px; font-weight: bold; margin-right:10px;">
                     {{ patient.first_name[0] if patient.first_name else '?' }}
                     {{ patient.last_name[0] if patient.last_name else '' }}
               </div>
            {% endif %}
            <h4 class="mb-0 ms-2">
               {% if patient.first_name %}
                  {{ patient.first_name }} 
               {% endif %}
               {% if patient.middle_name %}
                  {{ patient.middle_name  }} 
               {% endif %} 
               {% if patient.last_name %}
                  {{ patient.last_name }}
               {% endif %}
            </h4>
         </div>
         <i class="fa fa-print text-muted" style="cursor: pointer;" 
            onclick="print_patient_card(window.workstationInstance.selectedPatient)">
         </i>
         <i class="fa fa-refresh text-muted" style="cursor: pointer;" 
            onclick="window.workstationInstance.updateSectionsAfterSelection()">
         </i>
      </div>

      <div class="overflow-auto mb-2" style="white-space: nowrap;">
         <ul class="nav nav-tabs flex-nowrap" id="patientTabs" style="min-width: max-content;">
            <li class="nav-item">
               <button class="nav-link active" onclick="switchTab('details')">Details</button>
               
            </li>
             <li class="nav-item">
               <button class="nav-link active" onclick="switchTab('card')">Print</button>
               
            </li>
         </ul>
      </div>

      <div id="tab-content">
         <div id="tab-details" class="card m-2 p-3"> 
            {% set patient_fields = [
               ('ID', patient.name),
               ('Gender', patient.sex or 'Not Available'),
               ('DOB', patient.dob),
               ('Mobile', patient.mobile or 'Not Available'),
               ('Email', patient.email or 'Not Available'),
               ('IP Status', patient.inpatient_status or 'Not Admitted')
            ] %}

            {% for label, value in patient_fields %}
               <div class="row mb-2">
                  <div class="col-4"><strong>{{ label }}</strong></div>
                  <div class="col-8">
                     {% if label == 'DOB' %}
                        {% if value %}
                           {{ value.strftime('%Y-%m-%d') }} ({{ age_from_date(value) }} yrs)
                        {% else %}
                           Not Available
                        {% endif %}
                     {% elif label == 'IP Status' and patient.inpatient_status %}
                        <span class="badge rounded-pill bg-secondary text-light" style="opacity: 0.7; font-size: 12px; padding: 6px 10px;">
                           {{ value }}
                        </span>
                     {% else %}
                        {{ value }}
                     {% endif %}
                  </div>
               </div>
            {% endfor %}


               <hr>
            <div class="d-flex">
               <p><strong>{{ "Balance" if balance >= 0 else "Outstanding" }}:</strong> NPR {{ "{:,.2f}".format(abs(balance)) }}</p>
            </div>
            <button class="btn btn-sm mx-1" 
                    onclick='window.workstationInstance.renderNewForm("Payment Entry", "#content-section")'>
               Make Deposit
            </button>

            {% set last_ip = get_last_inpatient_record(patient.name) %}
            {% set existing_gp = get_gatepass_for_inpatient(last_ip) %}
            <div id="gate-pass" class="m-2">
               {% if last_ip and not existing_gp %}
                  <div class="alert alert-info d-flex justify-content-between align-items-center p-2">
                     <span>No Gate Pass issued for the recent discharge.</span>
                     <button class="btn btn-sm btn-secondary" onclick="createGatePass('{{ last_ip }}')">
                        Create Gate Pass
                     </button>
                  </div>
               {% elif existing_gp %}
                  <div class="alert alert-success d-flex justify-content-between align-items-center p-2">
                     <span>Gate Pass Already issued for the recent discharge.</span>
                     <button class="btn btn-sm btn-outline-light" onclick="print_in_new_page('Gatepass', '{{ existing_gp }}')" target="_blank">
                        <i class="fa fa-print text-muted" style="cursor: pointer;"></i> GatePass
                    </button>
                  </div>
               {% else %}
                  <div class="alert alert-warning p-2 mb-0">
                     No inpatient record found for this patient.
                  </div>
               {% endif %}
            </div>
         </div>

         <div id="tab-card" class="card m-2 p-3" style="display: none;">
            <div class="row">
               <div class="col-md-6 mb-2">
                     <button class="btn btn-outline-success w-100" onclick="print_patient_card(window.workstationInstance.selectedPatient)"> 
                        Patient Card
                     </button>
               </div>
               <div class="col-md-6 mb-2">
                     <button class="btn btn-outline-success w-100" onclick="print_opd_card(window.workstationInstance.selectedPatient)"> 
                        OPD Card
                     </button>
               </div>
               <div class="col-md-6 mb-2">
                     <button class="btn btn-outline-primary w-100" onclick="print_discharge_bill()"> 
                        Discharge Bill
                     </button>
               </div>
            </div>
         </div>

         <div id="tab-items" style="display: none;">
            <div class="d-flex gap-3">
               <div class="w-50">
                  <p class="text-muted">Select an item to view details:</p>
                  <ul class="list-group" id="items-list">
                     <li class="list-group-item text-muted">Loading...</li>
                  </ul>
               </div>
               <div class="w-50">
                  <h6>Item Details</h6>
                  <div id="item-details" class="text-muted">
                     Select an item to see its details.
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   {% else %}
      <p class="text-muted">No patient selected or patient data unavailable.</p>
   {% endif %}
</div>

<script>
   function switchTab(tabId) {
      const tabs = document.querySelectorAll("#patientTabs .nav-link");
      tabs.forEach(btn => btn.classList.remove("active"));
      const activeBtn = [...tabs].find(btn => btn.textContent.toLowerCase().includes(tabId));
      if (activeBtn) activeBtn.classList.add("active");

      document.getElementById("tab-details").style.display = tabId === "details" ? "block" : "none";
      document.getElementById("tab-items").style.display = tabId === "items" ? "block" : "none";
      document.getElementById("tab-card").style.display = tabId === "card" ? "block" : "none";

      if (tabId === "items") loadItemsToInvoice();
   }

   function createGatePass(inpatient_record) {
      frappe.call({
         method: "opterp_health.opterp_health.doctype.gatepass.gatepass.create_gate_pass",
         args: { inpatient_record },
         callback: function (r) {
            if (r.message && !r.message.error) {
               frappe.msgprint("Gate Pass created successfully");
               window.workstationInstance.updateSectionsAfterSelection();
            } else if (r.message && r.message.error) {
               frappe.msgprint("Error: " + r.message.error);
            } else {
               frappe.msgprint("Could not create gate pass.");
            }
         }
      });
   }

   function print_discharge_bill() {
      frappe.call({
         method: "frappe.client.get_list",
         args: {
            doctype: "Sales Invoice",
            filters: {
               patient: window.workstationInstance.selectedPatient,
               outstanding_amount: [">", 0],
               docstatus: 1
            },
            fields: ["name"],
            limit_page_length: 1000
         },
         callback: function(response) {
            if (response.message && response.message.length > 0) {
               let invoiceNames = response.message.map(inv => inv.name);
               const url = `/api/method/sb_health.sb_health.print_format.discharge_bill.discharge_bill.render_discharge_bill_pdf?sales_invoices=${encodeURIComponent(JSON.stringify(invoiceNames))}&patient=${encodeURIComponent(window.workstationInstance.selectedPatient)}`;
               window.open(url, '_blank');
            } else {
               frappe.msgprint("No unpaid or partly paid Sales Invoices found.");
            }
         }
      });
   }

function print_patient_card(patient_name) {
   // get the default print images
	const print_format = "Patient";

	const pdf_options = JSON.stringify({
		"page-height": "25mm",
		"page-width": "50mm"
	});

	const w = window.open(
		"/api/method/frappe.utils.print_format.download_pdf?" +
			"doctype=" + encodeURIComponent("Patient") +
			"&name=" + encodeURIComponent(patient_name) +
			"&format=" + encodeURIComponent(print_format) +
			"&no_letterhead=1" +
			"&options=" + encodeURIComponent(pdf_options)
      );
   }


   function print_opd_card(patient_name){
      frappe.db.get_value("Print Format", { "doc_type": "Appointment"}, "name").then((res) => {
         let defaultFormat = res.message ? res.message.name : 'Default'; 
         frappe.utils.print("Appointment", patient_name, defaultFormat);
      });
   }

</script>


