{% set actions = actions or [] %}
{% set grouped_actions = {} %}

{% for action in actions %}
  {% set group_name = action.group if action.group else 'Actions' %}
  {% if group_name not in grouped_actions %}
    {% set _ = grouped_actions.update({ group_name: [] }) %}
  {% endif %}
  {% set _ = grouped_actions[group_name].append(action) %}
{% endfor %}

<div class="doc-nav-container w-100">
  <!-- <div class="w-100 text-center mb-2">
    <i id="toggle-sidebar-btn" class="fa fa-angle-double-left" style="cursor: pointer;"></i>
  </div> -->

  {% for group, group_actions in grouped_actions.items() %}
    <div class="mb-1 nav-group-wrapper">
      <div
        class="doc-nav-group-header d-flex align-items-center justify-content-between"
        data-bs-target="#collapse-{{ loop.index }}"
      >
        <span class="nav-group-text">{{ group }}</span>
        <i class="fa fa-angle-down button-icon p-2 toggle-icon me-1"></i>
      </div>
      <div class="collapse show mt-1" id="collapse-{{ loop.index }}">
        {% for action in group_actions %}
          <div
            class="doc-nav-item d-flex align-items-center"
            id="{{ action.id }}"
            {% if action.key %}
              data-bs-toggle="tooltip"
              title="{{ action.key }}"
            {% endif %}
          >
            <i class="{{ action.iconClass or 'fa fa-circle' }} button-icon p-2"></i>
            <span class="nav-item-text">{{ action.text }}</span>
          </div>
        {% endfor %}
      </div>
    </div>
  {% endfor %}
</div>

<script>
  $(function () {
    $('[data-bs-toggle="tooltip"]').tooltip();

    function triggerAction(actionId) {
      $('.doc-nav-item').removeClass('selected');
      $('#' + actionId).addClass('selected');
    }

    $('.doc-nav-item').on('click', function () {
      const actionId = $(this).attr('id');
      triggerAction(actionId);
    });

    $('.doc-nav-group-header').on('click', function () {
      const target = $(this).attr('data-bs-target');
      $(target).collapse('toggle');
      const icon = $(this).find('.toggle-icon');
      icon.toggleClass('fa-angle-down fa-angle-left');
    });

    $('#toggle-sidebar-btn').on('click', function () {
      const container = $('#button-section-container');
      const content = $('#content-section-container');

      container.toggleClass('collapsed');

      const icon = $(this);
      if (container.hasClass('collapsed')) {
        icon.removeClass('fa-angle-double-left').addClass('fa-angle-double-right');

        container.removeClass('col-md-2').addClass('col-md-1');
        content.removeClass('col-md-7').addClass('col-md-8');
      } else {
        icon.removeClass('fa-angle-double-right').addClass('fa-angle-double-left');

        container.removeClass('col-md-1').addClass('col-md-2');
        content.removeClass('col-md-8').addClass('col-md-7');
      }
    });

    window.triggerAction = triggerAction;
  });
</script>

<style>
  .doc-nav-container {
    max-width: 250px;
    padding: 0.2rem 0.2rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    transition: width 0.3s ease;
  }

  #button-section-container.collapsed {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    overflow-x: hidden;
  }

  #button-section-container.collapsed .nav-group-text,
  #button-section-container.collapsed .nav-item-text,
  #button-section-container.collapsed .doc-nav-group-header .toggle-icon {
    display: none !important;
  }

  #button-section-container.collapsed .collapse {
    display: block !important;
    height: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 0.25rem;
  }

  #button-section-container.collapsed .doc-nav-group-header,
  #button-section-container.collapsed .doc-nav-item {
    justify-content: center !important;
  }

  #button-section-container.collapsed .doc-nav-group-header i,
  #button-section-container.collapsed .doc-nav-item i {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .doc-nav-item,
  .doc-nav-group-header {
    padding: 0.2rem 1rem;
    cursor: pointer;
    font-size: 13px;
    color: #333;
    border-left: 3px solid transparent;
    transition: background-color 0.2s, border-color 0.2s;
    user-select: none;
  }

  .doc-nav-item:hover,
  .doc-nav-group-header:hover {
    background-color: #f5f5f5;
  }

  .doc-nav-item.selected {
    border-left-color: #00ff55;
    background-color: #e6fff0a5;
    font-weight: 510;
    color: #012712;
  }

  .button-icon {
    font-size: 1.2rem;
    color: #5f6160e1;
    transition: color 0.2s;
    margin-right: 1rem;
  }

  .doc-nav-group-header {
    font-weight: 600;
    border-radius: 4px;
    text-transform: capitalize;
  }

  .toggle-icon {
    width: 1rem;
    text-align: center;
    margin-right: 0.5rem;
  }

  .collapse.show {
    margin-bottom: 0.5rem;
  }

  .mb-1 {
    margin-bottom: 0.5rem !important;
  }

  #toggle-sidebar-btn {
    user-select: none;
    border-radius: 4px;
    font-size: 1.2rem;
  }
</style>
