<button class="btn btn-secondary btn-sm" onclick="patient_history()">View History</button>
<script>
    function getIconForDoctype(doctype) {
        switch (doctype) {
            case 'Vital Signs': return 'fa fa-stethoscope';
            case 'Patient Encounter': return 'fa fa-user-md';
            case 'Lab Test': return 'fa fa-flask';
            case 'Medication': return 'fa fa-medkit';
            default: return 'fa fa-file-text';
        }
    }

    function patient_history() {
        const filters = {
            name: window.workstationInstance.selectedPatient,
            start: 0,
            page_length: 1000
        };

        frappe.call({
            method: 'healthcare.healthcare.page.patient_history.patient_history.get_feed',
            args: filters,
            callback: function (r) {
                const contentSection = document.getElementById('content-section');
                contentSection.innerHTML = '';

                if (!r.message || r.message.length === 0) {
                    contentSection.innerHTML = `
                        <div class="d-flex justify-content-center mt-4">
                            <div class="alert alert-info text-center">
                                No history found for this patient.
                            </div>
                        </div>
                    `;

                    return;
                }

                const grouped = {};
                r.message.forEach(item => {
                    if (!grouped[item.reference_doctype]) {
                        grouped[item.reference_doctype] = [];
                    }
                    grouped[item.reference_doctype].push(item);
                });

                const tabKeys = Object.keys(grouped);
                if (tabKeys.length === 0) return;

                let navTabs = `<ul class="nav nav-tabs" role="tablist">`;
                let tabContents = `<div class="tab-content mt-3">`;

                tabKeys.forEach((doctype, idx) => {
                    const safeId = doctype.replace(/\s+/g, '-').toLowerCase();

                    navTabs += `
                        <li class="nav-item">
                            <a class="nav-link ${idx === 0 ? 'active' : ''}" id="tab-${safeId}" data-toggle="tab" href="#pane-${safeId}" role="tab" onclick="event.preventDefault(); $('#tab-${safeId}').tab('show');">
                            ${doctype}
                            </a>
                        </li>
                    `;

                    const itemsHTML = grouped[doctype].map(item => `
                        <div class="card mb-3 p-3 shadow-sm">
                            <div class="card-body d-flex align-items-start gap-2">
                                <i class="${getIconForDoctype(item.reference_doctype)} text-muted fa-lg mt-1 m-2"></i>
                                <div>
                                    <p class="mb-1">
                                        <strong>${item.reference_doctype}</strong> with ID 
                                            <span class="text-primary" style="cursor: pointer;" onclick="window.workstationInstance.renderForm('${item.reference_doctype}', '${item.reference_name}', '#content-section')">
                                            ${item.reference_name}
                                        </span>
                                        taken on <strong>${item.communication_date || 'Unknown date'}</strong>.
                                    </p>
                                    <p class="mb-0 small text-muted">
                                        ${item.subject ? item.subject : '<em>No additional details.</em>'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `).join('');

                    tabContents += `
                        <div class="tab-pane fade ${idx === 0 ? 'show active' : ''}" id="pane-${safeId}" role="tabpanel" aria-labelledby="tab-${safeId}">
                            ${itemsHTML}
                        </div>
                    `;
                });

                navTabs += `</ul>`;
                tabContents += `</div>`;

                contentSection.innerHTML = navTabs + tabContents;
            }
        });
    }
</script>
<style>
.nav-tabs .nav-link.active {
    color:black;
    border-color: #ffffff #ffffff #038e43;
}

.nav-tabs .nav-link:hover {
    color: #000;
}
</style>
