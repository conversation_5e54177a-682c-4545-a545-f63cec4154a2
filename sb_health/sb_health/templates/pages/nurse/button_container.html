{% set actions = [
    {
        'id': 'make-appointment',
        'text': 'Make Appointment',
        'iconClass': 'fa fa-calendar-plus-o',
        'group': 'Consultation'
    },
    {
        'id': 'show-appointment',
        'text': 'Appointments',
        'iconClass': 'fa fa-calendar',
        'group': 'Consultation'
    },
        {
        'id': 'new-emergency-ipd',
        'text': 'Emergency',
        'key': 'Ctrl+Shift+I',
        'iconClass': 'fa fa-ambulance',
        'group': 'Consultation'
    },
        {
        'id': 'show-emergency',
        'text': 'Emergency List',
        'key': 'Ctrl+Shift+I',
        'iconClass': 'fa fa-list',
        'group': 'Consultation'
    },

    {
        'id': 'take-vitals',
        'text': 'Take Vitals',
        'iconClass': 'fa fa-heartbeat',
        'group': 'Consultation'
    },
    {
        'id': 'list-vitals',
        'text': 'All Vitals',
        'iconClass': 'fa fa-list-alt',
        'group': 'Consultation'
    },

    {
        'id': 'make-encounter',
        'text': 'Make Encounter',
        'iconClass': 'fa fa-user-md',
        'group': 'Consultation'
    },
    {
        'id': 'list-encounters',
        'text': 'All Encounters',
        'iconClass': 'fa fa-users',
        'group': 'Consultation'
    },

    {
        'id': 'make-ipr',
        'text': 'Make IPR',
        'iconClass': 'fa fa-file-text-o',
        'group': 'Inpatient'
    },
    {
        'id': 'list-iprs',
        'text': 'All IPRs',
        'iconClass': 'fa fa-files-o',
        'group': 'Inpatient'
    },

    {
        'id': 'new-material-request',
        'text': 'Material Request',
        'iconClass': 'fa fa-cubes',
        'group': 'Inpatient'
    },
    {
        'id': 'new-delivery-note',
        'text': 'IPD Service Billing',
        'iconClass': 'fa fa-truck',
        'group': 'Inpatient'
    },

    {
        'id': 'ipd-transfer',
        'text': 'New IPD Transfer',
        'iconClass': 'fa fa-exchange',
        'group': 'Inpatient'
    },
    {
        'id': 'all-ipd-transfer',
        'text': 'All IPD Transfers',
        'iconClass': 'fa fa-list',
        'group': 'Inpatient'
    },

    {
        'id': 'set-medical-department',
        'text': 'Switch Department',
        'iconClass': 'fa fa-exchange',
        'group': 'General'
    }
] %}


{% include "templates/pages/collapsible_button_container.html" %}