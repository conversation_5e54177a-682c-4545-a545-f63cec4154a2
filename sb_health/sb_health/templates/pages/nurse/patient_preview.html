{% set selected_date = date or frappe.utils.today() %}
{% set patient_doc = frappe.get_doc("Patient", selectedPatient) if selectedPatient else None %}
{% set patient = patient_doc.name if patient_doc else '' %}
{% set today = frappe.utils.today() %}
{% set tomorrow = frappe.utils.add_days(today, 1) %}
{% set yesterday = frappe.utils.add_days(today, -1) %}
<div class="card p-3">
  {% if patient_doc %}
    <div class="d-flex align-items-center justify-content-between mb-3">
      <div class="d-flex align-items-center">
        {% if patient_doc.image %}
          <img src="{{ patient_doc.image }}"
               class="img-fluid rounded-circle"
               alt="Patient Photo"
               style="width: 30px;
                      height: 30px;
                      margin-right:10px">
        {% else %}
          <div class="rounded-circle d-flex align-items-center justify-content-center bg-secondary text-white"
               style="width: 30px;
                      height: 30px;
                      font-size: 10px;
                      font-weight: bold;
                      margin-right:10px">
            {{ patient_doc.first_name[0] if patient_doc.first_name else '?' }}
            {{ patient_doc.last_name[0] if patient_doc.last_name else '' }}
          </div>
        {% endif %}
        <h4 class="mb-0 ms-2">
          {% if patient_doc.first_name %}{{ patient_doc.first_name }}{% endif %}
          {% if patient_doc.middle_name %}{{ patient_doc.middle_name }}{% endif %}
          {% if patient_doc.last_name %}{{ patient_doc.last_name }}{% endif %}
        </h4>
      </div>
      <div>
        <i class="fa fa-print"></i>
        <i class="fa fa-refresh text-muted"
           style="cursor: pointer"
           onclick="window.workstationInstance.updateSectionsAfterSelection()"></i>
      </div>
    </div>
    {% if patient_doc.inpatient_status %}
      <span id="inpatientBadge"
            class="badge rounded-pill bg-secondary text-light ms-2 p-2 m-2"
            style="opacity: 0.7;
                   font-size: 12px;
                   cursor: pointer"
            onclick="window.workstationInstance.renderForm('Inpatient Record', '{{ patient_doc.inpatient_record }}', '#content-section')">
        {{ patient_doc.inpatient_status }}
      </span>
    {% endif %}
    {% set patient_fields = [
          ('Patient ID', patient_doc.name),
          ('Gender', patient_doc.sex or 'Not Available'),
          ('DOB', patient_doc.dob or 'Not Available'),
          ('Mobile', patient_doc.mobile or 'Not Available'),
          ('Email', patient_doc.email or 'Not Available'),
        ] %}
    <hr>
    {% for label, value in patient_fields %}
      <div class="row mb-2">
        <div class="col-4">
          <strong>{{ label }}</strong>
        </div>
        <div class="col-8">
          {% if label == 'DOB' and value %}
            {% if value %}
              {{ value.strftime("%Y-%m-%d") }} ({{ age_from_date(value) }} yrs)
            {% else %}
              Not Available
            {% endif %}
          {% else %}
            {{ value }}
          {% endif %}
        </div>
      </div>
    {% endfor %}
    {% include 'templates/pages/nurse/components/medical_history.html' %}
    <hr>
    <div id="appointment-section">
      <div class="row align-items-center mb-2">
        <div class="col-auto text-end">
          <i class="fa fa-chevron-left"
             style="cursor: pointer;
                    font-size: 16px"
             onclick="updateAppointmentDate(-1)"></i>
        </div>
        <div class="col text-center">
          <h5 class="mb-0" id="appointment-date-label">
            Appointments for <span id="appointment-date-value" data-date="{{ selected_date }}">
            {% if selected_date == today %}
              Today
            {% elif selected_date == tomorrow %}
              Tomorrow
            {% elif selected_date == yesterday %}
              Yesterday
            {% else %}
              {{ selected_date }}
            {% endif %}
          </span>
        </h5>
      </div>
      <div class="col-auto text-end">
        <i class="fa fa-chevron-right"
           style="cursor: pointer;
                  font-size: 16px"
           onclick="updateAppointmentDate(1)"></i>
      </div>
    </div>
    {% include "templates/pages/nurse/components/appointment_list.html" %}
  </div>
{% else %}
  <p class="text-muted">No patient selected or patient data unavailable.</p>
{% endif %}
</div>
<script>
  function addDaysToDate(dateString, days) {
    const date = new Date(dateString);
    date.setDate(date.getDate() + days);
    return date.toISOString().slice(0, 10);
  }

  function formatDateLabel(dateStr) {
    const today = new Date().toISOString().slice(0, 10);
    const tomorrow = addDaysToDate(today, 1);
    const yesterday = addDaysToDate(today, -1);

    if (dateStr === today) return "Today";
    if (dateStr === tomorrow) return "Tomorrow";
    if (dateStr === yesterday) return "Yesterday";
    return dateStr;
  }

  function updateAppointmentDate(delta) {
    const dateSpan = document.getElementById("appointment-date-value");
    const oldDate = dateSpan.dataset.date;
    const newDate = addDaysToDate(oldDate, delta);

    dateSpan.textContent = formatDateLabel(newDate);
    dateSpan.dataset.date = newDate;

    window.workstationInstance.loadSection(
      "patient_preview_appointment_list",
      "#patient-preview-appointment-list",
      {
        patient: "{{ patient }}",
        date: newDate
      }
    );
  }
</script>
