{% set actions = actions or [] %}
{% set grouped_actions = {} %}

{% for action in actions %}
  {% set group_name = action.group if action.group else 'Actions' %}
  {% if group_name not in grouped_actions %}
    {% set _ = grouped_actions.update({ group_name: [] }) %}
  {% endif %}
  {% set _ = grouped_actions[group_name].append(action) %}
{% endfor %}

<div class="doc-nav-container w-100">
  {% for group, group_actions in grouped_actions.items() %}
    <div class="mb-1">
      <div
        class="doc-nav-group-header d-flex align-items-center justify-content-between"
        data-bs-target="#collapse-{{ loop.index }}"
      >
        <span>{{ group }}</span>
        <i class="fa fa-angle-down button-icon p-2 toggle-icon me-1"></i>
      </div>
      <div class="collapse show mt-1" id="collapse-{{ loop.index }}">
        {% for action in group_actions %}
          <div
            class="doc-nav-item d-flex align-items-center"
            id="{{ action.id }}"
            {% if action.key %}
              data-bs-toggle="tooltip"
              title="{{ action.key }}"
            {% endif %}
          >
            <i class="{{ action.iconClass or 'fa fa-circle' }} button-icon p-2"></i>
            <span>{{ action.text }}</span>
          </div>
        {% endfor %}
      </div>
    </div>
  {% endfor %}
</div>

<script>
  $(document).ready(function () {
    $('[data-bs-toggle="tooltip"]').tooltip();

    function triggerAction(actionId) {
      $('.doc-nav-item').removeClass('selected');
      $('#' + actionId).addClass('selected');
    }

    $('.doc-nav-item').on('click', function () {
      const actionId = $(this).attr('id');
      triggerAction(actionId);
    });

    $('.doc-nav-group-header').on('click', function () {
      const target = $(this).attr('data-bs-target');
      $(target).collapse('toggle');
      const icon = $(this).find('.toggle-icon');
      icon.toggleClass('fa-angle-down fa-angle-left');
    });

    window.triggerAction = triggerAction;
  });
</script>

<style>
  .doc-nav-container {
    max-width: 250px;
    padding: 0.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  }

  .doc-nav-item,
  .doc-nav-group-header {
    padding: 0.1rem 1rem;
    cursor: pointer;
    font-size: 13px;
    color: #333;
    border-left: 3px solid transparent;
    transition: background-color 0.2s, border-color 0.2s;
    user-select: none;
  }

  .doc-nav-item:hover,
  .doc-nav-group-header:hover {
    background-color: #f5f5f5;
  }

  .doc-nav-item.selected {
    border-left-color: #00ff55;
    background-color: #e6fff0a5;
    font-weight: 510;
    color: #012712;
  }

  .button-icon {
    font-size: 1.2rem;
    color: #5f6160e1;
    transition: color 0.2s;
    margin-right: 1rem;
  }

  .doc-nav-group-header {
    font-weight: 600;
    border-radius: 4px;
    text-transform: capitalize;
  }

  .toggle-icon {
    width: 1rem;
    text-align: center;
    margin-right: 0.5rem;
  }

  .collapse.show {
    margin-bottom: 0.5rem;
  }

  .mb-1 {
    margin-bottom: 0.5rem !important;
  }
</style>
