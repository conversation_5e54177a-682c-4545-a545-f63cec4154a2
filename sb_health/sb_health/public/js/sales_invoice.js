frappe.ui.form.on("Sales Invoice", {
	setup: function (frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			frm.page.sidebar.remove();

			let hidden_fields = [
				// "company",
				"posting_date",
				"posting_time",
				"accounting_dimensions_section",
				"time_sheet_list",
				"scan_barcode",
				"update_stock",
				"taxes",
				"currency_and_price_list",
				"set_warehouse",
				"set_posting_time",
				"due_date",
				// "is_debit_note",
				"ref_practitioner",
				"service_unit",
				"taxes_section",
				// "is_pos",
				"use_company_roundoff_cost_center",
			];

			if (frm.is_new()) {
				hidden_fields.forEach(function (field) {
					frm.set_df_property(field, "hidden", 1);
				});
			}

			if (window.workstationInstance && !window.workstationInstance.is_opd) {
				frm.set_df_property("advances_section", "collapsible", 0);
				moveFieldBefore(frm, "advances_section", "items");
			}

			moveFieldBefore(frm, "naming_series", "shipping_rule");
			moveFieldBefore(frm, "paid_amount", "taxes_section");
			moveFieldBefore(frm, "payments", "paid_amount");
		}
	},
	patient: function (frm) {
		if (!frm.is_new()) return;
		// if (!window.workstationInstance) return;

		frm.clear_table("items");

		const targetPages = ["health-counter"];

		if (frm.doc.patient !== window.workstationInstance.selectedPatient) {
			window.workstationInstance.setSelectedPatient(frm.doc.patient);
		}

		if (
			targetPages.includes(window.workstationInstance.page_name) &&
			frm.doc.patient &&
			!frm.doc.is_return
		) {
			frm.refresh_field("patient");
			frm.set_value("update_stock", 0);
			
			frm.clear_table("items");

			frappe.db
			.get_single_value("Opterp Health Settings", "auto_fill_outstanding_services")
			.then((auto_fill) => {
				if (auto_fill) {
					frm.call({
						doc: frm.doc,
						method: "auto_fill_services",
						callback: (r)=>{
							frappe.db.get_value("Patient", frm.doc.patient, "inpatient_status", (r) => {
								if (r.inpatient_status == "" || !r.inpatient_status) {
									// OPD
									frm.set_value("is_pos", 1);
								} else {
									// IPD
									frm.set_value("is_pos", 0);
								}
								frm.refresh_field("is_pos");
							});
						}
					});
				}
			});
		}
	},
	onload: function (frm) {
		if (window.workstationInstance) {
			const targetPages = ["health-counter"];

			if (frm.doc.patient !== window.workstationInstance.selectedPatient && frm.is_new() && frm.doc.is_return !== 1) {
				frm.set_value("patient", window.workstationInstance.selectedPatient);
				frm.refresh_field("patient");
			}
		}
	},
	refresh: function (frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			if (frm.doc.patient !== window.workstationInstance.selectedPatient && frm.is_new() && frm.doc.is_return !== 1) {
				frm.set_value("patient", window.workstationInstance.selectedPatient);
				frm.refresh_field("patient");
			}
			// frappe.after_ajax did not work here but the following works but need a better appraoch
			setTimeout(() => {
				removeButtons(frm, [
					["Delivery", "Create"],
					["Payment", "Create"],
					["Return / Credit Note", "Create"],
					["Payment Request", "Create"],
					["Invoice Discounting", "Create"],
					["Dunning", "Create"],
					["Maintenance Schedule", "Create"],
					["Accounting Ledger", "View"],
				]);
				if (frm.doc.docstatus == 1) {
					frm.add_custom_button(
						"Return / Credit Note",
						() => {
							frappe.call({
								method: "erpnext.accounts.doctype.sales_invoice.sales_invoice.make_sales_return",
								args: {
									source_name: frm.doc.name,
								},
								callback: function (doc) {
									window.workstationInstance.renderNewForm(
										"Sales Invoice",
										"#content-section",
										{},
										doc.message
									);
								},
							});
						},
						"Create"
					);
				}
			}, 500);
		}
		if (frm.doc.patient && frm.is_new()) {
			frm.trigger("get_advances");
		}
	},
	after_save: function (frm) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		) {
			frm.page.sidebar.remove();
		}
	},
});


frappe.ui.form.on('Sales Invoice Item', {
    item_code(frm, cdt, cdn) {
		const targetPages = ["health-counter", "pharmacy-page"];
		if (
			window.workstationInstance &&
			targetPages.includes(window.workstationInstance.page_name)
		){
		const row = frappe.get_doc(cdt,cdn);
		setTimeout(()=>{
			if (row.item_tax_template) {
				frappe.call({
					method: "frappe.client.get",
					args: {
						doctype: "Item Tax Template",
						name: row.item_tax_template
					},
					callback: function (r) {
						if (r.message && r.message.taxes && r.message.taxes.length > 0) {
							const tax_rate = r.message.taxes[0].tax_rate || 0;
							frappe.model.set_value(cdt, cdn, "tax_rate", tax_rate);
						} else {
							frappe.model.set_value(cdt, cdn, "tax_rate", 0);
						}
					}
				});
			} else {
				frappe.model.set_value(cdt, cdn, "tax_rate", 0);
			}
		}, 500)
    }
}
});
