.btn {
    background-color: #e2ebde !important;
    color: rgb(19, 4, 4) !important;
    border-color: #106fdc !important;
}

.btn-primary {
    background-color: #1d810b !important;
    font-weight: bold !important;
    color: white !important;
    border-color: #054811 !important;
}

.navbar-expand {
    background-color: #038e43!important;
    height: 65px !important;
}
#navbar-breadcrumbs a{
    margin-left: var(--margin-md);
    font-size: var(--text-base);
    font-weight: var(--weight-regular);
    letter-spacing: .02em;
    color: white !important;
}
.app-logo {
    max-height: 50px !important;
    border-radius: 15px !important;
    overflow: hidden !important;
}

svg.es-icon.icon-sm {
        width: 22px !important;
        height: 22px !important;
    } 

.widget.shortcut-widget-box{
    background-color: #b2f0b8 !important;
    color: rgb(145, 194, 148) !important;
    padding: 1.2rem;
    display: flex;
    justify-content: center;
    align-items: stretch;
    text-align: center;
    flex-direction: column;
}

#departmentDropdown {
    display: none;
    position: absolute;
    background-color: white;
    list-style: none;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.quick-links {
    padding: 60px 0;
    background-color: #baeabf;
}

.quick-links {
    padding: 60px 0;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .quick-links {
        padding: 40px 0;
        min-height: 250px;
    }

    .link-btn {
        font-size: 16px;
        padding: 14px 20px;
    }
}

footer{
    background-color: #97df8b !important;
}

.card {
    border-radius: 8px;
    padding: 1px;
}

.section-item {
    display: flex;
    align-items: center;
    background-color: #aadfa5;
    /* padding: 5px 5px;  */
    border-radius: 6px; 
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    /* font-size: 12px; */
    /* font-weight:600; */
}

.section-item:hover {
    background-color: #89eb9b;
    transform: scale(1.05); 
}


.section-item .text {
    flex-grow: 1;
}


.nav-tabs .nav-link.active {
    color:black !important;
    border-color: #ffffff #ffffff #038e43 !important;
}

.nav-tabs .nav-link:hover {
    color: #000 !important;
}