import frappe
from frappe import _
import json 
import urllib.parse

def get_context(context):
    """
    Get context for diagnostic reports page
    """
    context.no_cache = 1
    frappe.local.response.no_cache = True
    frappe.cache().delete_value(f"website_context:{frappe.request.path}")
    frappe.clear_cache()

    context.show_sidebar = False
    context.show_search = False

    user = frappe.session.user
    frappe.logger().info(f"Session User: {frappe.session.user}")

    # Initialize context variables
    context.logged_in = False
    context.is_patient = False
    context.patient_name = ""
    context.reports = []

    # Check if user is logged in
    if user and user != "Guest":
        context.logged_in = True

        # Try to find a patient linked to the user
        patient = frappe.db.get_value("Patient", {"email": user}, ["name", "patient_name"], as_dict=True)

        if not patient and frappe.get_meta("Patient").has_field("user_id"):
            patient = frappe.db.get_value("Patient", {"user_id": user}, ["name", "patient_name"], as_dict=True)

        frappe.logger().info(f"Found patient: {patient}")
        if not patient and frappe.get_meta("Patient").has_field("custom_user"):
            patient = frappe.db.get_value("Patient", {"custom_user": user}, ["name", "patient_name"], as_dict=True)

        # If patient is found, fetch diagnostic reports
        if patient:
            context.is_patient = True
            context.patient_name = patient.get("patient_name", "")
            context.patient_id = patient.get("name")

            # Get diagnostic reports
            reports = frappe.get_list("Diagnostic Report",
                filters={"patient": context.patient_id},
                fields=[
                    "name", "patient_name", "gender", "age", "practitioner_name", "status", "sample_collection", "title",
                    "creation", "reference_posting_date"
                ],
                order_by="creation desc"
            )

            # Format report data
            for report in reports:
                report.formatted_date = frappe.utils.formatdate(report.creation) if report.creation else None
                report.formatted_ref_date = frappe.utils.formatdate(report.reference_posting_date) if report.reference_posting_date else None

                names_list = json.dumps([report.name])
                options_json = json.dumps({
                    "page-size": "A4",
                    "margin-top": "5mm",
                    "margin-bottom": "5mm",
                    "margin-left": "0mm",
                    "margin-right": "0mm"
                })

                encoded_names = urllib.parse.quote(names_list)
                encoded_options = urllib.parse.quote(options_json)

                report.preview_url = (
                    f"/api/method/frappe.utils.print_format.download_multi_pdf"
                    f"?doctype=Diagnostic Report"
                    f"&name={encoded_names}"
                    f"&format=Diagnostic Format new"
                    f"&no_letterhead=1"
                    f"&letterhead=No Letterhead"
                    f"&options={encoded_options}"
                )
                report.download_url = (
                    f"/api/method/frappe.utils.print_format.download_multi_pdf"
                    f"?doctype=Diagnostic Report"
                    f"&name={encoded_names}"
                    f"&format=Diagnostic Format new"
                    f"&no_letterhead=1"
                    f"&letterhead=No Letterhead"
                    f"&options={encoded_options}"
                )
            context.reports = reports
    return context
