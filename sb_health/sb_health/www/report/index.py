import frappe
from frappe import _
import json 
import urllib.parse

def get_context(context):
    """
    Get context for diagnostic reports page
    """
    context.no_cache = 1
    frappe.local.response.no_cache = True
    frappe.cache().delete_value(f"website_context:{frappe.request.path}")
    frappe.clear_cache()

    context.show_sidebar = False
    context.show_search = False

    user = frappe.session.user
    frappe.logger().info(f"Session User: {frappe.session.user}")

    # Initialize context variables
    context.logged_in = False
    context.is_patient = False
    context.patient_name = ""
    context.reports = []

    # Check if user is logged in
    if user and user != "Guest":
        context.logged_in = True

        # Try to find a patient linked to the user
        patient = frappe.db.get_value("Patient", {"email": user}, ["name", "patient_name"], as_dict=True)

        if not patient and frappe.get_meta("Patient").has_field("user_id"):
            patient = frappe.db.get_value("Patient", {"user_id": user}, ["name", "patient_name"], as_dict=True)

        frappe.logger().info(f"Found patient: {patient}")
        if not patient and frappe.get_meta("Patient").has_field("custom_user"):
            patient = frappe.db.get_value("Patient", {"custom_user": user}, ["name", "patient_name"], as_dict=True)

        # If patient is found, fetch diagnostic reports
        if patient:
            context.is_patient = True
            context.patient_name = patient.get("patient_name", "")
            context.patient_id = patient.get("name")

            # Get diagnostic reports
            reports = frappe.get_list("Diagnostic Report",
                filters={"patient": context.patient_id},
                fields=[
                    "name", "patient_name", "gender", "age", "practitioner_name", "status", "sample_collection", "title",
                    "creation", "reference_posting_date"
                ],
                order_by="creation desc"
            )

            # Format report data
            for report in reports:
                report.formatted_date = frappe.utils.formatdate(report.creation) if report.creation else None
                report.formatted_ref_date = frappe.utils.formatdate(report.reference_posting_date) if report.reference_posting_date else None

                try:
                    # Use the correct print format name - "Diagnostic Report" from healthcare module
                    print_format = "Diagnostic Report"

                    # Check if the print format exists and is enabled
                    format_exists = frappe.db.exists("Print Format", {
                        "name": print_format,
                        "doc_type": "Diagnostic Report",
                        "disabled": 0
                    })

                    if not format_exists:
                        # Fallback to Standard format
                        print_format = "Standard"

                    frappe.logger().info(f"Using print format: {print_format} for report: {report.name}")

                    # Create preview URL (for viewing in browser)
                    report.preview_url = (
                        f"/printview"
                        f"?doctype={urllib.parse.quote('Diagnostic Report')}"
                        f"&name={urllib.parse.quote(report.name)}"
                        f"&format={urllib.parse.quote(print_format)}"
                        f"&no_letterhead=1"
                    )

                    # Create download URL (for PDF download)
                    report.download_url = (
                        f"/api/method/frappe.utils.print_format.download_pdf"
                        f"?doctype={urllib.parse.quote('Diagnostic Report')}"
                        f"&name={urllib.parse.quote(report.name)}"
                        f"&format={urllib.parse.quote(print_format)}"
                        f"&no_letterhead=1"
                    )

                except Exception as e:
                    frappe.logger().error(f"Error generating URLs for report {report.name}: {str(e)}")
                    # Fallback to basic URLs
                    report.preview_url = f"/printview?doctype=Diagnostic%20Report&name={urllib.parse.quote(report.name)}&format=Standard&no_letterhead=1"
                    report.download_url = f"/api/method/frappe.utils.print_format.download_pdf?doctype=Diagnostic%20Report&name={urllib.parse.quote(report.name)}&format=Standard&no_letterhead=1"
            context.reports = reports
    return context
