{% extends "templates/web.html" %}
{% block meta_block %}
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
{% endblock %}
{% block title %} {{ _("Diagnostic Reports") }} {%endblock title %} 
{% block page_content %}
<style>
	.patient-info {
		background-color: #f0fdf4; /* Very light green */
		padding: 25px;
		border-bottom: 1px solid #d4edda;
		display: flex;
		align-items: center;
		gap: 20px;
	}

	.patient-avatar {
		width: 70px;
		height: 70px;
		background: linear-gradient(135deg, #1e7e34 0%, #218838 100%); /* Darker green */
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 2.2em;
		color: white;
		font-weight: bold;
		box-shadow: 0 5px 15px rgba(30, 126, 52, 0.3);
	}

	.patient-details h2 {
		font-size: 1.6em;
		color: #1e7e34;
		margin-bottom: 5px;
	}

	.patient-meta {
		display: flex;
		gap: 25px;
		flex-wrap: wrap;
	}

	.meta-item {
		display: flex;
		align-items: center;
		gap: 7px;
		color: #495057;
		font-size: 0.9em;
	}

	.meta-icon {
		color: #28a745;
		font-size: 1.1em;
	}

	.reports-header {
		padding: 25px;
		background: white;
		border-bottom: 1px solid #e9ecef;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.reports-title {
		font-size: 1.4em;
		color: #1e7e34;
		font-weight: 600;
	}

	.reports-count {
		background: #28a745; /* Green */
		color: white;
		padding: 6px 14px;
		border-radius: 18px;
		font-size: 0.85em;
		font-weight: 600;
	}

	.reports-table-container {
		padding: 25px;
		overflow-x: auto;
	}

	.reports-table {
		width: 100%;
		border-collapse: separate;
		border-spacing: 0 10px; /* Space between rows */
	}

	.reports-table th,
	.reports-table td {
		padding: 15px;
		text-align: left;
		vertical-align: middle;
	}

	.reports-table th {
		background-color: #e9f5ed; /* Lighter green for header */
		color: #1e7e34; /* Dark green text */
		font-weight: 700;
		text-transform: uppercase;
		font-size: 0.8em;
		letter-spacing: 0.5px;
		border-bottom: 2px solid #d4edda;
	}

	.reports-table tbody tr {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
		transition: all 0.2s ease;
		cursor: default; /* Changed from pointer to default as actions are on buttons */
	}

	.reports-table tbody tr:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
	}

	.reports-table td {
		color: #343a40;
		font-size: 0.9em;
		border-bottom: 1px solid #f0f0f0;
	}

	.reports-table tbody tr:last-child td {
		border-bottom: none;
	}

	.reports-table tbody tr td:first-child {
		border-top-left-radius: 8px;
		border-bottom-left-radius: 8px;
	}

	.reports-table tbody tr td:last-child {
		border-top-right-radius: 8px;
		border-bottom-right-radius: 8px;
	}

	.status-badge {
		padding: 5px 10px;
		border-radius: 15px;
		font-size: 0.7em;
		font-weight: 700;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		display: inline-block;
	}

	.status-completed {
		background: #d4edda;
		color: #155724;
	}
	.status-pending {
		background: #fff3cd;
		color: #856404;
	}
	.status-processing {
		background: #cce5ff;
		color: #004085;
	} /* Added processing status */
	.status-draft {
		background: #f8d7da;
		color: #721c24;
	}
	.status-cancelled {
		background: #f5c6cb;
		color: #721c24;
	}

	.icon-btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 38px; /* Fixed width for icon buttons */
		height: 38px; /* Fixed height for icon buttons */
		border: none;
		border-radius: 50%; /* Make them circular */
		background-color: transparent;
		cursor: pointer;
		transition: all 0.2s ease;
		position: relative; /* For tooltip */
	}

	.icon-btn svg {
		width: 20px;
		height: 20px;
		fill: #495057; /* Default icon color */
		transition: fill 0.2s ease;
	}

	.icon-btn:hover {
		background-color: rgba(0, 0, 0, 0.05);
	}

	.icon-btn.preview-btn:hover svg {
		fill: #28a745; /* Green for preview on hover */
	}

	.icon-btn.download-btn:hover svg {
		fill: #007bff; /* Blue for download on hover */
	}

	/* Tooltip styles */
	.icon-btn::after {
		content: attr(data-tooltip);
		position: absolute;
		bottom: 120%; /* Position above the button */
		left: 50%;
		transform: translateX(-50%);
		background-color: #333;
		color: #fff;
		padding: 5px 8px;
		border-radius: 4px;
		font-size: 0.75em;
		white-space: nowrap;
		opacity: 0;
		visibility: hidden;
		transition: opacity 0.2s, visibility 0.2s;
		z-index: 10;
	}

	.icon-btn:hover::after {
		opacity: 1;
		visibility: visible;
	}

	.btn-group {
		display: flex;
		gap: 5px;
	}

	.no-reports {
		text-align: center;
		padding: 60px 40px;
		color: #6c757d;
	}

	.no-reports-icon {
		font-size: 3.5em;
		margin-bottom: 15px;
		opacity: 0.6;
		color: #28a745;
	}

	.no-reports h3 {
		font-size: 1.4em;
		margin-bottom: 8px;
		color: #495057;
	}

	.no-access {
		text-align: center;
		padding: 60px 40px;
		color: #dc3545;
	}

	.no-access-icon {
		font-size: 3.5em;
		margin-bottom: 15px;
		opacity: 0.7;
		color: #dc3545;
	}

	.no-access h3 {
		font-size: 1.4em;
		margin-bottom: 12px;
	}

	/* Responsive adjustments for icon buttons */
	@media (max-width: 768px) {
		.icon-btn {
			width: 32px;
			height: 32px;
		}
		.icon-btn svg {
			width: 18px;
			height: 18px;
		}
		.icon-btn::after {
			/* Adjust tooltip for smaller screens */
			font-size: 0.7em;
			padding: 4px 6px;
		}
	}
</style>
<body>
	<div class="container">
		<div class="header">
			<h1>Diagnostic Reports</h1>
			<p>Your comprehensive health report dashboard</p>
		</div>

		{% if logged_in %} {% if is_patient %}
		<div class="patient-info">
			<div class="patient-avatar">{{ patient_name[0] if patient_name else 'P' }}</div>
			<div class="patient-details">
				<h2>{{ patient_name }}</h2>
				<div class="patient-meta">
					<div class="meta-item">
						<span class="meta-icon">👤</span>
						<span>Patient Portal</span>
					</div>
					<div class="meta-item">
						<span class="meta-icon">📊</span>
						<span
							>{{ reports|length }} Report{{ 's' if reports|length != 1 else ''
							}}</span
						>
					</div>
				</div>
			</div>
		</div>

		{% if reports %}
		<div class="reports-header">
			<h3 class="reports-title">Your Diagnostic Reports</h3>
			<div class="reports-count">
				{{ reports|length }} Report{{ 's' if reports|length != 1 else '' }}
			</div>
		</div>

		<div class="reports-table-container">
			<table class="reports-table">
				<thead>
					<tr>
						<th>S.N.</th>
						<th>Report ID</th>
						<th>Test Type</th>
						<th>Date</th>
						<th>Status</th>
						<th>Practitioner</th>
						<th>Actions</th>
					</tr>
				</thead>
				<tbody>
					{% for report in reports %}
					<tr>
						<td>{{ loop.index }}</td>
						<td>{{ report.name }}</td>
						<td>{{ report.title or 'N/A' }}</td>
						<td>{{ report.formatted_date or 'N/A' }}</td>
						<td>
							<span class="status-badge status-{{ report.status.lower() }}">
								{{ report.status }}
							</span>
						</td>
						<td>{{ report.practitioner_name or 'Not specified' }}</td>
						<td>
							<div class="btn-group">
								<a
									href="{{ report.preview_url }}"
									target="_blank"
									class="icon-btn preview-btn"
									data-tooltip="View Report"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 24 24"
										fill="currentColor"
									>
										<path
											d="M12 4.5C7 4.5 2.73 7.61 0 12c2.73 4.39 7 7.5 12 7.5s9.27-3.11 12-7.5c-2.73-4.39-7-7.5-12-7.5zm0 13c-3.1 0-5.6-2.5-5.6-5.6s2.5-5.6 5.6-5.6 5.6 2.5 5.6 5.6-2.5 5.6-5.6 5.6zm0-9c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5z"
										/>
									</svg>
								</a>
								<a
									href="{{ report.download_url }}"
									target="_blank"
									class="icon-btn download-btn"
									data-tooltip="Download Report"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 24 24"
										fill="currentColor"
									>
										<path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
									</svg>
								</a>
							</div>
						</td>
					</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
		{% else %}
		<div class="no-reports">
			<div class="no-reports-icon">📋</div>
			<h3>No Reports Available</h3>
			<p>
				You don't have any diagnostic reports yet. Reports will appear here once they're
				available.
			</p>
		</div>
		{% endif %} {% else %}
		<div class="no-access">
			<div class="no-access-icon">🚫</div>
			<h3>Access Restricted</h3>
			<p>You are not linked to a patient record in our system.</p>
			<p>Please contact your healthcare provider to set up portal access.</p>
		</div>
		{% endif %} {% else %}
		<div class="no-access">
			<div class="no-access-icon">🔒</div>
			<h3>Login Required</h3>
			<p>Please log in to access your diagnostic reports.</p>
			<p><a href="/login?redirect-to=/report">Click here to log in</a></p>
		</div>
		{% endif %}
	</div>
</body>
{% endblock %}
