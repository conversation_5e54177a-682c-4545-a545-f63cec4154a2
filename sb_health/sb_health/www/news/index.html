{% extends "templates/web.html" %}

{% block title %}
  {{ _("SBHealth Doctors") }}
{% endblock title %}

{% block header %}{% endblock header %}

{% block page_content %}
<link rel="stylesheet" href="/assets/css/news.css">

{% if show_list %}
  <section class="news-section">
    <h1 class="news-title">Latest News</h1>
    <div class="news-grid">
      {% for item in news_items %}
        <div class="news-card">
          {% if item.image %}
            <div class="news-card-image">
              <img src="{{ item.image }}" alt="{{ item.title }}">
            </div>
          {% endif %}
          <div class="news-card-body">
            <h2 class="news-card-title">
              <a href="/{{ item.route }}">{{ item.title }}</a>
            </h2>
            <small class="news-card-date">Published on {{ item.published_date }}</small>
            <p class="news-card-excerpt">
              {{ item.content[:150] | safe }}{% if item.content|length > 150 %}...{% endif %}
            </p>
            <a class="read-more" href="/{{ item.route }}">Read More</a>
          </div>
        </div>
      {% endfor %}
    </div>
  </section>
{% else %}
  <section class="news-detail">
    <article>
      <h1 class="news-detail-title">{{ news_item.title }}</h1>
      <div class="news-detail-date">Published on {{ news_item.published_date }}</div>
      
      {% if news_item.image %}
        <div class="news-detail-image">
          <img src="{{ news_item.image }}" alt="{{ news_item.title }}">
        </div>
      {% endif %}
      
      <div class="news-content">
        {{ news_item.content | safe }}
      </div>
    </article>
  </section>
{% endif %}
{% endblock page_content %}
