/* Container for the news section */
.news-section {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: auto;
}

/* Title */
.news-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: bold;
  color: #333;
}

/* Responsive grid */
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

/* Card styles */
.news-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
}

.news-card:hover {
  transform: translateY(-5px);
}

/* Image container */
.news-card-image img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  display: block;
}

/* Card body */
.news-card-body {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Title */
.news-card-title {
  font-size: 1.3rem;
  margin: 0 0 0.5rem;
  color: #222;
}

.news-card-title a {
  color: inherit;
  text-decoration: none;
}

.news-card-title a:hover {
  color: #007bff;
}

/* Date */
.news-card-date {
  color: #888;
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
}

/* Excerpt */
.news-card-excerpt {
  flex-grow: 1;
  font-size: 0.95rem;
  color: #444;
  margin-bottom: 1rem;
}

/* Read More */
.read-more {
  align-self: flex-start;
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.read-more:hover {
  text-decoration: underline;
}

/* Detail page */
.news-detail {
  max-width: 800px;
  margin: auto;
  padding: 2rem 1rem;
}

.news-detail-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #222;
}

.news-detail-date {
  font-size: 0.9rem;
  color: #777;
  margin-bottom: 1.5rem;
}

.news-detail-image img {
  width: 100%;
  height: auto;
  margin-bottom: 1.5rem;
  border-radius: 10px;
}

.news-content {
  font-size: 1.05rem;
  line-height: 1.6;
  color: #333;
}
.news-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); 
  gap: 2rem;
}

@media (max-width: 992px) {
  .news-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 600px) {
  .news-grid {
    grid-template-columns: 1fr;
  }
}
