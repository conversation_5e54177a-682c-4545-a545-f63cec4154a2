import frappe
from frappe.model.document import Document
import re

def slugify(text):
    if not text:
        return 'untitled'
    text = str(text).strip().lower()
    return re.sub(r'[\W_]+', '-', text).strip('-')

class News(Document):
    def before_insert(self):
        # Check if title exists before trying to generate route
        if not self.title:
            return  # Exit early if no title - avoid error on Quick Entry save or image-only entry

        if not self.route:
            self.route = self.generate_unique_route()

    def before_save(self):
        # Check if title exists before trying to generate route
        if not self.title:
            return  # Allow saving partial drafts safely

        if self.has_value_changed('title') or not self.route:
            self.route = self.generate_unique_route()

    def generate_unique_route(self):
        # Additional safety check - this method should only be called when title exists
        if not self.title:
            return 'news/untitled'
            
        base_slug = slugify(self.title)
        slug = f"news/{base_slug}"
        i = 1
        while frappe.db.exists("News", {"route": slug, "name": ["!=", self.name]}):
            slug = f"news/{base_slug}-{i}"
            i += 1
        return slug