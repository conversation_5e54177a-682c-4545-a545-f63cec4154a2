import frappe
from frappe import _
from frappe.website.utils import get_comment_list

def get_context(context):
    path = frappe.local.request.path

    if path in ('/news', '/news/'):
        context.news_items = frappe.get_all(
            "News",
            fields=["title", "published_date", "content", "route", "image"],
            order_by="published_date desc"
        )
        context.show_list = True
        return context

    route = path.lstrip('/')

    try:
        news_name = frappe.db.get_value("News", {"route": route}, "name")

        if not news_name:
            frappe.throw(_("News item not found"), frappe.DoesNotExistError)

        news_item = frappe.get_doc("News", news_name)
        context.news_item = news_item
        context.show_list = False
        context.title = news_item.title
        context.metatags = {
            "title": news_item.title,
            "description": (news_item.content[:150] + "...") if len(news_item.content) > 150 else news_item.content
        }
    except frappe.DoesNotExistError:
        frappe.throw(_("News item not found"), frappe.DoesNotExistError)

    return context
