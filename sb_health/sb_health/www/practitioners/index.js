// Modern Practitioners Page JavaScript
$(document).ready(function() {
    // Initialize page
    initializePage();

    // Search functionality
    let searchTimeout;
    $("#search-box").on("input", function() {
        const searchTerm = $(this).val().trim();

        // Show/hide clear button
        if (searchTerm) {
            $("#clear-search").show();
        } else {
            $("#clear-search").hide();
        }

        // Debounced search
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch();
        }, 500);
    });

    // Clear search
    $("#clear-search").click(function() {
        $("#search-box").val("");
        $(this).hide();
        performSearch();
    });

    // Enter key search
    $("#search-box").keypress(function(e) {
        if (e.which === 13) {
            performSearch();
        }
    });

    // Filter functionality
    $(".filter-checkbox").change(function() {
        performSearch();
    });

    // Sort functionality
    $("#sort-select").change(function() {
        performSearch();
    });

    // Clear all filters
    $("#clear-all-filters, #clear-all-filters-empty").click(function() {
        clearAllFilters();
    });

    // Mobile filter toggle
    $("#mobile-filter-btn").click(function() {
        $("#filter-sidebar").addClass("show");
        $("#filter-overlay").addClass("show");
        $("body").addClass("overflow-hidden");
    });

    // Close mobile filters
    $("#close-filters, #filter-overlay").click(function() {
        $("#filter-sidebar").removeClass("show");
        $("#filter-overlay").removeClass("show");
        $("body").removeClass("overflow-hidden");
    });

    // Pagination
    $(document).on("click", ".pagination-btn", function() {
        const page = $(this).data("page");
        if (page && !$(this).prop("disabled")) {
            goToPage(page);
        }
    });

    // Page size change
    $("#page-size").change(function() {
        // This would require backend changes to support different page sizes
        performSearch();
    });

    let selectedSlotTime = null;
    document.addEventListener("click", function (e) {
        if (e.target.classList.contains("time-slot")) {
            const allSlots = document.querySelectorAll(".time-slot");
            allSlots.forEach((slot) => slot.classList.remove("active"));
            e.target.classList.add("active");

            selectedSlotTime = e.target.getAttribute("data-from");
        }
    });
});

// Helper Functions
function initializePage() {
    // Initialize filters from URL parameters
    const urlParams = new URLSearchParams(window.location.search);

    // Set search box value
    const searchQuery = urlParams.get('query') || urlParams.get('search') || '';
    $("#search-box").val(searchQuery);
    if (searchQuery) {
        $("#clear-search").show();
    }

    // Set filter checkboxes
    urlParams.forEach((value, key) => {
        if (['department', 'gender', 'practitioner_type'].includes(key)) {
            $(`input[name="${key}"][value="${value}"]`).prop('checked', true);
        }
    });

    // Set sort dropdown
    const sort = urlParams.get('sort') || '';
    $("#sort-select").val(sort);
}

function performSearch() {
    showLoading();

    const params = new URLSearchParams();

    // Add search query
    const searchQuery = $("#search-box").val().trim();
    if (searchQuery) {
        params.append('search', searchQuery);
    }

    // Add filters
    $(".filter-checkbox:checked").each(function() {
        params.append($(this).attr('name'), $(this).val());
    });

    // Add sort
    const sort = $("#sort-select").val();
    if (sort) {
        params.append('sort', sort);
    }

    // Navigate to new URL
    const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    window.location.href = newUrl;
}

function goToPage(page) {
    showLoading();

    const params = new URLSearchParams(window.location.search);
    params.set('page', page);

    const newUrl = window.location.pathname + '?' + params.toString();
    window.location.href = newUrl;
}

function clearAllFilters() {
    $("#search-box").val("");
    $("#clear-search").hide();
    $(".filter-checkbox").prop('checked', false);
    $("#sort-select").val("");

    // Navigate to clean URL
    window.location.href = window.location.pathname;
}

function showLoading() {
    $("#loading-state").show();
    $("#practitioners-grid").hide();
}

function hideLoading() {
    $("#loading-state").hide();
    $("#practitioners-grid").show();
}

document.getElementById("bookAppointmentBtn").addEventListener("click", function () {
		const selected_time = document.getElementById("selectedSlotId").value;
		const selected_date = document.getElementById("selectedDateId").value;
		const practitioner = "{{ practitioner_name | e }}";

		if (!selected_time || !selected_date) {
			alert("Please select both a date and time slot before booking.");
			return;
		}
		document.getElementById("step1").style.display = "none";
		document.getElementById("step2").style.display = "block";
		document.getElementById("step3").style.display = "none";

		updateStepIndicator(2);
	});

	document.getElementById("step2form").addEventListener("submit", function (e) {
		e.preventDefault();

		const form = e.target;
		const formData = new FormData(form);
		const data = {};

		formData.forEach((value, key) => {
			data[key] = value;
		});

		document.getElementById("step2selectedId").value = JSON.stringify(data);
		document.getElementById("step1").style.display = "none";
		document.getElementById("step2").style.display = "none";
		document.getElementById("step3").style.display = "block";
		updateStepIndicator(3);
		fillConfirmationStep();
	});

	const ageInput = document.getElementById("patientAge");

	ageInput.addEventListener("input", function () {
		const age = parseInt(this.value);
		if (!isNaN(age)) {
			if (age < 0 || age > 105) {
				alert("Age must be between 0 and 105");
				this.value = "";
				dobInput.value = "";
				return;
			}
		}
	});

	document.getElementById("go_back_2").addEventListener("click", function (e) {
		e.preventDefault();
		document.getElementById("step1").style.display = "none";
		document.getElementById("step3").style.display = "none";
		document.getElementById("step2").style.display = "block";
		updateStepIndicator(2);
	});

	document.getElementById("go_back_1").addEventListener("click", function (e) {
		e.preventDefault();
		document.getElementById("step1").style.display = "block";
		document.getElementById("step2").style.display = "none";
		document.getElementById("step3").style.display = "none";

		updateStepIndicator(1);
	});

	// Legacy code cleanup - keeping only essential parts
	// The new modern functionality is handled above

document.addEventListener("DOMContentLoaded", function () {
	const mainElement = document.querySelector("main.container");
	if (mainElement) {
		mainElement.classList.remove("container");
		mainElement.classList.add("container-fluid mt-5 pt-4");
	}
});

document.addEventListener("DOMContentLoaded", () => {
	const dialog = document.getElementById("customDialog");
	const closeBtn = document.getElementById("closeDialogBtn");
	const today = new Date().toISOString().split("T")[0];
	document.getElementById("selectedDateId").value = today;

	document.querySelectorAll(".openDialogBtn").forEach((button) => {
		button.addEventListener("click", () => {
			const name = button.dataset.name;
			const image = button.dataset.image;
			const practitioner = button.dataset.practitioner;
			const department = button.dataset.department;
			const type = button.dataset.type;

			document.getElementById("selectedPractitionerId").value = name;

			document.getElementById("dialogImage").src = image;
			document.getElementById("dialogImage").alt = practitioner;
			document.getElementById("dialogName").textContent = practitioner;
			document.getElementById("h2_name").textContent = practitioner;
			document.getElementById("dialogDepartment").textContent = department;
			document.getElementById("dialogType").textContent = type;
			checkAvailability(name, new Date().toISOString().split("T")[0]);
			flatpickr("#calendar", {
				inline: true,
				minDate: "today",
				dateFormat: "Y-m-d",
				defaultDate: new Date().toISOString().split("T")[0],
				onChange: function (selectedDates, dateStr, instance) {
					document.getElementById("selectedDateId").value = dateStr;

					checkAvailability(name, dateStr);
				},
			});

			dialog.style.display = "flex";
		});
	});

	closeBtn.addEventListener("click", () => {
		dialog.style.display = "none";
	});

	window.addEventListener("click", (e) => {
		if (e.target == dialog) {
			dialog.style.display = "none";
		}
	});
});

function updateStepIndicator(stepNumber) {
	for (let i = 1; i <= 3; i++) {
		const stepEl = document.getElementById(`step-indicator-${i}`);
		if (i === stepNumber) {
			stepEl.classList.add("active");
		} else {
			stepEl.classList.remove("active");
		}
	}
}
document.querySelectorAll(".openDialogBtn").forEach(function (btn) {
	btn.onclick = function () {
		document.getElementById("customDialog").style.display = "flex";
		document.getElementById("step1").style.display = "block";
		document.getElementById("step2").style.display = "none";
		document.getElementById("step3").style.display = "none";
		document.getElementById("selectedSlotId").value = null;
		document.getElementById("step2selectedId").value = "";
		updateStepIndicator(1);
	};
});
document.getElementById("closeDialogBtn").onclick = function () {
	document.getElementById("customDialog").style.display = "none";
	document.getElementById("step2selectedId").value = "";
};

window.onclick = function (event) {
	const dialog = document.getElementById("customDialog");
	if (event.target === dialog) {
		dialog.style.display = "none";
	}
};

document.getElementById("openDialogBtn").onclick(function () {
	document.getElementById("selectedSlotId").value = null;

	const card = document.getElementById("health_card");
	const practitionerId = card.getAttribute("data-practitioner-id");
	document.getElementById("selectedPractitionerId").value = practitionerId;
	const dateInput = document.getElementById("appointmentDate");
	if (dateInput) {
		const today = new Date().toISOString().split("T")[0];
		dateInput.setAttribute("min", today);

		dateInput.addEventListener("change", function () {
			checkAvailability(practitionerId, dateInput.value);
		});
	}

	const defaultDate = dateInput
		? dateInput.value || new Date().toISOString().split("T")[0]
		: new Date().toISOString().split("T")[0];
	checkAvailability(practitionerId, defaultDate);
});

function initCalendarIfNeeded() {
	const calendarInput = document.getElementById("appointmentDate");
	if (calendarInput && !calendarInput._flatpickr) {
		flatpickr(calendarInput, {
			onChange: function (selectedDates, dateStr) {
				const result = document.getElementById("result");
				if (result) {
					result.innerText = `Selected date: ${dateStr}`;
				}
			},
		});
	}
}

function checkDates(practitioner_id) {
	const practitioner =
		frappe.get_doc("Healthcare Practitioner", practitioner_id).as_dict() | tojson | safe;
}

function checkAvailability(practitionerId, date) {
	if (!practitionerId) return;

	frappe.call({
		method: "opterp_health.methods.patient_appointment.check_availability",
		args: {
			practitioner: practitionerId,
			date: date,
		},
		callback: function (response) {
			const slots = response.message;
			let slotList = document.getElementById("slotList");
			let daysList = document.getElementById("daysList");
			let serviceUnit = document.getElementById("serviceUnit");

			if (!slotList || !daysList || !serviceUnit) return;

			if (!slots || !slots.length || !slots[0].avail_slot) {
				daysList.innerHTML = "<p>No available days</p>";
				slotList.innerHTML = "<p>No available slots</p>";
				return;
			}

			try {
				const avail_slots = slots[0].avail_slot;

				serviceUnit.innerText = slots[0].service_unit;
				slotList.innerHTML = avail_slots
					.map(
						(slot, _) => `
							<div class="time-slot" data-from="${slot.from_time}" data-to="${slot.to_time}">
								${slot.from_time.split(":").slice(0, 2).join(":")}
							</div>
						`
					)
					.join("");

				const timeSlots = document.querySelectorAll(".time-slot");

				timeSlots.forEach((slot) => {
					slot.addEventListener("click", function () {
						timeSlots.forEach((s) => s.classList.remove("active"));
						this.classList.add("active");

						const selectedSlot = {
							from_time: this.dataset.from,
							to_time: this.dataset.to,
						};
						document.getElementById("selectedSlotId").value = selectedSlot.from_time;
					});
				});
			} catch (error) {
				console.error("Error parsing slots:", error);
				slotList.innerHTML = "<p>No available slots</p>";
			}
		},
	});
}

function fillConfirmationStep() {
	const step2Data = document.getElementById("step2selectedId").value;
	const userData = JSON.parse(step2Data);
	document.getElementById("confirm_email").textContent = userData.user || "None";
	document.getElementById("confirm_first_name").textContent = userData.first_name || "";
	document.getElementById("confirm_last_name").textContent = userData.last_name || "";
	document.getElementById("confirm_mobile").textContent = userData.mobile || "";
	document.getElementById("confirm_gender").textContent = userData.gender || "";
	document.getElementById("confirm_age").textContent = userData.age || "";

	const department = document.getElementById("selectedDepartmentName")?.textContent || "";
	const practitioner = document.getElementById("h2_name").textContent;

	const date = document.getElementById("selectedDateId").value || "";
	const time = document.getElementById("selectedSlotId").value || "";
	const serviceUnit = document.getElementById("serviceUnit").textContent || "";

	document.getElementById("confirm_department").textContent = department;
	document.getElementById("confirm_practitioner").textContent = practitioner;
	document.getElementById("confirm_appointmentDate").textContent = date;
	document.getElementById("confirm_timeSlots").textContent = time;
	document.getElementById("confirm_serviceUnit").textContent = serviceUnit;
}

function confirmAppointment() {
	const data = {
		department: document.getElementById("selectedDepartmentName").value,
		practitioner: document.getElementById("selectedPractitionerId").value,
		type: document.getElementById("type").textContent,
		appointmentDate: document.getElementById("confirm_appointmentDate").textContent,
		serviceUnit: document.getElementById("confirm_serviceUnit").textContent,
		email: document.getElementById("confirm_email").textContent,
		timeSlots: document.getElementById("confirm_timeSlots").textContent,
		first_name: document.getElementById("confirm_first_name").textContent,
		last_name: document.getElementById("confirm_last_name").textContent,
		mobile: document.getElementById("confirm_mobile").textContent,
		gender: document.getElementById("confirm_gender").textContent,
		age: document.getElementById("confirm_age").textContent,
	};

	let site = window.location.origin;
	let url = site + "/api/method/sb_health.methods.patient.create_patient_request";

	fetch(url, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Frappe-CSRF-Token": frappe.csrf_token,
		},
		body: JSON.stringify({ data: data }),
	})
		.then((response) => response.json())
		.then((res) => {
			if (res.message && res.message.status === "success") {
				const custom_dialog = document.getElementById("customDialog");
				if (custom_dialog) {
					custom_dialog.remove();
				}

				frappe.msgprint(
					"Successfully added patient appointment request: " +
						res.message.patient_name +
						" The hospital will contact you shortly"
				);

				setTimeout(function () {
					window.location.href = "/home";
				}, 20000);
			} else {
				frappe.msgprint("Failed to create patient appointment request.");
			}
		})
		.catch((err) => {
			console.error("Error:", err);
			frappe.msgprint("An error occurred.");
		});
}
