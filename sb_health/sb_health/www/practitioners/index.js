$(() => {
	$("#search-box").bind("search", function () {
		update_params(get_new_params(".desktop-filters"));
	});

	$("#search-box").keyup(function (e) {
		if (e.keyCode == 13) {
			$(this).trigger("search");
		}
	});

	let selectedSlotTime = null;
	document.addEventListener("click", function (e) {
		if (e.target.classList.contains("time-slot")) {
			const allSlots = document.querySelectorAll(".time-slot");
			allSlots.forEach((slot) => slot.classList.remove("active"));
			e.target.classList.add("active");

			selectedSlotTime = e.target.getAttribute("data-from");
		}
	});

	document.getElementById("bookAppointmentBtn").addEventListener("click", function () {
		const selected_time = document.getElementById("selectedSlotId").value;
		const selected_date = document.getElementById("selectedDateId").value;
		const practitioner = "{{ practitioner_name | e }}";

		if (!selected_time || !selected_date) {
			alert("Please select both a date and time slot before booking.");
			return;
		}
		document.getElementById("step1").style.display = "none";
		document.getElementById("step2").style.display = "block";
		document.getElementById("step3").style.display = "none";

		updateStepIndicator(2);
	});

	document.getElementById("step2form").addEventListener("submit", function (e) {
		e.preventDefault();

		const form = e.target;
		const formData = new FormData(form);
		const data = {};

		formData.forEach((value, key) => {
			data[key] = value;
		});

		document.getElementById("step2selectedId").value = JSON.stringify(data);
		document.getElementById("step1").style.display = "none";
		document.getElementById("step2").style.display = "none";
		document.getElementById("step3").style.display = "block";
		updateStepIndicator(3);
		fillConfirmationStep();
	});

	const ageInput = document.getElementById("patientAge");

	ageInput.addEventListener("input", function () {
		const age = parseInt(this.value);
		if (!isNaN(age)) {
			if (age < 0 || age > 105) {
				alert("Age must be between 0 and 105");
				this.value = "";
				dobInput.value = "";
				return;
			}
		}
	});

	document.getElementById("go_back_2").addEventListener("click", function (e) {
		e.preventDefault();
		document.getElementById("step1").style.display = "none";
		document.getElementById("step3").style.display = "none";
		document.getElementById("step2").style.display = "block";
		updateStepIndicator(2);
	});

	document.getElementById("go_back_1").addEventListener("click", function (e) {
		e.preventDefault();
		document.getElementById("step1").style.display = "block";
		document.getElementById("step2").style.display = "none";
		document.getElementById("step3").style.display = "none";

		updateStepIndicator(1);
	});

	const query_params = frappe.utils.get_query_params();
	update_ui_with_filters();

	$(".desktop-filters").change(function () {
		update_params(get_new_params(".desktop-filters"));
	});

	$("#apply-filters").on("click", function () {
		update_params(get_new_params(".mobile-filters"));
	});

	$("[name=clear-filters]").on("click", function () {
		update_params();
	});

	$("#filter").click(function () {
		scroll_up_and_execute(() => {
			$("#filters-drawer").css("bottom", 0);
			$("#overlay").show();
			$("html, body").css({
				overflow: "hidden",
				height: "100%",
			});
		});
	});

	$("[name=close-filters-drawer]").click(function () {
		$("#filters-drawer").css("bottom", "-80vh");
		$("#overlay").hide();
		$("html, body").css({
			overflow: "auto",
			height: "auto",
		});
	});

	$("#sort").on("click", function () {
		const filters = $(".desktop-filters").serialize();
		query_params.sort === "asc"
			? update_params(filters)
			: update_params(filters + "&sort=asc");
	});

	$("[name=card]").on("click", function () {
		window.location.href = this.id;
	});

	$("[name=pagination]").on("click", function () {
		const filters = $(".desktop-filters").serialize();
		update_params(filters + "&page=" + this.id);
	});

	$("#previous").on("click", function () {
		const new_page = (Number(query_params?.page) || 1) - 1;
		const filters = $(".desktop-filters").serialize();
		update_params(filters + "&page=" + new_page);
	});

	$("#next").on("click", function () {
		const new_page = (Number(query_params?.page) || 1) + 1;
		const filters = $(".desktop-filters").serialize();
		update_params(filters + "&page=" + new_page);
	});

	function update_ui_with_filters() {
		const allowed_filters = Object.keys(
			JSON.parse($("#data").data("filters").replace(/'/g, '"'))
		);

		for (const filter in query_params) {
			if (filter === "query") $("#search-box").val(query_params["query"]);
			else if (filter === "page") disable_inapplicable_pagination_buttons();
			else if (allowed_filters.includes(filter)) {
				if (typeof query_params[filter] === "string") {
					$("#desktop-" + $.escapeSelector(query_params[filter])).prop("checked", true);
					$("#mobile-" + $.escapeSelector(query_params[filter])).prop("checked", true);
				} else
					for (const d of query_params[filter]) {
						$("#desktop-" + $.escapeSelector(d)).prop("checked", true);
						$("#mobile-" + $.escapeSelector(d)).prop("checked", true);
					}
			} else continue;
		}
	}

	function disable_inapplicable_pagination_buttons() {
		const no_of_pages = JSON.parse($("#data").data("no-of-pages"));
		const page_no = Number(query_params["page"]);
		if (page_no === no_of_pages) {
			$("#next").prop("disabled", true);
		} else if (page_no > no_of_pages || page_no <= 1) {
			$("#previous").prop("disabled", true);
		}
	}

	function get_new_params(filter_group) {
		return "sort" in query_params
			? $(filter_group).serialize() + "&" + $.param({ sort: query_params["sort"] })
			: $(filter_group).serialize();
	}
});

function update_params(params = "") {
	if ($("#filters-drawer").css("bottom") != "0px")
		return scroll_up_and_execute(() => (window.location.href = "/practitioners?" + params));

	$("#filters-drawer").css("bottom", "-80vh");
	$("#filters-drawer").on("transitionend webkitTransitionEnd oTransitionEnd", () =>
		scroll_up_and_execute(() => (window.location.href = "/practitioners?" + params))
	);
}

function scroll_up_and_execute(callback) {
	if (window.scrollY === 0) return callback();

	function execute_after_scrolling_up() {
		if (window.scrollY === 0) {
			callback();
			window.removeEventListener("scroll", execute_after_scrolling_up);
		}
	}

	window.scroll({
		top: 0,
		behavior: "smooth",
	});
	window.addEventListener("scroll", execute_after_scrolling_up);
}

document.addEventListener("DOMContentLoaded", function () {
	const mainElement = document.querySelector("main.container");
	if (mainElement) {
		mainElement.classList.remove("container");
		mainElement.classList.add("container-fluid mt-5 pt-4");
	}
});

document.addEventListener("DOMContentLoaded", () => {
	const dialog = document.getElementById("customDialog");
	const closeBtn = document.getElementById("closeDialogBtn");
	const today = new Date().toISOString().split("T")[0];
	document.getElementById("selectedDateId").value = today;

	document.querySelectorAll(".openDialogBtn").forEach((button) => {
		button.addEventListener("click", () => {
			const name = button.dataset.name;
			const image = button.dataset.image;
			const practitioner = button.dataset.practitioner;
			const department = button.dataset.department;
			const type = button.dataset.type;

			document.getElementById("selectedPractitionerId").value = name;

			document.getElementById("dialogImage").src = image;
			document.getElementById("dialogImage").alt = practitioner;
			document.getElementById("dialogName").textContent = practitioner;
			document.getElementById("h2_name").textContent = practitioner;
			document.getElementById("dialogDepartment").textContent = department;
			document.getElementById("dialogType").textContent = type;
			checkAvailability(name, new Date().toISOString().split("T")[0]);
			flatpickr("#calendar", {
				inline: true,
				minDate: "today",
				dateFormat: "Y-m-d",
				defaultDate: new Date().toISOString().split("T")[0],
				onChange: function (selectedDates, dateStr, instance) {
					document.getElementById("selectedDateId").value = dateStr;

					checkAvailability(name, dateStr);
				},
			});

			dialog.style.display = "flex";
		});
	});

	closeBtn.addEventListener("click", () => {
		dialog.style.display = "none";
	});

	window.addEventListener("click", (e) => {
		if (e.target == dialog) {
			dialog.style.display = "none";
		}
	});
});

function updateStepIndicator(stepNumber) {
	for (let i = 1; i <= 3; i++) {
		const stepEl = document.getElementById(`step-indicator-${i}`);
		if (i === stepNumber) {
			stepEl.classList.add("active");
		} else {
			stepEl.classList.remove("active");
		}
	}
}
document.querySelectorAll(".openDialogBtn").forEach(function (btn) {
	btn.onclick = function () {
		document.getElementById("customDialog").style.display = "flex";
		document.getElementById("step1").style.display = "block";
		document.getElementById("step2").style.display = "none";
		document.getElementById("step3").style.display = "none";
		document.getElementById("selectedSlotId").value = null;
		document.getElementById("step2selectedId").value = "";
		updateStepIndicator(1);
	};
});
document.getElementById("closeDialogBtn").onclick = function () {
	document.getElementById("customDialog").style.display = "none";
	document.getElementById("step2selectedId").value = "";
};

window.onclick = function (event) {
	const dialog = document.getElementById("customDialog");
	if (event.target === dialog) {
		dialog.style.display = "none";
	}
};

document.getElementById("openDialogBtn").onclick(function () {
	document.getElementById("selectedSlotId").value = null;

	const card = document.getElementById("health_card");
	const practitionerId = card.getAttribute("data-practitioner-id");
	document.getElementById("selectedPractitionerId").value = practitionerId;
	const dateInput = document.getElementById("appointmentDate");
	if (dateInput) {
		const today = new Date().toISOString().split("T")[0];
		dateInput.setAttribute("min", today);

		dateInput.addEventListener("change", function () {
			checkAvailability(practitionerId, dateInput.value);
		});
	}

	const defaultDate = dateInput
		? dateInput.value || new Date().toISOString().split("T")[0]
		: new Date().toISOString().split("T")[0];
	checkAvailability(practitionerId, defaultDate);
});

function initCalendarIfNeeded() {
	const calendarInput = document.getElementById("appointmentDate");
	if (calendarInput && !calendarInput._flatpickr) {
		flatpickr(calendarInput, {
			onChange: function (selectedDates, dateStr) {
				const result = document.getElementById("result");
				if (result) {
					result.innerText = `Selected date: ${dateStr}`;
				}
			},
		});
	}
}

function checkDates(practitioner_id) {
	const practitioner =
		frappe.get_doc("Healthcare Practitioner", practitioner_id).as_dict() | tojson | safe;
}

function checkAvailability(practitionerId, date) {
	if (!practitionerId) return;

	frappe.call({
		method: "opterp_health.methods.patient_appointment.check_availability",
		args: {
			practitioner: practitionerId,
			date: date,
		},
		callback: function (response) {
			const slots = response.message;
			let slotList = document.getElementById("slotList");
			let daysList = document.getElementById("daysList");
			let serviceUnit = document.getElementById("serviceUnit");

			if (!slotList || !daysList || !serviceUnit) return;

			if (!slots || !slots.length || !slots[0].avail_slot) {
				daysList.innerHTML = "<p>No available days</p>";
				slotList.innerHTML = "<p>No available slots</p>";
				return;
			}

			try {
				const avail_slots = slots[0].avail_slot;

				serviceUnit.innerText = slots[0].service_unit;
				slotList.innerHTML = avail_slots
					.map(
						(slot, _) => `
							<div class="time-slot" data-from="${slot.from_time}" data-to="${slot.to_time}">
								${slot.from_time.split(":").slice(0, 2).join(":")}
							</div>
						`
					)
					.join("");

				const timeSlots = document.querySelectorAll(".time-slot");

				timeSlots.forEach((slot) => {
					slot.addEventListener("click", function () {
						timeSlots.forEach((s) => s.classList.remove("active"));
						this.classList.add("active");

						const selectedSlot = {
							from_time: this.dataset.from,
							to_time: this.dataset.to,
						};
						document.getElementById("selectedSlotId").value = selectedSlot.from_time;
					});
				});
			} catch (error) {
				console.error("Error parsing slots:", error);
				slotList.innerHTML = "<p>No available slots</p>";
			}
		},
	});
}

function fillConfirmationStep() {
	const step2Data = document.getElementById("step2selectedId").value;
	const userData = JSON.parse(step2Data);
	document.getElementById("confirm_email").textContent = userData.user || "None";
	document.getElementById("confirm_first_name").textContent = userData.first_name || "";
	document.getElementById("confirm_last_name").textContent = userData.last_name || "";
	document.getElementById("confirm_mobile").textContent = userData.mobile || "";
	document.getElementById("confirm_gender").textContent = userData.gender || "";
	document.getElementById("confirm_age").textContent = userData.age || "";

	const department = document.getElementById("selectedDepartmentName")?.textContent || "";
	const practitioner = document.getElementById("h2_name").textContent;

	const date = document.getElementById("selectedDateId").value || "";
	const time = document.getElementById("selectedSlotId").value || "";
	const serviceUnit = document.getElementById("serviceUnit").textContent || "";

	document.getElementById("confirm_department").textContent = department;
	document.getElementById("confirm_practitioner").textContent = practitioner;
	document.getElementById("confirm_appointmentDate").textContent = date;
	document.getElementById("confirm_timeSlots").textContent = time;
	document.getElementById("confirm_serviceUnit").textContent = serviceUnit;
}

function confirmAppointment() {
	const data = {
		department: document.getElementById("selectedDepartmentName").value,
		practitioner: document.getElementById("selectedPractitionerId").value,
		type: document.getElementById("type").textContent,
		appointmentDate: document.getElementById("confirm_appointmentDate").textContent,
		serviceUnit: document.getElementById("confirm_serviceUnit").textContent,
		email: document.getElementById("confirm_email").textContent,
		timeSlots: document.getElementById("confirm_timeSlots").textContent,
		first_name: document.getElementById("confirm_first_name").textContent,
		last_name: document.getElementById("confirm_last_name").textContent,
		mobile: document.getElementById("confirm_mobile").textContent,
		gender: document.getElementById("confirm_gender").textContent,
		age: document.getElementById("confirm_age").textContent,
	};

	let site = window.location.origin;
	let url = site + "/api/method/sb_health.methods.patient.create_patient_request";

	fetch(url, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			"X-Frappe-CSRF-Token": frappe.csrf_token,
		},
		body: JSON.stringify({ data: data }),
	})
		.then((response) => response.json())
		.then((res) => {
			if (res.message && res.message.status === "success") {
				const custom_dialog = document.getElementById("customDialog");
				if (custom_dialog) {
					custom_dialog.remove();
				}

				frappe.msgprint(
					"Successfully added patient appointment request: " +
						res.message.patient_name +
						" The hospital will contact you shortly"
				);

				setTimeout(function () {
					window.location.href = "/home";
				}, 20000);
			} else {
				frappe.msgprint("Failed to create patient appointment request.");
			}
		})
		.catch((err) => {
			console.error("Error:", err);
			frappe.msgprint("An error occurred.");
		});
}
