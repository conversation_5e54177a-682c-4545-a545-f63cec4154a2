<input id="selectedSlotId" type="hidden" />
<input id="selectedDateId" type="hidden" />
<input id="step2selectedId" type="hidden" />
<input id="selectedDepartmentName" type="hidden" />
<input id="selectedPractitionerName" type="hidden" />
<input id="selectedPractitionerId" type="hidden" />
<div id="customDialog" class="dialog-overlay">
    <div class="dialog-box">
        <div class="step-progress">
            <div class="step" id="step-indicator-1">1. Select Doctor</div>
            <div class="step" id="step-indicator-2">2. Patient Info</div>
            <div class="step" id="step-indicator-3">3. Confirm</div>
        </div>
        <span class="close-btn" id="closeDialogBtn">&times;</span>
        <!-- Step 1 -->
        <div class="doctor-info-container" id="step1">
            <div class="doctor-profile">
                <div class="doctor-inside-image">
                    <img id="dialogImage" src="" alt="" />
                    <input id="h2_name" type="hidden" />
                    <h2 id="dialogName"></h2>
                    <p>
                        Department: <span id="dialogDepartment"></span>
                    </p>
                    <p>
                        Type: <span id="dialogType"></span>
                    </p>
                </div>
                <div class="doctor-info">
                    <div class="daysList" id="daysList"></div>
                    <div class="appointment-container">
                        <div class="appointment-header">Choose appointment Date & Time</div>
                        <div class="appointment-calendar">
                            <div class="form-group">
                                <div class="calendar-wrapper">
                                    <div id="calendar"></div>
                                </div>
                            </div>
                        </div>
                        <div style="align-content: center">
                            <div id="serviceUnit"></div>
                            <div style="max-height: 100px; overflow-y: auto; padding-right: 10px">
                                <div class="slot-list time-selection" id="slotList"></div>
                            </div>
                            <div id="result"></div>
                            <button id="bookAppointmentBtn" class="btn btn-success btn-lg">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Step 2 -->
        <div class="doctor-info-container" id="step2" style="display: none">
            <form id="step2form">
                <h1 style="margin-top: -7px">Patient Details</h1>
                <div class="form-group" style="display: flex; gap: 20px">
                    <div style="flex: 1">
                        <label for="first_name">First Name*</label>
                        <input type="text"
                               id="first_name"
                               name="first_name"
                               placeholder="First Name"
                               required />
                    </div>
                    <div style="flex: 1">
                        <label for="last_name">Last Name*</label>
                        <input type="text"
                               id="last_name"
                               name="last_name"
                               placeholder="Last Name"
                               required />
                    </div>
                </div>
                <div class="form-group" style="display: flex; gap: 20px">
                    <div style="flex: 1">
                        <label for="patientMobile">Mobile Number*</label>
                        <input type="tel"
                               id="patientMobile"
                               name="mobile"
                               placeholder="Enter your mobile number"
                               required />
                    </div>
                    <div style="flex: 1">
                        <label for="user">Email</label>
                        <input type="email" id="user" name="user" placeholder="Enter your email" />
                    </div>
                </div>
                <div class="form-group" style="display: flex; gap: 20px">
                    <div style="flex: 1">
                        <label for="patientAge">Age*</label>
                        <input type="number"
                               id="patientAge"
                               name="age"
                               placeholder="Enter your Age"
                               required />
                    </div>
                    <div style="flex: 1">
                        <label for="gender">Gender*</label>
                        <select id="gender" name="gender" required>
                            <option value="" disabled selected>Select your gender</option>
                            {% for gender in frappe.get_all("Gender", ["name"]) %}
                                <option value="{{ gender.name }}">{{ gender.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-group"
                     style="display: flex;
                            justify-content: space-between">
                    <button class="btn btn-secondary btn-lg" id="go_back_1">Go Back</button>
                    <button type="submit" class="btn btn-success btn-lg" id="go_to_3">Next</button>
                </div>
            </form>
        </div>
        <!-- Step 3 -->
        <div class="doctor-info-container" id="step3" style="display: none">
            <div class="container" id="confirm_appointment">
                <h1 style="margin-top: -7px">Confirm Your Appointment</h1>
                <div class="info-section">
                    <div class="info-group">
                        <div class="info">
                            <label>Department:</label>
                            <span id="confirm_department"></span>
                        </div>
                        <div class="info">
                            <label>Practitioner:</label>
                            <span id="confirm_practitioner"></span>
                        </div>
                        <div class="info">
                            <label>Appointment Type:</label>
                            <span id="type">Specialist</span>
                        </div>
                        <div class="info">
                            <label>Appointment Date:</label>
                            <span id="confirm_appointmentDate"></span>
                        </div>
                        <div class="info">
                            <label>Service Unit:</label>
                            <span id="confirm_serviceUnit"></span>
                        </div>
                        <div class="info">
                            <label>Time Slot:</label>
                            <span id="confirm_timeSlots"></span>
                        </div>
                    </div>
                    <div class="info-group">
                        <div class="info">
                            <label>Email:</label>
                            <span id="confirm_email"></span>
                        </div>
                        <div class="info">
                            <label>First Name:</label>
                            <span id="confirm_first_name"></span>
                        </div>
                        <div class="info">
                            <label>Last Name:</label>
                            <span id="confirm_last_name"></span>
                        </div>
                        <div class="info">
                            <label>Mobile:</label>
                            <span id="confirm_mobile"></span>
                        </div>
                        <div class="info">
                            <label>Gender:</label>
                            <span id="confirm_gender"></span>
                        </div>
                        <div class="info">
                            <label>Age:</label>
                            <span id="confirm_age"></span>
                        </div>
                    </div>
                </div>
                <div class="form-group"
                     style="display: flex;
                            justify-content: space-between">
                    <button class="btn btn-secondary btn-lg" id="go_back_2">Go Back</button>
                    <button class="btn btn-success btn-lg"
                            id="confirm_button"
                            onclick="confirmAppointment()">Confirm Appointment</button>
                </div>
            </div>
        </div>
    </div>
</div>
