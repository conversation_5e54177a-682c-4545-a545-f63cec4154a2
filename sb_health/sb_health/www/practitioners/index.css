/* Modern Practitioners Page Styles */
.practitioners-page-container {
    background: #f8f9fa;
    min-height: 100vh;
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.page-content-wrapper {
    padding: 0 !important;
    margin-top: 0 !important;
}

#page-index {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

.button-filter-bottom {
    display: none !important;
}

.page-breadcrumbs {
    display: none !important;
}

/* Remove any top margins/padding from parent containers */
main.container-fluid {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem 0 1.5rem;
    margin-bottom: 2rem;
    margin-top: 0 !important;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Search Container */
.search-container {
    display: flex;
    justify-content: flex-end;
}

.search-box {
    position: relative;
    max-width: 400px;
    width: 100%;
}

.search-input {
    padding: 0.75rem 3rem 0.75rem 3rem;
    border: none;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    font-size: 1rem;
    transition: all 0.3s ease;
    height: 48px;
    line-height: 1.5;
}

.search-input:focus {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
    border-color: transparent;
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1.25rem;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 2;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-clear-btn {
    position: absolute;
    right: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    z-index: 2;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Practitioner Cards */
.practitioner-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.practitioner-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.card-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.practitioner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.practitioner-card:hover .practitioner-image {
    transform: scale(1.05);
}

.practitioner-initials {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: 700;
}

.status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255,255,255,0.95);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.card-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.practitioner-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.practitioner-details {
    flex: 1;
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.detail-item i {
    width: 16px;
    text-align: center;
}

.phone-link {
    color: #28a745;
    text-decoration: none;
}

.phone-link:hover {
    text-decoration: underline;
}

.card-actions {
    margin-top: auto;
}

.book-appointment-btn {
    width: 100%;
    padding: 0.75rem;
    border-radius: 12px;
    font-weight: 600;
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
}

.book-appointment-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.empty-state-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination {
    margin: 0;
}

.page-link {
    border: none;
    color: #6c757d;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: #e9ecef;
    color: #495057;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: transparent;
    color: white;
}

.page-item.disabled .page-link {
    color: #adb5bd;
    background-color: transparent;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-size-selector .form-label {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.page-size-selector .form-select {
    width: auto;
    border-radius: 8px;
}

/* Loading States */
.loading-state {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    padding: 4rem 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .page-header {
        padding: 2rem 0 1.5rem;
        text-align: center;
    }

    .page-title {
        font-size: 2rem;
    }

    .search-container {
        justify-content: center;
        margin-top: 1rem;
    }

    .pagination-container {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pagination-info {
        order: 2;
    }

    .pagination {
        order: 1;
    }

    .page-size-selector {
        order: 3;
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .page-header {
        padding: 1.5rem 0 1rem;
    }

    .page-title {
        font-size: 1.75rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .search-box {
        max-width: none;
    }

    .practitioner-card {
        margin-bottom: 1rem;
    }

    .card-image-container {
        height: 150px;
    }

    .practitioner-initials {
        font-size: 2rem;
    }

    .practitioner-name {
        font-size: 1.1rem;
    }

    .detail-item {
        font-size: 0.85rem;
    }

    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 575.98px) {
    .practitioners-page-container {
        padding: 0 0.5rem;
    }

    .page-header .container-fluid {
        padding: 0 1rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state-icon {
        font-size: 3rem;
    }
}
/* Additional fixes for alignment and spacing */

/* Fix any inherited margins from Frappe framework */
.web-form-wrapper,
.page-container,
.container,
.container-fluid {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Ensure proper checkbox alignment */
.filter-sidebar .form-check {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin-bottom: 0.75rem !important;
}

.filter-sidebar .form-check-input {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-right: 0 !important;
    flex-shrink: 0 !important;
    width: 16px !important;
    height: 16px !important;
    position: relative !important;
}

.filter-sidebar .form-check-label {
    margin: 0 !important;
    margin-bottom: 0 !important;
    line-height: 1.4 !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
}

/* Search box improvements */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input::placeholder {
    color: #9ca3af;
    font-size: 0.95rem;
}

/* Remove any unwanted spacing from parent elements */
body {
    margin-top: 0 !important;
}

.main-section {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Ensure the page starts immediately after navbar */
#page-practitioners {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.dialog-overlay {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0; top: 0;
  width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
}

.dialog-box {
  background-color: #fff;
  padding: 20px;
  width: 100%;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  max-width: 1300px;
}

.close-btn {
  position: absolute;
  top: 10px; right: 15px;
  font-size: 38.3px;
  cursor: pointer;
}
 .calendar-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}


.doctor-info-container {
  font-family: Arial, sans-serif;
  
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /*min-height: 100vh;*/
}

/* Practitioner Profile Section */
.doctor-profile {
  background-color: #fff;
  border-radius: 8px;

  width: 100%;
  max-width: 1237.3px;
 
  align-items:center;
  display:flex;
  justify-content: space-around;
  gap:20px;
}

.doctor-info{
    text-align: left;
   
    /*margin-left: 10px;*/
    align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Doctor's Image */
.doctor-profile img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 20px;
  margin-top: 20px;
}

/* Doctor's Name */
.doctor-profile h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 10px;
}

/* Department and Type */
.doctor-profile p {
  /*font-size: 1.1rem;*/
  color: #555;
  margin: 0;
}

/* Error or Empty State */
.error-message {
  color: #e74c3c;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  margin-top: 20px;
}

/* Section for No Practitioner Found */
.no-practitioner {
  font-size: 1.2rem;
  color: #f39c12;
  text-align: center;
  margin-top: 20px;
}

/* For the main wrapper to handle the entire structure */
.doctor-info-container h1 {
  font-size: 2.5rem;
  color: rgb(18, 153, 30);
  margin-bottom: 20px;
}

.doctor-details {
    
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid rgb(18, 153, 30) /* Left border for styling */
}

.doctor-details h3 {
    font-size: 22px;
    font-weight: bold;
    color: rgb(18, 153, 30);
    margin-bottom: 15px;
}

.doctor-details h4 {
    font-size: 18px;
    font-weight: bold;
    color: rgb(18, 153, 30);
    margin-top: 10px;
}

.doctor-details ul {
    list-style: none;
    padding: 0;
}

.doctor-details ul li {
    font-size: 16px;
    padding: 5px 0;
}
 .appointment-container {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 764.5px;
            text-align: center;
        }

.appointment-header {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.appointment-calendar {
    background: #e9f6ec;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.calendar-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 18px;
    color: #2c7a3f;
}

.nav-btn {
    cursor: pointer;
    color: #28a745;
    font-size: 22px;
    border: none;
    background: none;
}

.days-of-week {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #4a7a58;
    margin: 10px 0;
}

.days {
    display: flex;
    justify-content: space-between;
}

.day {
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #28a745;
    transition: background 0.3s, color 0.3s;
}

.day:hover {
    background-color: #28a745;
    color: white;
}

.day.active {
    background-color: #28a745;
    color: white;
    font-weight: bold;
}

.time-selection {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.time-slot {
    border: 1px solid #28a745;
    padding: 2px 6px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 13px;
    color: #28a745;
    font-weight: 500;
    background: white;
    transition: background 0.3s, color 0.3s;
}

.time-slot:hover {
    background: #28a745;
    color: white;
}

.time-slot.active {
    background: #28a745;
    color: white;
}

.flatpickr-day.selected{
  background: #2b9d02!important;
}

@media (max-width: 991.98px){
   
.doctor-profile{
flex-direction: column;
gap:20px;
}

.doctor-profile img{
    width: 154.2px;
height: 177.6PX;
    margin-top: 20px;
    
}


#h2_name{
    font-size:1rem;
}

}

body {
    background-color: #f2f2f2;
    margin: 0;
    padding: 0;
}

.web-form-container {
    max-width: 750px;
    margin: 2rem auto;
    padding: 30px 60px 60px 60px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

h2 {
    margin-top: 1.5rem !important;
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

input, select {
    width: 100%;
    padding: 10px;
    margin-bottom: 1.25rem;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
}

#username {
    width: 357.2px;
}

.btn {
    cursor: pointer;
}

.row {
    display: flex;
    flex-wrap: wrap;
}

.col-md-6 {
    flex: 1 1 48%;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 350px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    text-align: center;
    position: relative;
}

.modal-content input {
    width: 90%;
    padding: 10px;
    margin: 10px 0;
}

.modal-content button {
    padding: 10px 20px;
    margin-top: 10px;
    cursor: pointer;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

.toggle-link {
    color: blue;
    cursor: pointer;
}

.patient-check {
    margin-top: 10px;
}
@media (max-width: 768px) {
    .web-form-container{
        padding: 10px;
    }
    .row {
        flex-direction: column;
    }

    .col-md-6 {
        flex: 1 1 100%;
    }

    #lowerForms, #upperForm {
        flex-direction: column;
    }

    #patientMobile {
        width: 100%;
    }

    #username {
        width: 330px;
    } 
    .time-slot {
        font-size: 14px;
        
        /* transition: background 0.3s color 0.3s; */
    }
}

#result {
    margin-top: 1rem;
    text-align: center;
    font-weight: bold;
}

#confirm_appointment {
    /*max-width: 900px;*/
    background: #fff;
    margin: 30px auto;
    padding: 25px 20px;
    border-radius: 8px;
    /*box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);*/
}

h1 {
    text-align: left;
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

.info-section {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    position: relative;
    flex-wrap: nowrap;
}

.info-group {
    flex: 1;
    min-width: 300px;
    padding: 0 10px;
}

/* Middle dividing line for desktop */
.info-section::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 1px;
    background-color: #ddd;
    transform: translateX(-50%);
}

.info {
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    margin: 8px 0;
    color: #444;
}

.info label {
    font-weight: 600;
    color: #222;
    flex: 1;
}

.info span {
    flex: 1.5;
    text-align: right;
    word-break: break-word;
}

@media (max-width: 500px) {
    .info-section {
        flex-direction: column;
    }

    .info-section::before {
        display: none;
    }

    .info-group {
        padding: 10px 0;
        border-top: 1px solid #ddd;
    }

    .info-group:first-child {
        border-top: none;
    }
    #customDialog{
        transform: scale(0.9);
        overflow-y: auto;
        top: 50px;
    }
    .doctor-inside-image{
        display:none;
    }
    #go_back{
        transform: scale(0.9);
    }
    #confirm_button{
        transform: scale(0.9);
    }
    #confirm_appointment{
        margin:0px;
        padding:0px;
    }
    .info{
        margin:0px;
    }
    .step-progress{
        margin-bottom:0px;
        padding-bottom:0px;
    }
}
.step-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px;
}

.step {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  border-bottom: 3px solid #ccc;
  position: relative;
  font-weight: bold;
  color: #b5b5b5;
}

.step.active {
  border-color: #28a745;
  color: #28a745;
}

.step::after {
  content: "";
  position: absolute;
  right: -1px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  border-right: 1px solid #ccc;
}
.step:last-child::after {
  content: none;
}
