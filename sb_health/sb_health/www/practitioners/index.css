.page-content-wrapper{
    padding-right: 120px;
    padding-left: 120px;
    
}

#page-index{
	padding-top: 85px;
}

	.button-filter-bottom{
	display: none !important;
}
.page-breadcrumbs{
	display: none;
}
	
.card {
	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
	border-radius: 16px;
	transition: transform 0.2s ease, box-shadow 0.2s ease;
	background-color: #fff;
}
.card:hover {
	transform: translateY(-6px);
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.253);
	border-radius: 0px;

}
.card-img-top {
	height: 300px !important;
	width: 100% !important;
	max-width: 400px !important;
	border-radius:20px 20px 0px 30px;
	object-fit: cover;
}
.card .p-6 {
	padding: 1.5rem;
}
.card h4 {
	font-size: 1.2rem;
	font-weight: 600;
	color: #222;
	margin-top: 12px;
}
.card .text-14 span.font-weight-bold {
	color: #444;
	font-weight: 500;
}
.card .text-14 div {
	color: #555;
	margin-top: 6px;
	display: flex;
	align-items: center;
	gap: 4px;
}
.job-card-footer {
	border-top: 1px solid #eee;
	background: #fafafa;
	border-radius: 0 0 16px 16px;
}
.job-card-footer .text-secondary {
	color: #666;
}
.job-card-footer .border-right {
	border-right: 1px solid #e0e0e0;
}
.btn-sm {
	font-size: 0.85rem;
	padding: 6px 12px;
	border-radius: 8px;
}
.btn-outline-primary {
	border-color: #019414;
	color: #019414
}
.btn-outline-primary:hover {
	background-color: #019414;
	color: white;
	
}
.btn-outline-secondary {
	border-color: #6c757d;
	color: #6c757d;
}
.btn-outline-secondary:hover {
	background-color: #6c757d;
	color: white;
}


@media (max-width: 1242px) {
	.row .no-gutter{
		margin-left: 50px;
	}
	#page-index{
	padding-top: 0px;
}
.specialist-title-div{
	flex-direction: column;
}
.specialist-title-div .search-bar{
	margin-left: 20px;
	margin-top: 10px;
}

.specialist-title-div .specialist-title{
	/* margin-left: 20px; */
	margin-top: 10px;
}
.button-filter-bottom{
	display: block !important;
}
.mb-10 > div:nth-child(1){
	margin-right: 60px;
}
.no-gutter{
	margin-left: 15px;
	padding-left:25px;
}

}
.department-header{
    margin-top: -50px;
    top: .4rem;
}
.department-header::before {
    background: rgba(0, 100, 0, 0.5) !important;
}
.hero-title{
    font-size: 2rem;
}


.link-btn{
     border: none !important;
    background: none !important;
    box-shadow: none !important;
}

.link-btn img{
    display: none;
}

.link-btn h4 {
    display: inline-block; /* Ensures the box wraps tightly around the text */
    background-color: grey;
    color: white; /* Adjust text color for contrast */
    padding: 5px 10px; /* Adds spacing inside the box */
    border-radius: 5px; /* Slightly rounded corners */
    margin: 5px 0; /* Adds space above and below */
    text-align: center;
}

.book-appointment-btn{
    background: none!important;
}
.book-appointment-btn:hover,.view-profile-btn:hover{
    background: #1b8800 !important;
    transform: none!important;
    border-radius:0px;
}

#health_card{
    width: 300px!important;
    padding-bottom:0px !important;
}

.department-seperation{
    display: flex;
    align-items:center;
    gap: 80px;
        padding: 0 310px;
  /*padding-top: 50px;*/
}



   #department_name {
        color: #fff;
        position: relative;
        font-size: 2rem;
        font-weight: bold;
        z-index: 1;
    }

    .section-description {
        /*padding-top: 20px;*/
        /*padding-bottom: 20px;*/
        text-align: Justify;
        font-size: 1rem;
    }
.section-with-cards .section-title{
    display: none
}

.section-with-cards .section-description{
    display: none
}
.section-description h4{
    margin:0;
}

.department-seperation div{

    text-align: justify;
}
.contact-section {
    /*margin-top: 20px;*/
    padding:0 20px;
    border: 2px solid #ddd !important; /* Border around the contact section */
    border-radius: 8px; /* Rounded corners */
    background-color: #f9f9f9; /* Light background */
    width:100%;
    /*min-width:500px;*/
    text-align:center;
    align-items:center;
    padding-bottom:40px;
    padding-top:40px;
}

.contact-section h3 {
    font-size: 1.6rem;
    color: #2c3e50;
    margin-bottom: 10px;
    margin-top:0;
}

/* Contact Number */
.department-separation .section-contact {
    font-size: 1.2rem;
    color: #333;
}


.other_departments .mt-n6{
    gap:5px;
}

/* Number of Doctors */
.department-separation .number-of-doctors {
    font-size: 1.2rem;
    color: #333;
    margin-top: 10px;
}

.divide-btns{
    transform:none !important;
}

/* Responsive Design: Mobile */
@media (max-width: 768px) {
    .department-separation {
        width: 90%;
        padding: 15px;
    }
   
}

@media (max-width: 991.98px){
 .department-seperation{  
     flex-direction: column;
 gap: 20px;
 padding:20px;
}
}

.dialog-overlay {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0; top: 0;
  width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
}

.dialog-box {
  background-color: #fff;
  padding: 20px;
  width: 100%;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  max-width: 1300px;
}

.close-btn {
  position: absolute;
  top: 10px; right: 15px;
  font-size: 38.3px;
  cursor: pointer;
}
 .calendar-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}


.doctor-info-container {
  font-family: Arial, sans-serif;
  
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /*min-height: 100vh;*/
}

/* Practitioner Profile Section */
.doctor-profile {
  background-color: #fff;
  border-radius: 8px;

  width: 100%;
  max-width: 1237.3px;
 
  align-items:center;
  display:flex;
  justify-content: space-around;
  gap:20px;
}

.doctor-info{
    text-align: left;
   
    /*margin-left: 10px;*/
    align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Doctor's Image */
.doctor-profile img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 20px;
  margin-top: 20px;
}

/* Doctor's Name */
.doctor-profile h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 10px;
}

/* Department and Type */
.doctor-profile p {
  /*font-size: 1.1rem;*/
  color: #555;
  margin: 0;
}

/* Error or Empty State */
.error-message {
  color: #e74c3c;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
  margin-top: 20px;
}

/* Section for No Practitioner Found */
.no-practitioner {
  font-size: 1.2rem;
  color: #f39c12;
  text-align: center;
  margin-top: 20px;
}

/* For the main wrapper to handle the entire structure */
.doctor-info-container h1 {
  font-size: 2.5rem;
  color: rgb(18, 153, 30);
  margin-bottom: 20px;
}

.doctor-details {
    
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid rgb(18, 153, 30) /* Left border for styling */
}

.doctor-details h3 {
    font-size: 22px;
    font-weight: bold;
    color: rgb(18, 153, 30);
    margin-bottom: 15px;
}

.doctor-details h4 {
    font-size: 18px;
    font-weight: bold;
    color: rgb(18, 153, 30);
    margin-top: 10px;
}

.doctor-details ul {
    list-style: none;
    padding: 0;
}

.doctor-details ul li {
    font-size: 16px;
    padding: 5px 0;
}
 .appointment-container {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            max-width: 764.5px;
            text-align: center;
        }

.appointment-header {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.appointment-calendar {
    background: #e9f6ec;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.calendar-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 18px;
    color: #2c7a3f;
}

.nav-btn {
    cursor: pointer;
    color: #28a745;
    font-size: 22px;
    border: none;
    background: none;
}

.days-of-week {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #4a7a58;
    margin: 10px 0;
}

.days {
    display: flex;
    justify-content: space-between;
}

.day {
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #28a745;
    transition: background 0.3s, color 0.3s;
}

.day:hover {
    background-color: #28a745;
    color: white;
}

.day.active {
    background-color: #28a745;
    color: white;
    font-weight: bold;
}

.time-selection {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.time-slot {
    border: 1px solid #28a745;
    padding: 2px 6px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 13px;
    color: #28a745;
    font-weight: 500;
    background: white;
    transition: background 0.3s, color 0.3s;
}

.time-slot:hover {
    background: #28a745;
    color: white;
}

.time-slot.active {
    background: #28a745;
    color: white;
}

.flatpickr-day.selected{
  background: #2b9d02!important;
}

@media (max-width: 991.98px){
   
.doctor-profile{
flex-direction: column;
gap:20px;
}

.doctor-profile img{
    width: 154.2px;
height: 177.6PX;
    margin-top: 20px;
    
}


#h2_name{
    font-size:1rem;
}

}

body {
    background-color: #f2f2f2;
    margin: 0;
    padding: 0;
}

.web-form-container {
    max-width: 750px;
    margin: 2rem auto;
    padding: 30px 60px 60px 60px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

h2 {
    margin-top: 1.5rem !important;
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

input, select {
    width: 100%;
    padding: 10px;
    margin-bottom: 1.25rem;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
}

#username {
    width: 357.2px;
}

.btn {
    cursor: pointer;
}

.row {
    display: flex;
    flex-wrap: wrap;
}

.col-md-6 {
    flex: 1 1 48%;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 350px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    text-align: center;
    position: relative;
}

.modal-content input {
    width: 90%;
    padding: 10px;
    margin: 10px 0;
}

.modal-content button {
    padding: 10px 20px;
    margin-top: 10px;
    cursor: pointer;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

.toggle-link {
    color: blue;
    cursor: pointer;
}

.patient-check {
    margin-top: 10px;
}
@media (max-width: 768px) {
    .web-form-container{
        padding: 10px;
    }
    .row {
        flex-direction: column;
    }

    .col-md-6 {
        flex: 1 1 100%;
    }

    #lowerForms, #upperForm {
        flex-direction: column;
    }

    #patientMobile {
        width: 100%;
    }

    #username {
        width: 330px;
    } 
    .time-slot {
        font-size: 14px;
        
        /* transition: background 0.3s color 0.3s; */
    }
}

#result {
    margin-top: 1rem;
    text-align: center;
    font-weight: bold;
}

#confirm_appointment {
    /*max-width: 900px;*/
    background: #fff;
    margin: 30px auto;
    padding: 25px 20px;
    border-radius: 8px;
    /*box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);*/
}

h1 {
    text-align: left;
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
}

.info-section {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    position: relative;
    flex-wrap: nowrap;
}

.info-group {
    flex: 1;
    min-width: 300px;
    padding: 0 10px;
}

/* Middle dividing line for desktop */
.info-section::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 1px;
    background-color: #ddd;
    transform: translateX(-50%);
}

.info {
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    margin: 8px 0;
    color: #444;
}

.info label {
    font-weight: 600;
    color: #222;
    flex: 1;
}

.info span {
    flex: 1.5;
    text-align: right;
    word-break: break-word;
}

@media (max-width: 500px) {
    .info-section {
        flex-direction: column;
    }

    .info-section::before {
        display: none;
    }

    .info-group {
        padding: 10px 0;
        border-top: 1px solid #ddd;
    }

    .info-group:first-child {
        border-top: none;
    }
    #customDialog{
        transform: scale(0.9);
        overflow-y: auto;
        top: 50px;
    }
    .doctor-inside-image{
        display:none;
    }
    #go_back{
        transform: scale(0.9);
    }
    #confirm_button{
        transform: scale(0.9);
    }
    #confirm_appointment{
        margin:0px;
        padding:0px;
    }
    .info{
        margin:0px;
    }
    .step-progress{
        margin-bottom:0px;
        padding-bottom:0px;
    }
}
.step-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px;
}

.step {
  flex: 1;
  text-align: center;
  padding: 10px 0;
  border-bottom: 3px solid #ccc;
  position: relative;
  font-weight: bold;
  color: #b5b5b5;
}

.step.active {
  border-color: #28a745;
  color: #28a745;
}

.step::after {
  content: "";
  position: absolute;
  right: -1px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  border-right: 1px solid #ccc;
}
.step:last-child::after {
  content: none;
}
