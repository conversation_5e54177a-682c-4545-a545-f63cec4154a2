{% if department_openings %}
    <div class="row g-4">
        {% for jo in department_openings %}
            <div class="col-12 col-sm-6 col-lg-4">
                <div class="practitioner-card">
                    <div class="card-image-container">
                        {% if jo.image %}
                            <img src="{{ jo.image }}"
                                 alt="Dr. {{ jo.first_name }} {{ jo.last_name }}"
                                 class="practitioner-image" />
                        {% else %}
                            {% set first_initial = jo.first_name[:1] if jo.first_name else 'D' %}
                            {% set last_initial = jo.last_name[:1] if jo.last_name else 'R' %}
                            <div class="practitioner-initials">
                                {{ first_initial }}{{ last_initial }}
                            </div>
                        {% endif %}

                        <!-- Status Badge -->
                        <div class="status-badge">
                            <i class="fas fa-circle text-success"></i>
                            <span>Available</span>
                        </div>
                    </div>

                    <div class="card-content">
                        <h3 class="practitioner-name">
                            Dr. {{ jo.first_name }} {% if jo.last_name %}{{ jo.last_name }}{% endif %}
                        </h3>

                        <div class="practitioner-details">
                            {% if jo.department %}
                                <div class="detail-item">
                                    <i class="fas fa-hospital text-primary"></i>
                                    <span>{{ jo.department }}</span>
                                </div>
                            {% endif %}

                            {% if jo.practitioner_type %}
                                <div class="detail-item">
                                    <i class="fas fa-user-md text-info"></i>
                                    <span>{{ jo.practitioner_type }}</span>
                                </div>
                            {% endif %}

                            {% if jo.designation %}
                                <div class="detail-item">
                                    <i class="fas fa-award text-warning"></i>
                                    <span>{{ jo.designation }}</span>
                                </div>
                            {% endif %}

                            {% if jo.mobile_phone %}
                                <div class="detail-item">
                                    <i class="fas fa-phone text-success"></i>
                                    <a href="tel:{{ jo.mobile_phone }}" class="phone-link">
                                        {{ jo.mobile_phone }}
                                    </a>
                                </div>
                            {% endif %}
                        </div>

                        <div class="card-actions">
                            <button class="btn btn-primary book-appointment-btn openDialogBtn"
                                    data-name="{{ jo.name }}"
                                    data-image="{{ jo.image or '/files/default-avatar.png' }}"
                                    data-practitioner="Dr. {{ jo.first_name }} {% if jo.last_name %}{{ jo.last_name }}{% endif %}"
                                    data-department="{{ jo.department }}"
                                    data-type="{{ jo.practitioner_type }}">
                                <i class="fas fa-calendar-plus"></i>
                                Book Appointment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <!-- Empty State -->
    <div class="empty-state">
        <div class="empty-state-content">
            <i class="fas fa-user-md empty-state-icon"></i>
            <h3>No Practitioners Found</h3>
            <p>We couldn't find any practitioners matching your search criteria.</p>
            <button class="btn btn-outline-primary" id="clear-all-filters-empty">
                <i class="fas fa-refresh"></i>
                Clear All Filters
            </button>
        </div>
    </div>
{% endif %}
