<style>
    .view-profile-btn .hover-fill {
        fill: green;
        transition: fill 0.3s;
    }
    .view-profile-btn:hover {
        margin-right: 10px;
    }
    .view-profile-btn:hover .hover-fill {
        fill: white;
    }

    .icon{
        margin: 0%;

    }
</style>
{% for jo in department_openings %}
    <div class="col-12 col-md-6 col-lg-3 mb-4">
        <div name="card" class="card border h-100 text-center p-3">
            {% if jo.image %}
                <img src="{{ jo.image }}"
                     alt="{{ jo.first_name }} image"
                     class="card-img-top" />
            {% else %}
                {% set first_initial = jo.first_name[:1] if jo.first_name else '' %}
                {% set last_initial = jo.last_name[:1] if jo.last_name else '' %}
                {% set department = jo.department %}
                <div class="initials-fallback bg-primary text-white d-flex align-items-center justify-content-center"
                     style="height: 200px;
                            font-size: 2rem">{{ first_initial }}{{ last_initial }}</div>
            {% endif %}
            <h3 class="mt-3 text-truncate border-bottom"
                style="color: #1b8800;
                       font-size: 1.5rem"
                title="{{ jo.first_name }}">
                Dr.
                {% if jo.first_name %}{{ jo.first_name }}{% endif %}
                {% if jo.last_name %}{{ jo.last_name }}{% endif %}
            </h3>
            <div class="mt-2 d-flex align-items-center justify-content-center">
                <svg class="icon mr-2" style="--icon-stroke: var(--green-700)">
                    <use href="#icon-branch"></use>
                </svg>
                <span class="text-muted" style="font-size: 1.2rem">{{ jo.department or "None" }}</span>
            </div>
            {% if jo.mobile_phone %}
                <div class="mt-1 d-flex align-items-center justify-content-center">
                    <svg class="icon"
                         xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 32 32"
                         id="Phone">
                        <path d="..." fill="#34a853"></path>
                    </svg>
                    <a href="tel:{{ jo.mobile_phone }}"
                       class="text-muted"
                       style="font-size: 1.2rem">{{ jo.mobile_phone }}</a>
                </div>
            {% endif %}
            <div class="mt-4 d-flex justify-content-center">
                <button class="btn btn-success btn-sm openDialogBtn d-flex align-items-center gap-1"
                        data-name="{{ jo.name }}"
                        data-image="{{ jo.image or '/files/default-image.png' }}"
                        data-practitioner="{{ jo.name }}"
                        data-department="{{ jo.department }}"
                        data-type="{{ jo.practitioner_type }}">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         width="16"
                         height="16"
                         fill="currentColor"
                         class="bi bi-calendar-check"
                         viewBox="0 0 16 16">
                        <path d="M10.854 7.646a.5.5 0 0 0-.708.708L11.293 9.5l-1.147 1.146a.5.5 0 0 0 .708.708L12 10.207l1.146 1.147a.5.5 0 0 0 .708-.708L12.707 9.5l1.147-1.146a.5.5 0 0 0-.708-.708L12 8.793l-1.146-1.147z" />
                        <path d="M1 4a2 2 0 0 1 2-2h1V1.5a.5.5 0 0 1 1 0V2h4V1.5a.5.5 0 0 1 1 0V2h1a2 2 0 0 1 2 2v1H1V4zm14 1v7a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V5h14z" />
                    </svg>
                    Book
                </button>
            </div>
        </div>
    </div>
{% endfor %}
