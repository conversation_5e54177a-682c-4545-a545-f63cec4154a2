{% extends "templates/web.html" %}
{% block title %}
    {{ _("SBHealth Doctors") }}
{% endblock title %}
{% block header %}
{% endblock header %}
{% block page_content %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <div class="practitioners-page-container">
        <!-- Page Header -->
        <div class="page-header">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="page-title">Our Medical Specialists</h1>
                        <p class="page-subtitle">Find and book appointments with our qualified healthcare professionals</p>
                    </div>
                    <div class="col-md-6">
                        <div class="search-container">
                            <div class="search-box">
                                <i class="fas fa-search search-icon"></i>
                                <input type="search"
                                       name="query"
                                       id="search-box"
                                       class="form-control search-input"
                                       placeholder="Search doctors, departments, specializations..."
                                       value="{{ search_query }}"
                                       aria-label="Doctor Search" />
                                <button class="search-clear-btn" id="clear-search" style="display: none;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container-fluid">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3 col-md-4">
                    {% include "www/practitioners/filters.html" %}
                </div>

                <!-- Practitioners Grid -->
                <div class="col-lg-9 col-md-8">
                    <div class="practitioners-content">
                        <!-- Loading State -->
                        <div class="loading-state" id="loading-state" style="display: none;">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading practitioners...</p>
                            </div>
                        </div>

                        <!-- Practitioners Grid -->
                        <div class="practitioners-grid" id="practitioners-grid">
                            {% include "www/practitioners/practitioners_grid.html" %}
                        </div>

                        <!-- Pagination -->
                        {% include "www/practitioners/pagination.html" %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden data for JavaScript -->
    <div id="page-data"
         data-filters="{{ all_filters | tojson | e }}"
         data-current-page="{{ current_page }}"
         data-total-pages="{{ no_of_pages }}"
         data-total-count="{{ total_count }}"
         style="display: none;"></div>

    {% include "www/practitioners/booking_dialog.html" %}
{% endblock page_content %}
