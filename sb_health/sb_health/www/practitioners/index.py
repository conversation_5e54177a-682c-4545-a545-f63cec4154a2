import math
import frappe
from frappe import _
from frappe.query_builder import Order
from frappe.query_builder.functions import Count
from frappe.utils import pretty_date


def get_context(context):
	context.no_cache = 1
	if frappe.session.user == "Guest":
		context.parents = [{"name": _("Home"), "route": "/"}]
	else:
		context.parents = [{"name": _("My Account"), "route": "/me"}]
	context.body_class = "jobs-page"
	page_len = 20
	filters, txt, sort, offset = get_filters_txt_sort_offset(page_len)
	context.department_openings = get_department_openings(filters, txt, sort, page_len, offset)

	context.no_of_pages = get_no_of_pages(filters, txt, page_len)
	context.all_filters = get_all_filters(filters)
	context.sort = sort


def get_department_openings(filters=None, txt=None, sort=None, limit=2000, offset=0):
	jo = frappe.qb.DocType("Healthcare Practitioner")

	query = (
		frappe.qb.from_(jo)
		.select(
			jo.name,
			jo.first_name,
			jo.last_name,
			jo.status,
			jo.mobile_phone,
			jo.gender,
			jo.image,
			jo.department,
			jo.image,
		)
		.where((jo.status == "Active"))
		.groupby(jo.first_name)
		.limit(limit)
		.offset(offset)
	)

	if filters:
		for d in filters:
			if d == "department" and filters[d]:  # Check if the department filter is present and not empty
				query = query.where(jo.department.isin(filters[d]))  # Filter by department
			else:
				query = query.where(frappe.qb.Field(d).isin(filters[d]))

	if txt:
		query = query.where(jo.first_name.like(f"%{txt}%"))

	# If 'department' filter is applied, we ensure to only show doctors from that department
	if filters.get("department"):
		department_filter = filters["department"]
		query = query.where(jo.department.isin(department_filter))

	results = query.run(as_dict=True)

	return results


def get_no_of_pages(filters=None, txt=None, page_length=20):
	jo = frappe.qb.DocType("Healthcare Practitioner")
	query = (
		frappe.qb.from_(jo)
		.select(
			Count("*").as_("no_of_practitioner"),
		)
		.where((jo.status == "Active"))
	)

	for d in filters:
		query = query.where(frappe.qb.Field(d).isin(filters[d]))

	if txt:
		# Remove the `description` filter if not necessary
		query = query.where(jo.first_name.like(f"%{txt}%"))

	result = query.run(as_dict=True)
	return math.ceil(result[0].no_of_practitioner / page_length)


def get_all_filters(filters=None):
	filters = filters or {}

	active_practitioners = frappe.get_all(
		"Healthcare Practitioner",
		filters={"status": "Active", "healthcare_practitioner_type": "Doctor"},
		fields=["department"],
	)

	departments_filter = filters.get("department", [])

	all_filters = {}
	for practitioner in active_practitioners:
		for key, value in practitioner.items():
			if not value:
				continue

			# If department filter is applied, include only relevant entries
			if key == "department" and departments_filter and value not in departments_filter:
				continue

			all_filters.setdefault(key, set()).add(value)

	# Convert sets to sorted lists
	return {key: sorted(values) for key, values in all_filters.items()}


def get_filters_txt_sort_offset(page_len=20):
	args = frappe.request.args.to_dict(flat=False)
	filters = {}
	txt = ""
	sort = None
	offset = 0
	allowed_filters = ["department", "gender"]

	for d in args:
		if d in allowed_filters:
			filters[d] = args[d]
		elif d == "query":
			txt = args["query"][0]
		elif d == "sort":
			if args["sort"][0]:
				sort = args["sort"][0]
		elif d == "page":
			offset = (int(args["page"][0]) - 1) * page_len

	return filters, txt, sort, offset
