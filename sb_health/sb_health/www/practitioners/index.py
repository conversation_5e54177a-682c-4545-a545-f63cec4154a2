import math
import frappe
from frappe import _
from frappe.query_builder import Order
from frappe.query_builder.functions import Count
from frappe.utils import pretty_date


def get_context(context):
	context.no_cache = 1
	if frappe.session.user == "Guest":
		context.parents = [{"name": _("Home"), "route": "/"}]
	else:
		context.parents = [{"name": _("My Account"), "route": "/me"}]
	context.body_class = "practitioners-page"
	page_len = 12  # Better for card layout
	filters, txt, sort, offset = get_filters_txt_sort_offset(page_len)
	context.department_openings = get_department_openings(filters, txt, sort, page_len, offset)
	context.total_count = get_total_count(filters, txt)
	context.no_of_pages = get_no_of_pages(filters, txt, page_len)
	context.all_filters = get_all_filters(filters)
	context.sort = sort
	context.current_page = (offset // page_len) + 1
	context.search_query = txt or ""


def get_department_openings(filters=None, txt=None, sort=None, limit=12, offset=0):
	jo = frappe.qb.DocType("Healthcare Practitioner")

	query = (
		frappe.qb.from_(jo)
		.select(
			jo.name,
			jo.first_name,
			jo.last_name,
			jo.status,
			jo.mobile_phone,
			jo.gender,
			jo.image,
			jo.department,
			jo.practitioner_type,
			jo.designation,
		)
		.where((jo.status == "Active") & (jo.healthcare_practitioner_type == "Doctor"))
		.distinct()
		.limit(limit)
		.offset(offset)
	)

	# Apply filters
	if filters:
		for field_name, field_values in filters.items():
			if field_values:  # Only apply filter if values exist
				if field_name == "department":
					query = query.where(jo.department.isin(field_values))
				elif field_name == "gender":
					query = query.where(jo.gender.isin(field_values))
				elif field_name == "practitioner_type":
					query = query.where(jo.practitioner_type.isin(field_values))

	# Enhanced search functionality
	if txt:
		search_term = f"%{txt}%"
		query = query.where(
			(jo.first_name.like(search_term))
			| (jo.last_name.like(search_term))
			| (jo.department.like(search_term))
			| (jo.designation.like(search_term))
		)

	# Apply sorting
	if sort == "name_asc":
		query = query.orderby(jo.first_name, order=Order.asc)
	elif sort == "name_desc":
		query = query.orderby(jo.first_name, order=Order.desc)
	elif sort == "department":
		query = query.orderby(jo.department, order=Order.asc)
	else:
		query = query.orderby(jo.first_name, order=Order.asc)  # Default sort

	results = query.run(as_dict=True)
	return results


def get_total_count(filters=None, txt=None):
	"""Get total count of practitioners matching the filters and search"""
	jo = frappe.qb.DocType("Healthcare Practitioner")
	query = (
		frappe.qb.from_(jo).select(Count("*").as_("total_count")).where((jo.status == "Active")).distinct()
	)

	# Apply filters
	if filters:
		for field_name, field_values in filters.items():
			if field_values:  # Only apply filter if values exist
				if field_name == "department":
					query = query.where(jo.department.isin(field_values))
				elif field_name == "gender":
					query = query.where(jo.gender.isin(field_values))
				elif field_name == "practitioner_type":
					query = query.where(jo.practitioner_type.isin(field_values))

	# Enhanced search functionality
	if txt:
		search_term = f"%{txt}%"
		query = query.where(
			(jo.first_name.like(search_term))
			| (jo.last_name.like(search_term))
			| (jo.department.like(search_term))
			| (jo.designation.like(search_term))
		)

	result = query.run(as_dict=True)
	return result[0].total_count if result else 0


def get_no_of_pages(filters=None, txt=None, page_length=12):
	total_count = get_total_count(filters, txt)
	return math.ceil(total_count / page_length) if total_count > 0 else 1


def get_all_filters(filters=None):
	"""Get all available filter options for practitioners"""
	filters = filters or {}

	# Get all active practitioners with relevant fields
	active_practitioners = frappe.get_all(
		"Healthcare Practitioner",
		filters={"status": "Active", "healthcare_practitioner_type": "Doctor"},
		fields=["department"],
	)

	all_filters = {
		"department": set(),
		"gender": set(),
		"practitioner_type": set(),
	}

	# Collect unique values for each filter
	for practitioner in active_practitioners:
		if practitioner.get("department"):
			all_filters["department"].add(practitioner["department"])
		if practitioner.get("gender"):
			all_filters["gender"].add(practitioner["gender"])
		if practitioner.get("practitioner_type"):
			all_filters["practitioner_type"].add(practitioner["practitioner_type"])

	# Convert sets to sorted lists and add counts
	result = {}
	for key, values in all_filters.items():
		if values:
			result[key] = sorted(list(values))

	return result


def get_filters_txt_sort_offset(page_len=12):
	"""Parse URL parameters for filters, search text, sort, and pagination"""
	args = frappe.request.args.to_dict(flat=False)
	filters = {}
	txt = ""
	sort = None
	offset = 0
	allowed_filters = ["department", "gender", "practitioner_type"]

	for param_name, param_values in args.items():
		if param_name in allowed_filters:
			# Handle multiple values for the same filter
			filters[param_name] = param_values
		elif param_name == "query" and param_values:
			txt = param_values[0].strip()
		elif param_name == "search" and param_values:
			txt = param_values[0].strip()
		elif param_name == "sort" and param_values:
			sort = param_values[0]
		elif param_name == "page" and param_values:
			try:
				page_num = int(param_values[0])
				offset = (page_num - 1) * page_len
			except (ValueError, IndexError):
				offset = 0

	return filters, txt, sort, offset
