<!-- Modern Filter Component -->
<div class="filters-container">
    <!-- Mobile Filter Toggle -->
    <div class="mobile-filter-toggle d-md-none">
        <button class="btn btn-outline-primary" id="mobile-filter-btn">
            <i class="fa fa-filter"></i> Filters
        </button>
    </div>

    <!-- Filter Sidebar -->
    <div class="filter-sidebar" id="filter-sidebar">
        <div class="filter-header">
            <h5 class="mb-0">Filter Doctors</h5>
            <button class="btn-close d-md-none" id="close-filters">&times;</button>
        </div>

        <div class="filter-content">
            <!-- Search Results Info -->
            <div class="results-info mb-3">
                <small class="text-muted">
                    Showing {{ department_openings|length }} of {{ total_count }} doctors
                    {% if search_query %}for "{{ search_query }}"{% endif %}
                </small>
            </div>

            <!-- Clear All Filters -->
            <div class="filter-actions mb-3">
                <button class="btn btn-sm btn-outline-secondary" id="clear-all-filters">
                    Clear All Filters
                </button>
            </div>

            <!-- Department Filter -->
            {% if all_filters.department %}
            <div class="filter-group">
                <h6 class="filter-title">Department</h6>
                <div class="filter-options">
                    {% for dept in all_filters.department %}
                    <div class="form-check d-flex align-items-center">
                        <input class="form-check-input filter-checkbox me-2"
                               type="checkbox"
                               value="{{ dept }}"
                               id="dept-{{ loop.index }}"
                               name="department"
                               data-filter="department">
                        <label class="form-check-label mb-0" for="dept-{{ loop.index }}">
                            {{ dept }}
                        </label>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Gender Filter -->
            {% if all_filters.gender %}
            <div class="filter-group">
                <h6 class="filter-title">Gender</h6>
                <div class="filter-options">
                    {% for gender in all_filters.gender %}
                    <div class="form-check d-flex align-items-center">
                        <input class="form-check-input filter-checkbox me-2"
                               type="checkbox"
                               value="{{ gender }}"
                               id="gender-{{ loop.index }}"
                               name="gender"
                               data-filter="gender">
                        <label class="form-check-label mb-0" for="gender-{{ loop.index }}">
                            {{ gender }}
                        </label>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Practitioner Type Filter -->
            {% if all_filters.practitioner_type %}
            <div class="filter-group">
                <h6 class="filter-title">Type</h6>
                <div class="filter-options">
                    {% for type in all_filters.practitioner_type %}
                    <div class="form-check d-flex align-items-center">
                        <input class="form-check-input filter-checkbox me-2"
                               type="checkbox"
                               value="{{ type }}"
                               id="type-{{ loop.index }}"
                               name="practitioner_type"
                               data-filter="practitioner_type">
                        <label class="form-check-label mb-0" for="type-{{ loop.index }}">
                            {{ type }}
                        </label>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Sort Options -->
            <div class="filter-group">
                <h6 class="filter-title">Sort By</h6>
                <select class="form-select" id="sort-select" name="sort">
                    <option value="">Default</option>
                    <option value="name_asc" {% if sort == 'name_asc' %}selected{% endif %}>Name (A-Z)</option>
                    <option value="name_desc" {% if sort == 'name_desc' %}selected{% endif %}>Name (Z-A)</option>
                    <option value="department" {% if sort == 'department' %}selected{% endif %}>Department</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Filter Overlay for Mobile -->
    <div class="filter-overlay d-md-none" id="filter-overlay"></div>
</div>

<style>
.filters-container {
    position: relative;
}

.mobile-filter-toggle {
    margin-bottom: 1rem;
}

.filter-sidebar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.filter-header h5 {
    color: #333;
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
}

.results-info {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 8px;
    text-align: center;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.filter-options {
    max-height: 200px;
    overflow-y: auto;
}

.form-check {
    margin-bottom: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.form-check-input {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-right: 0.5rem !important;
    flex-shrink: 0 !important;
    width: 16px !important;
    height: 16px !important;
    position: static !important;
    float: none !important;
}

.form-check-input:checked {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
}

.form-check-label {
    font-size: 0.9rem !important;
    color: #555 !important;
    cursor: pointer !important;
    margin: 0 !important;
    margin-bottom: 0 !important;
    line-height: 1.4 !important;
    display: flex !important;
    align-items: center !important;
    padding-left: 0 !important;
}

.form-select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 0.5rem;
}

.form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Mobile Styles */
@media (max-width: 767.98px) {
    .filter-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 300px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
        border-radius: 0;
        max-height: none;
    }
    
    .filter-sidebar.show {
        left: 0;
    }
    
    .filter-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
        display: none;
    }
    
    .filter-overlay.show {
        display: block;
    }
}

/* Desktop Styles */
@media (min-width: 768px) {
    .mobile-filter-toggle {
        display: none !important;
    }
}
</style>
