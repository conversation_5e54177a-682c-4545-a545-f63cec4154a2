{% if no_of_pages > 1 %}
    <div class="pagination-container">
        <div class="pagination-info">
            <span class="results-text">
                Showing {{ ((current_page - 1) * 12 + 1) }} - {{ ((current_page - 1) * 12 + department_openings|length) }}
                of {{ total_count }} practitioners
            </span>
        </div>

        <nav aria-label="Practitioners pagination">
            <ul class="pagination justify-content-center">
                <!-- Previous Button -->
                <li class="page-item {% if current_page <= 1 %}disabled{% endif %}">
                    <button class="page-link pagination-btn"
                            data-page="{{ current_page - 1 }}"
                            {% if current_page <= 1 %}disabled{% endif %}>
                        <i class="fas fa-chevron-left"></i>
                        <span class="d-none d-sm-inline">Previous</span>
                    </button>
                </li>

                <!-- First Page -->
                {% if current_page > 3 %}
                    <li class="page-item">
                        <button class="page-link pagination-btn" data-page="1">1</button>
                    </li>
                    {% if current_page > 4 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endif %}

                <!-- Page Numbers -->
                {% set start_page = [1, current_page - 2]|max %}
                {% set end_page = [no_of_pages, current_page + 2]|min %}

                {% for page_num in range(start_page, end_page + 1) %}
                    <li class="page-item {% if page_num == current_page %}active{% endif %}">
                        <button class="page-link pagination-btn"
                                data-page="{{ page_num }}"
                                {% if page_num == current_page %}aria-current="page"{% endif %}>
                            {{ page_num }}
                        </button>
                    </li>
                {% endfor %}

                <!-- Last Page -->
                {% if current_page < no_of_pages - 2 %}
                    {% if current_page < no_of_pages - 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                    <li class="page-item">
                        <button class="page-link pagination-btn" data-page="{{ no_of_pages }}">{{ no_of_pages }}</button>
                    </li>
                {% endif %}

                <!-- Next Button -->
                <li class="page-item {% if current_page >= no_of_pages %}disabled{% endif %}">
                    <button class="page-link pagination-btn"
                            data-page="{{ current_page + 1 }}"
                            {% if current_page >= no_of_pages %}disabled{% endif %}>
                        <span class="d-none d-sm-inline">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </li>
            </ul>
        </nav>

        <!-- Page Size Selector -->
        <div class="page-size-selector d-none d-md-block">
            <label for="page-size" class="form-label">Show:</label>
            <select class="form-select form-select-sm" id="page-size">
                <option value="12" selected>12 per page</option>
                <option value="24">24 per page</option>
                <option value="36">36 per page</option>
            </select>
        </div>
    </div>
{% endif %}
