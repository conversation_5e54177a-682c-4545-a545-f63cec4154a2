{% if no_of_pages > 1 %}
    <div class="mb-4 flex">
        <div class="btn-group mx-auto border rounded"
             role="group"
             aria-label="Pagination">
            {% set page = frappe.form_dict.page or '1' %}
            <button id="previous"
                    class="btn btn-default border-right flex align-items-center bg-white">
                <svg class="icon icon-sm" style="--icon-stroke: var(--gray-600)">
                    <use href="#icon-left"></use>
                </svg>
            </button>
            <div class="flex bg-white">
                {% set initial_page = 1 if page|int == 1 else ((page|int / 3 + 0.5) |
                                    round(method='floor')|int * 3 - 2) %} {% set no_of_displayed_pages = 5 if no_of_pages -
                 initial_page > 5 else no_of_pages - initial_page + 1 %}
                {% for i in
                    range(no_of_displayed_pages) %}
                    {% set pg = i + initial_page %}
                    <button id="{{ pg }}"
                            name="pagination"
                            class="btn btn-default text-muted rounded-0">
                        <span>{{ pg }}</span>
                    </button>
                {% endfor %}
            </div>
            <button id="next"
                    class="btn btn-default border-left flex align-items-center bg-white">
                <svg class="icon icon-sm" style="--icon-stroke: var(--gray-600)">
                    <use href="#icon-right"></use>
                </svg>
            </button>
        </div>
    </div>
{% endif %}
