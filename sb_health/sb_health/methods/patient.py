import frappe
import json
from erpnext.accounts.utils import get_balance_on
from healthcare.healthcare.doctype.inpatient_record.inpatient_record import (
    get_pending_invoices,
)


@frappe.whitelist()
def patient_balance(patient_id, company=None):
    try:
        default_company = company if company else frappe.defaults.get_defaults().get("company")

        company_settings = frappe.get_doc("Company", default_company)

        customer = frappe.get_value("Patient", patient_id, "customer")

        debtor_account = company_settings.default_receivable_account

        if not debtor_account:
            return {"message": "Commission Payable account not configured."}

        balance = get_balance_on(
            account=debtor_account,
            party_type="Customer",
            party=customer,
        )

        return -balance
    except Exception as e:
        return 0


@frappe.whitelist()
def get_services_to_invoice(patient):
    record = frappe.get_all(
        "Inpatient Record",
        filters={"patient": patient, "status": "Admitted"},
        fields=["name"],
        limit=1,
    )
    if record:
        inpatient_record = frappe.get_doc("Inpatient Record", record[0]["name"])
        return get_pending_invoices(inpatient_record)
    else:
        return []


@frappe.whitelist()
def issue_gate_pass(patient):
    # invoices = frappe.get_all("Sales Invoice",
    #     filters={"patient": patient, "docstatus": 1},
    #     fields=["name", "outstanding_amount"]
    # )

    # for invoice in invoices:
    #     if invoice.outstanding_amount > 0:
    #         return False
    

    if patient_balance(patient) <= 0 and patient_balance(patient, ):
        return True

    return True



@frappe.whitelist(allow_guest=True)
def create_patient_request(data):

    email = data.get("email")
    patient_appointment_request = frappe.new_doc("Patient Appointment Request")

    existing_patient = frappe.get_value("Patient", {"email": email}, "name")

    if existing_patient:
        existing_patient_doc = frappe.db.get(
            "Patient", {"name": existing_patient}, ["first_name", "last_name"]
        )

        if existing_patient_doc:
            patient_appointment_request.patient = existing_patient
            patient_appointment_request.patient_first_name = (
                existing_patient_doc.first_name
            )
            patient_appointment_request.patient_last_name = (
                existing_patient_doc.last_name
            )
            patient_appointment_request.mobile = existing_patient_doc.get("mobile")

    else:
        patient_appointment_request.patient_first_name = data.get("first_name")
        patient_appointment_request.patient_last_name = data.get("last_name")
        patient_appointment_request.mobile = "+977-" + data.get("mobile")

    patient_appointment_request.patient_sex = data.get("gender")
    patient_appointment_request.status = "Open"
    patient_appointment_request.email_id = email
    patient_appointment_request.healthcare_practitioner = data.get("practitioner")

    practitioner_doc = frappe.db.get(
        "Healthcare Practitioner",
        {"name": patient_appointment_request.healthcare_practitioner},
        ["first_name", "last_name"],
    )

    if not practitioner_doc:
        frappe.throw(
            f"No practitioner found with name '{patient_appointment_request.healthcare_practitioner}'"
        )

    patient_appointment_request.department = data.get("department")
    patient_appointment_request.appointment_for = "Practitioner"
    patient_appointment_request.appointment_date = data.get("appointmentDate")
    patient_appointment_request.appointment_time = data.get("timeSlots")
    patient_appointment_request.service_unit = data.get("serviceUnit")
    patient_appointment_request.patient_age = data.get("age")

    patient_appointment_request.save(ignore_permissions=True)

    return {
        "status": "success",
        "patient_name": patient_appointment_request.patient_first_name,
    }


@frappe.whitelist()
def create_appointment(data):
    try:
        if isinstance(data, str):
            data = json.loads(data)
        
        appointment = frappe.get_doc({
            'doctype': 'Patient Appointment',
            'patient': data.get('patient'),
            'practitioner': data.get('healthcare_practitioner'),
            'department': data.get('department'),
            'service_unit': data.get('service_unit'),
            'appointment_date': data.get('appointment_date'),
            'appointment_time': data.get('appointment_time'),
            'appointment_type': 'Specialist',
            'appointment_for': 'Practitioner',
            'status': 'Scheduled',
        })
        
        appointment.insert(ignore_permissions=True)
        appointment.submit()
        
        frappe.db.set_value('Patient Appointment Request', data.get('name'), 'status', 'Booked')
        
        return [appointment.name, data.get('patient')]
    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), 'Create Appointment Error')
        return None


@frappe.whitelist()
def create_patient_and_appointment(data):
    try:
        if isinstance(data, str):
            data = json.loads(data)
        
        email = data.get('email_id')
        mobile = data.get('mobile')
        
        existing_patient = None
        if email:
            existing_patient = frappe.db.get_value("Patient", {"email": email}, "name")
        
        if not existing_patient and mobile:
            existing_patient = frappe.db.get_value("Patient", {"mobile": mobile}, "name")
        
        if existing_patient:
            patient_name = existing_patient
        else:
            patient = frappe.get_doc({
                'doctype': 'Patient',
                'first_name': data.get('patient_first_name'),
                'last_name': data.get('patient_last_name'),
                'sex': data.get('patient_sex'),
                'mobile': data.get('mobile'),
                'email': data.get('email_id'),
                'dob': data.get('dob')
            })
            
            patient.insert(ignore_permissions=True)
            patient_name = patient.name
        

        appointment = frappe.get_doc({
            'doctype': 'Patient Appointment',
            'patient': patient_name,
            'practitioner': data.get('healthcare_practitioner'),
            'department': data.get('department'),
            'service_unit': data.get('service_unit'),
            'appointment_date': data.get('appointment_date'),
            'appointment_time': data.get('appointment_time'),
            'appointment_type': 'Specialist',
            'appointment_for': 'Practitioner',
            'status': 'Scheduled',
        })
        
        appointment.insert(ignore_permissions=True)
        appointment.submit()
        
        frappe.db.set_value('Patient Appointment Request', data.get('name'), 'status', 'Booked')
        
        return [appointment.name, patient_name]
    
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), 'Create Patient and Appointment Error')
        return None
