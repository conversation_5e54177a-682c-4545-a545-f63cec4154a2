import frappe
from frappe import _


@frappe.whitelist(allow_guest=True)
def save_packaging_form(full_name, email, phone, health_package):
	doc = frappe.get_doc(
		{
			"doctype": "Health Package Request",
			"full_name": full_name,
			"package_email": email,
			"phone_number": "+977-" + phone,
			"health_package": health_package,
		}
	)
	doc.insert(ignore_permissions=True)
	frappe.db.commit()

	return {"status": "success", "patient_name": full_name}
