import frappe

def boot_session(bootinfo):
    if frappe.request.host == "sbhealth.com.np":
        frappe.local.response["type"] = "redirect"
        frappe.local.response["location"] = "https://kathmandunational.edu.np"

    user = frappe.get_doc("User", frappe.session.user)
    roles = {role.role for role in user.roles}

    if "Healthcare Administrator" in roles:
        frappe.local.response["home_page"] = "healthcare"
    elif "Healthcare Administrator" not in roles:
        if "Pharmacy Admin" in roles:
            frappe.local.response["home_page"] = "pharmacyadmin"
        if "Counter" in roles:
            bootinfo.home_page = "health-counter"
        elif "Nursing User" in roles:
            bootinfo.home_page = "nurse-page"
        elif "Pharmacy" in roles:
            bootinfo.home_page = "pharmacy-page"
        elif "Physician" in roles:
            bootinfo.home_page = "doctor-page"
        else:
            frappe.local.response["home_page"] = "home"

    
# "ssl_certificate": "/etc/letsencrypt/live/sbhealth.com.np/fullchain.pem",
# "ssl_certificate_key": "/etc/letsencrypt/live/sbhealth.com.np/privkey.pem"