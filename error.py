{
    "exception": "frappe.exceptions.TimestampMismatchError: Error: Document has been modified after you have opened it (2025-07-04 17:09:38.661305, 2025-07-04 17:09:38.704886). Please refresh to get the latest document.",
    "exc_type": "TimestampMismatchError",
    "exc": '["Traceback (most recent call last):\\n  File \\"apps/frappe/frappe/app.py\\", line 115, in application\\n    response = frappe.api.handle(request)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/api/__init__.py\\", line 49, in handle\\n    data = endpoint(**arguments)\\n           ^^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/api/v1.py\\", line 36, in handle_rpc_call\\n    return frappe.handler.handle()\\n           ^^^^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/handler.py\\", line 51, in handle\\n    data = execute_cmd(cmd)\\n           ^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/handler.py\\", line 84, in execute_cmd\\n    return frappe.call(method, **frappe.form_dict)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/__init__.py\\", line 1751, in call\\n    return fn(*args, **newargs)\\n           ^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/utils/typing_validations.py\\", line 31, in wrapper\\n    return func(*args, **kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/desk/form/save.py\\", line 39, in savedocs\\n    doc.save()\\n  File \\"apps/frappe/frappe/model/document.py\\", line 378, in save\\n    return self._save(*args, **kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File \\"apps/frappe/frappe/model/document.py\\", line 408, in _save\\n    self.check_if_latest()\\n  File \\"apps/frappe/frappe/model/document.py\\", line 863, in check_if_latest\\n    frappe.msgprint(\\n  File \\"apps/frappe/frappe/__init__.py\\", line 574, in msgprint\\n    _raise_exception()\\n  File \\"apps/frappe/frappe/__init__.py\\", line 525, in _raise_exception\\n    raise exc\\nfrappe.exceptions.TimestampMismatchError: Error: Document has been modified after you have opened it (2025-07-04 17:09:38.661305, 2025-07-04 17:09:38.704886). Please refresh to get the latest document.\\n"]',
    "_server_messages": '["{\\"message\\": \\"Error: Document has been modified after you have opened it (2025-07-04 17:09:38.661305, 2025-07-04 17:09:38.704886). Please refresh to get the latest document.\\", \\"title\\": \\"Message\\", \\"indicator\\": \\"red\\", \\"raise_exception\\": 1, \\"__frappe_exc_id\\": \\"76dc3bb02a9117d010748b3f8c40776303374503333c15e86239abd9\\"}"]',
}
