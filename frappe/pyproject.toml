[project]
name = "frappe"
authors = [
    { name = "Frappe Technologies Pvt Ltd", email = "<EMAIL>"}
]
description = "Metadata driven, full-stack low code web framework"
requires-python = ">=3.10,<3.14"
readme = "README.md"
dynamic = ["version"]
dependencies = [
    # core dependencies
    "Babel~=2.13.1",
    "Click~=8.2.0",
    "filelock~=3.13.1",
    "filetype~=1.2.0",
    "GitPython~=3.1.34",
    "Jinja2~=3.1.2",
    "Pillow~=11.0.0",
    "PyJWT~=2.8.0",
    # We depend on internal attributes,
    # do NOT add loose requirements on PyMySQL versions.
    "PyMySQL==1.1.1",
    "pypdf~=3.17.0",
    "PyPika==0.48.9",
    "PyQRCode~=1.2.1",
    "PyYAML~=6.0.2",
    "RestrictedPython~=8.0",
    "WeasyPrint==59.0",
    "pydyf==0.10.0",
    "Werkzeug~=3.0.1",
    "Whoosh~=2.7.4",
    "beautifulsoup4~=4.12.2",
    "bleach-allowlist~=1.0.3",
    "bleach[css]~=6.0.0",
    "cairocffi==1.5.1",
    "chardet~=5.1.0",
    "croniter~=2.0.1",
    "cryptography~=44.0.1",
    "cssutils~=2.9.0",
    "email-reply-parser~=0.5.12",
    "gunicorn @ git+https://github.com/frappe/gunicorn@bb554053bb87218120d76ab6676af7015680e8b6",
    "html5lib~=1.1",
    "ipython~=8.15.0",
    "ldap3~=2.9",
    "markdown2~=2.4.8",
    "MarkupSafe>=2.1.0,<3",
    "maxminddb-geolite2==2018.703",
    "num2words~=0.5.12",
    "oauthlib~=3.2.2",
    "openpyxl~=3.1.2",
    "passlib~=1.7.4",
    "pdfkit~=1.0.0",
    "phonenumbers==8.13.55",
    "premailer~=3.10.0",
    "psutil~=5.9.5",
    "psycopg2-binary~=2.9.1",
    "pyOpenSSL~=25.0.0",
    "pydantic~=2.10.2",
    "pyotp~=2.8.0",
    "python-dateutil~=2.8.2",
    "pytz==2023.3",
    "rauth~=0.7.3",
    "redis~=4.5.5",
    "hiredis~=2.2.3",
    "requests-oauthlib~=1.3.1",
    "requests~=2.32.0",
    "rq==1.15.1",
    "rsa>=4.1",
    "semantic-version~=2.10.0",
    "sentry-sdk~=1.45.1",
    "sqlparse~=0.5.0",
    "sql_metadata~=2.11.0",
    "tenacity~=8.2.2",
    "terminaltables~=3.1.10",
    "traceback-with-variables~=2.0.4",
    "typing_extensions>=4.6.1,<5",
    "tomli~=2.0.1",
    "xlrd~=2.0.1",
    "zxcvbn~=4.4.28",
    "markdownify~=0.14.1",

    # integration dependencies
    "boto3~=1.34.143",
    "dropbox~=11.36.2",
    "google-api-python-client~=2.2.0",
    "google-auth-oauthlib~=0.4.4",
    "google-auth~=1.29.0",
    "posthog~=3.21.0",
    "vobject~=0.9.7",
]

[project.urls]
Homepage = "https://frappeframework.com/"
Repository = "https://github.com/frappe/frappe.git"
"Bug Reports" = "https://github.com/frappe/frappe/issues"

[build-system]
requires = ["flit_core >=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.bench.dev-dependencies]
coverage = "~=6.5.0"
Faker = "~=18.10.1"
pyngrok = "~=6.0.0"
unittest-xml-reporting = "~=3.2.0"
watchdog = "~=3.0.0"
hypothesis = "~=6.77.0"
responses = "==0.23.1"
freezegun = "~=1.2.2"

[tool.ruff]
line-length = 110
target-version = "py310"

[tool.ruff.lint]
select = [
    "F",
    "E",
    "W",
    "I",
    "UP",
    "B",
    "RUF",
]
ignore = [
    "B017", # assertRaises(Exception) - should be more specific
    "B018", # useless expression, not assigned to anything
    "B023", # function doesn't bind loop variable - will have last iteration's value
    "B904", # raise inside except without from
    "E101", # indentation contains mixed spaces and tabs
    "E402", # module level import not at top of file
    "E501", # line too long
    "E741", # ambiguous variable name
    "F401", # "unused" imports
    "F403", # can't detect undefined names from * import
    "F405", # can't detect undefined names from * import
    "F722", # syntax error in forward type annotation
    "W191", # indentation contains tabs
]
typing-modules = ["frappe.types.DF"]

[tool.ruff.format]
quote-style = "double"
indent-style = "tab"
docstring-code-format = true

[tool.frappix]
# use identifier from https://search.nixos.org/packages
nixpkgs-deps = [
    "mariadb",
    "restic",
    "wkhtmltopdf-bin",
    "which",
    "gzip",
    "bash",
    "redis",
    "nodejs_20",
    "python312",
]
