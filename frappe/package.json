{"name": "frappe-framework", "scripts": {"build": "node esbuild", "production": "node esbuild --production", "watch": "node esbuild --watch", "coverage:report": "npx nyc report --reporter=clover"}, "repository": {"type": "git", "url": "git+https://github.com/frappe/frappe.git"}, "author": "Frappe Technologies Pvt. Ltd.", "license": "MIT", "bugs": {"url": "https://github.com/frappe/frappe/issues"}, "engines": {"node": ">=18"}, "homepage": "https://frappeframework.com", "dependencies": {"@editorjs/editorjs": "~2.26.3", "@frappe/esbuild-plugin-postcss2": "^0.1.3", "@headlessui/vue": "^1.7.16", "@popperjs/core": "^2.11.2", "@redis/client": "^1.5.8", "@sentry/browser": "^7.119.1", "@vue-flow/background": "^1.1.0", "@vue-flow/core": "^1.16.2", "@vue/component-compiler": "^4.2.4", "@vueuse/core": "^9.5.0", "ace-builds": "^1.4.8", "air-datepicker": "github:frappe/air-datepicker", "autoprefixer": "10", "awesomplete": "^1.1.5", "bootstrap": "4.6.2", "chalk": "^2.3.2", "cliui": "^7.0.4", "cookie": "^0.4.0", "cropperjs": "^1.5.12", "cssnano": "^5.0.0", "driver.js": "^0.9.8", "editorjs-undo": "0.1.6", "esbuild": "^0.14.29", "esbuild-plugin-vue3": "^0.3.0", "fast-deep-equal": "^2.0.1", "fast-glob": "^3.2.5", "frappe-charts": "^2.0.0-rc26", "frappe-datatable": "1.19.0", "frappe-gantt": "^0.6.0", "highlight.js": "^10.4.1", "html5-qrcode": "^2.3.8", "jquery": "3.7.0", "js-sha256": "^0.9.0", "jsbarcode": "^3.11.0", "launch-editor": "^2.2.1", "localforage": "^1.10.0", "md5": "^2.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.35", "pinia": "^2.0.23", "plyr": "^3.7.8", "popper.js": "^1.16.0", "postcss": "8", "quill": "2.0.3", "frappe-quill-image-resize": "^3.0.9", "quill-magic-url": "^3.0.0", "qz-tray": "^2.0.8", "rtlcss": "^4.0.0", "sass": "^1.63.0", "showdown": "^2.1.0", "socket.io": "^4.7.1", "socket.io-client": "^4.7.1", "sortablejs": "^1.15.0", "superagent": "^8.0.0", "touch": "^3.1.0", "vue": "^3.3.0", "vue-router": "^4.1.5", "vuedraggable": "^4.1.0", "vuex": "4.0.2", "yargs": "^17.5.1"}, "nyc": {"report-dir": ".cypress-coverage"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.3"}}