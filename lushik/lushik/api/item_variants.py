import frappe

@frappe.whitelist(allow_guest=True)
def get_item_variants_and_routes(item_code):
    item = frappe.get_value(
        "Item", {"item_code": item_code}, ["name", "has_variants"], as_dict=True
    )
    if not item or not item.has_variants:
        return {"variants": [], "routes": {}}

    variants = frappe.get_all(
        "Item", filters={"variant_of": item_code}, fields=["name", "item_name", "image"]
    )
    variant_names = [v["name"] for v in variants]

    routes = frappe.get_all(
        "Website Item",
        filters={"item_code": ["in", variant_names]},
        fields=["item_code", "route"],
    )

    route_map = {r["item_code"]: r["route"] for r in routes}
    return {"variants": variants, "routes": route_map}
