{% extends "templates/web.html" %}
{% from "webshop/templates/includes/macros.html" import recommended_item_row %}



{% block breadcrumbs %}
<div class="item-breadcrumbs small text-muted">
    {% include "templates/includes/breadcrumbs.html" %}
</div>
{% endblock %}

{% block page_content %}

<div class="product-container item-main">
    {% from "webshop/templates/includes/macros.html" import product_image %}
    <div class="item-content">
        <div class="product-page-content" itemscope itemtype="http://schema.org/Product">
            <div class="row mb-5">
                {% include "templates/generators/item/item_image.html" %}
                {% include "templates/generators/item/item_details.html" %}
            </div>
        </div>
    </div>
</div>

<!-- Additional Info/Reviews, Recommendations -->
<div class="d-flex flex-wrap">
    {% set show_recommended_items = recommended_items and shopping_cart.cart_settings.enable_recommendations %}
    {% set info_col = 'col-lg-9 col-12' if show_recommended_items else 'col-12' %}
    {% set padding_top = 'pt-0' if (show_tabs and tabs) else '' %}

    <div class="product-container mt-4 {{ padding_top }} {{ info_col }}">
        <div class="item-content {{ 'mt-minus-2' if (show_tabs and tabs) else '' }}">
            <div class="product-page-content" itemscope itemtype="http://schema.org/Product">
                {% if show_tabs and tabs %}
                <div class="category-tabs">
                    {{ web_block("Section with Tabs", values=tabs, add_container=0, add_top_padding=0,
                    add_bottom_padding=0) }}
                </div>
                {% elif website_specifications %}
                {% include "templates/generators/item/item_specifications.html" %}
                {% endif %}

                {{ doc.website_content or '' }}

                {% if shopping_cart.cart_settings.enable_reviews and not doc.has_variants %}
                {% include "templates/generators/item/item_reviews.html" %}
                {% endif %}
            </div>
        </div>


        <!-- Variants List in Card Format -->
        <div id="variants-debug" class="mt-4">
            <div class="row" id="variants-container"></div>
        </div>
    </div>

    {% if show_recommended_items %}
    <div class="mt-4 col-lg-3 col-12 recommended-item-section">
        <span class="recommendation-header">{{ _("Recommended") }}</span>
        <div class="product-container mt-2 recommendation-container">
            {% for item in recommended_items %}
            {{ recommended_item_row(item) }}
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block base_scripts %}
<script type="text/javascript" src="/assets/frappe/js/lib/jquery/jquery.min.js"></script>
{{ include_script("frappe-web.bundle.js") }}
{{ include_script("controls.bundle.js") }}
{{ include_script("dialog.bundle.js") }}

<script>
    frappe.ready(function () {
        const itemCode = "{{ doc.item_code }}";
        console.log("Item Code Used in Variant Lookup:", itemCode);

    });

    let currentView = 'groups';
    let currentItemCode = '';
    let currentItemGroup = '';



    function loadVariants(itemCode) {
        frappe.call({
            method: "moonstar.api.item_variants.get_item_variants_and_routes",
            args: { item_code: itemCode },
            callback: function (response) {
                if (response && response.message) {
                    const variants = response.message.variants || [];
                    const routeMap = response.message.routes || {};
                    if (variants.length > 0) {
                        displayVariantsCard(variants, routeMap);
                    }
                }
            }
        });
    }



    function displayVariantsCard(variants, routeMap, itemGroupName = null) {
        const variantsContainer = document.getElementById("variants-container");
        if (!variantsContainer) return;
        variantsContainer.innerHTML = '';

        if (!variants || variants.length === 0) {
            variantsContainer.innerHTML = "<p>No variants found for this item.</p>";
            return;
        }

        // Back button and heading wrapper
        const headingWrapper = document.createElement("div");
        headingWrapper.className = "w-100 mb-4";

        let headingContent = '';
        if (itemGroupName && currentView === 'variants') {
            headingContent = `
                <h2 class="item-variant-title text-center">${itemGroupName}</h2>
            `;
        } else {
            headingContent = `<h2 class="item-variant-title text-center">Explore the Flavour Family</h2>`;
        }

        headingWrapper.innerHTML = headingContent;
        variantsContainer.appendChild(headingWrapper);

        // Cards row wrapper
        const rowWrapper = document.createElement("div");
        rowWrapper.className = "row";


        variants.forEach(function (variant) {
            const route = routeMap[variant.name] || "#";
            const imageSrc = variant.image && variant.image.trim() !== "" ? variant.image : variant.image;

            const variantCard = document.createElement("div");
            variantCard.className = "col-lg-4 col-md-6 col-12 mb-4";
            variantCard.innerHTML = `
                <div class="item-variant-card h-100 shadow-sm">
                    <img src="${imageSrc}"
                        class="card-img-top justify-content-center align-items-center text-center" 
                        alt="${variant.item_name}">
                    
                    <hr>
                    
                    <div class="card-body d-flex flex-column justify-content-center align-items-center text-center">
                        <h6 class="card-title">${variant.item_name}</h6>
                        <a href="/${route}" class="btn btn-primary mt-auto">View Details</a>
                    </div>
                </div>
            `;
            rowWrapper.appendChild(variantCard);
        });

        variantsContainer.appendChild(rowWrapper);
    }

</script>
{% endblock %}