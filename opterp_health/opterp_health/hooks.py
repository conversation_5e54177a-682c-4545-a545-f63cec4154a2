app_name = "opterp_health"
app_title = "Opterp Health"
app_publisher = "<PERSON><PERSON><PERSON>"
app_description = "Opterp Health"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "opterp_health",
# 		"logo": "/assets/opterp_health/logo.png",
# 		"title": "opterp_health",
# 		"route": "/opterp_health",
# 		"has_permission": "opterp_health.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/opterp_health/css/opterp_health.css"

# make this a dict:
www_include_js = ["/assets/opterp_health/js/www/patient_appointment.js",
                  "/assets/opterp_health/js/www/button_actions.js"]

app_include_js = [
    "/assets/opterp_health/js/patient_quick_entry.js",
    "/assets/opterp_health/js/observation_widget.js",
    "/assets/opterp_health/js/patient_appointment.js",
]
# include js, css files in header of web template
# web_include_css = "/assets/opterp_health/css/opterp_health.css"
# web_include_js = "/assets/opterp_health/js/opterp_health.js"
# web_include_js = ["/assets/frappe/js/frappe-web.bundle.js"]

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "opterp_health/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"point-of-sale" : "public/js/pos.js"}

# include js in doctype views
doctype_js = {"Patient" : "public/js/patient.js", 
              "Patient Appointment": "public/js/patient_appointment.js", 
              "Patient Encounter": "public/js/patient_encounter.js", 
              "POS Invoice": "public/js/pos_invoice.js",
              "Sales Invoice": "public/js/sales_invoice.js",
              "Delivery Note": "public/js/delivery_note.js",
              "Sample Collection": "public/js/sample_collection.js"
              }
doctype_list_js = {"Service Request" : "public/js/service_request_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "opterp_health/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "opterp_health.utils.jinja_methods",
# 	"filters": "opterp_health.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "opterp_health.install.before_install"
after_install = "opterp_health.install.after_install"

# Uninstallation
# ------------

before_uninstall = "opterp_health.uninstall.before_uninstall"
# after_uninstall = "opterp_health.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "opterp_health.utils.before_app_install"
# after_app_install = "opterp_health.utils.after_app_install"
# fixtures = [
#     {
#         "dt": "Role",
#         "filters": [["name", "in", [
#             "Patient", 
#             "Healthcare Administrator", 
#             "Physician", 
#             "Nursing User",  
#             "Counter",
#             "Pharmacy"
#         ]]]
#     },
#     {
#         "dt": "Custom DocPerm",
#         "filters": [["role", "in", [
#             "Patient", 
#             "Healthcare Administrator", 
#             "Physician", 
#             "Nursing User", 
#             "Counter",
#             "Pharmacy"
#         ]]]
#     },
# ]
# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "opterp_health.utils.before_app_uninstall"
# after_app_uninstall = "opterp_health.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "opterp_health.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }
override_doctype_class = {
    "Sales Invoice": "opterp_health.opterp_health.sales_invoice.HealthSalesInvoice",
    "POS Invoice": "opterp_health.opterp_health.pos_invoice.HealthcarePOSInvoice",
    "Patient": "opterp_health.opterp_health.patient.CustomPatient",
    "Diagnostic Report": "opterp_health.opterp_health.diagnostic_report.DiagnosticReport",
    "Observation": "opterp_health.opterp_health.observation.Observation",
    "Inpatient Record": "opterp_health.overrides.inpatient_record.InpatientRecord"
}
# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }
doc_events = {
    # "Patient": {
    #     "on_update": "opterp_health.opterp_health.patient_registration.invoice_patient_registration_on_update",
    # },
    "POS Invoice": {
        "on_submit": "opterp_health.opterp_health.utils.manage_invoice_submit_cancel",
        "on_cancel": "opterp_health.opterp_health.utils.manage_invoice_submit_cancel",
        "validate": "opterp_health.opterp_health.utils.manage_invoice_validate",
    },
    "Vital Signs": {
        "on_submit": "opterp_health.methods.vital_signs.vital_signs",
        "on_cancel": "opterp_health.methods.vital_signs.vital_signs",
    },
    "Sales Invoice": {
        "on_update": "opterp_health.methods.sales_invoice.sales_invoice",
        "on_cancel": "opterp_health.methods.sales_invoice.sales_invoice",
    }
}
# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"opterp_health.tasks.all"
# 	],
# 	"daily": [
# 		"opterp_health.tasks.daily"
# 	],
# 	"hourly": [
# 		"opterp_health.tasks.hourly"
# 	],
# 	"weekly": [
# 		"opterp_health.tasks.weekly"
# 	],
# 	"monthly": [
# 		"opterp_health.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "opterp_health.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "opterp_health.event.get_events"
# }
override_whitelisted_methods = {
    "healthcare.healthcare.utils.get_healthcare_services_to_invoice": "opterp_health.opterp_health.utils.get_healthcare_services_to_invoice",
    "healthcare.healthcare.doctype.sample_collection.sample_collection.create_observation": "opterp_health.opterp_health.utils.create_observation",
    "healthcare.healthcare.doctype.observation.observation.add_observation": "opterp_health.opterp_health.utils.add_observation",
    "healthcare.healthcare.utils.create_sample_collection": "opterp_health.opterp_health.utils.create_sample_collection",
    "healthcare.healthcare.doctype.observation.observation.get_observation_details": "opterp_health.opterp_health.observation.get_observation_details",
    "healthcare.healthcare.doctype.diagnostic_report.diagnostic_report.set_observation_status": "opterp_health.opterp_health.diagnostic_report.set_observation_status",
}
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "opterp_health.task.get_dashboard_data"
# }
override_doctype_dashboards = {
    "Patient Encounter": "opterp_health.opterp_health.patient_encounter_dashboard.get_data",
}
# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["opterp_health.utils.before_request"]
# after_request = ["opterp_health.utils.after_request"]

# Job Events
# ----------
# before_job = ["opterp_health.utils.before_job"]
# after_job = ["opterp_health.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"opterp_health.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }

website_route_rules = [{'from_route': '/portal/<path:app_path>', 'to_route': 'portal'},]