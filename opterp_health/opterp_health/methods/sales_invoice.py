import frappe

def sales_invoice(doc, method):
    if method == 'before_save':
        for item in doc.items:
            if item.reference_dt == "Patient Appointment" and item.reference_dn:
                try:
                    appointment = frappe.get_doc("Patient Appointment", item.reference_dn)
                    appointment.paid_amount = item.amount
                    appointment.save(ignore_permissions=True)
                except frappe.DoesNotExistError:
                    frappe.log_error(f"Patient Appointment {item.reference_dn} not found", "Sales Invoice Sync")

    elif method == 'on_cancel':
        for item in doc.items:
            if item.reference_dt == "Patient Appointment" and item.reference_dn:
                try:
                    appointment = frappe.get_doc("Patient Appointment", item.reference_dn)
                    appointment.paid_amount = 0
                    appointment.save(ignore_permissions=True)
                except frappe.DoesNotExistError:
                    frappe.log_error(f"Patient Appointment {item.reference_dn} not found during cancel", "Sales Invoice Sync")
