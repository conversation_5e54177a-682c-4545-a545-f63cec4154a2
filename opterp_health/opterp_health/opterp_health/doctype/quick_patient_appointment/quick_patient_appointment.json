{"actions": [], "allow_rename": 1, "creation": "2025-04-17 11:38:06.542393", "doctype": "DocType", "engine": "InnoDB", "field_order": ["patient", "patient_name", "sex", "mobile_no", "age", "dob", "column_break_jnwn", "appointment_type", "appointment_for", "department", "healthcare_practitioner", "practitioner_name", "service_unit", "date", "appointment_time", "section_break_sctt", "practitioner_schedule"], "fields": [{"fieldname": "sex", "fieldtype": "Link", "label": "Gender", "options": "Gender", "reqd": 1}, {"fieldname": "mobile_no", "fieldtype": "Phone", "label": "Mobile"}, {"fieldname": "column_break_jnwn", "fieldtype": "Column Break"}, {"fieldname": "appointment_type", "fieldtype": "Link", "label": "Appointment Type", "options": "Appointment Type", "reqd": 1}, {"fieldname": "patient_name", "fieldtype": "Data", "label": "Patient Name", "reqd": 1}, {"fieldname": "appointment_for", "fieldtype": "Select", "label": "Appointment For", "options": "\nPractitioner\nDepartment\nService Unit", "reqd": 1, "set_only_once": 1}, {"depends_on": "eval:doc.appointment_for==\"Practitioner\"", "fieldname": "healthcare_practitioner", "fieldtype": "Link", "in_standard_filter": 1, "label": "Healthcare Practitioner", "options": "Healthcare Practitioner", "search_index": 1, "set_only_once": 1}, {"fieldname": "practitioner_name", "fieldtype": "Data", "label": "Practitioner Name", "read_only": 1}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Medical Department", "search_index": 1}, {"fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Appointment Date", "reqd": 1, "search_index": 1}, {"fieldname": "practitioner_schedule", "fieldtype": "HTML", "label": "Practioner Schedule"}, {"fieldname": "appointment_time", "fieldtype": "Time", "label": "Appointment Time", "read_only": 1}, {"fieldname": "service_unit", "fieldtype": "Link", "label": "Service Unit", "options": "Healthcare Service Unit", "read_only": 1}, {"description": "Leave empty to create new patient.", "fieldname": "patient", "fieldtype": "Link", "label": "Patient", "options": "Patient"}, {"fieldname": "section_break_sctt", "fieldtype": "Section Break"}, {"fieldname": "age", "fieldtype": "Data", "label": "Age", "non_negative": 1, "reqd": 1}, {"fieldname": "dob", "fieldtype": "Date", "label": "Date of Birth"}], "index_web_pages_for_search": 1, "is_virtual": 1, "links": [], "modified": "2025-06-19 10:42:28.348097", "modified_by": "Administrator", "module": "Opterp Health", "name": "Quick Patient Appointment", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}