# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from datetime import date
from dateutil.relativedelta import relativedelta

def get_dob_from_age(age):
    try:
        age = int(age)
        if age < 0 or age > 125:
            raise ValueError("Age must be between 0 and 125.")
    except (ValueError, TypeError):
        frappe.throw("Invalid age. Please enter a number between 0 and 125.")

    today = date.today()
    dob = today - relativedelta(years=age)
    return dob


class QuickPatientAppointment(Document):

	def delete(self):
		pass

	def db_insert(self, *args, **kwargs):
		pass

	def load_from_db(self):
		pass

	def db_update(self):
		pass

	@staticmethod
	def get_list(args):
		return []

	@staticmethod
	def get_count(args):
		return 0

	@staticmethod
	def get_stats(args):
		return {}


@frappe.whitelist()
def quick_admit(data):
    try:
        data = frappe._dict(frappe.parse_json(data))
    except Exception as e:
        frappe.throw(_("Invalid data format: {0}").format(str(e)))
    
    if not data.patient:
        missing = []

        if not data.patient_name:
            missing.append("Patient Name")
        if not data.sex:
            missing.append("Sex")
        if not any([data.age,data.dob]):
            missing.append("Age/DOB")

        if missing:
            frappe.throw(_("The following fields are mandatory: {0}").format(", ".join(missing)))

        try:
            name_parts = data.patient_name.strip().split()
            first_name = name_parts[0]

            if len(name_parts) == 1:
                middle_name = ""
                last_name = "_"
            elif len(name_parts) == 2:
                middle_name = ""
                last_name = name_parts[1]
            else:
                middle_name = " ".join(name_parts[1:-1])
                last_name = name_parts[-1]

            if data.age:
                try:
                    age = int(data.age)
                except (ValueError, TypeError):
                    frappe.throw("Age must be a number.")

                if age < 0 or age > 125:
                    frappe.throw("Age must be between 0 and 125.")
                
        except Exception as e:
            frappe.throw(_("Error parsing patient name: {0}").format(str(e)))

        try:
            patient = frappe.get_doc({
                "doctype": "Patient",
                "first_name": first_name,
                "middle_name": middle_name,
                "last_name": last_name,
                "sex": data.sex,
                "mobile": data.get("mobile_no"),
                "dob": data.dob if data.dob else get_dob_from_age(data.age)
            })
            patient.insert(ignore_permissions=True)
        except Exception as e:
            frappe.log_error(frappe.get_traceback(), "Quick Admit - Patient Insert Error")
            frappe.throw(_("Failed to create patient: {0}").format(str(e)))
    else:
        patient = frappe.get_doc("Patient", data.patient)

    try:
        appointment = frappe.get_doc({
            "doctype": "Patient Appointment",
            "patient": patient.name,
            "appointment_type": data.appointment_type,
            "appointment_for": data.appointment_for,
            "practitioner": data.get("healthcare_practitioner") if data.appointment_for == "Practitioner" else None,
            "department": data.get("department") if data.appointment_for == "Department" else None,
            "appointment_date": data.get("date"),
            "appointment_time": data.get("appointment_time"),
            "service_unit": data.get("service_unit"),
            "status": "Open",
            "company": data.get("company") or frappe.defaults.get_user_default("Company"),
        })
        appointment.insert(ignore_permissions=True)
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Quick Admit - Appointment Insert Error")
        frappe.throw(_("Failed to create appointment: {0}").format(str(e)))

    return {"patient": patient.name, "appointment": appointment.name}
