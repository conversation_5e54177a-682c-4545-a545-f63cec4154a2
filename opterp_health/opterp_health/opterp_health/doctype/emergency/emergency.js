// Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Emergency", {
	onload(frm) {
		if (frm.is_new()) {
			frm.set_value("arrival_time", frappe.datetime.now_datetime());
		}

		if (frm.doc.patient) {
			set_patient_details(frm, frm.doc.patient);
		}
	},
	patient(frm) {
		if (frm.doc.patient) {
			set_patient_details(frm, frm.doc.patient);
		}
	},
	refresh(frm) {
    	if (!frm.is_new()) {
			if (!frm.doc.vitals && frm.doc.docstatus === 0){
				frm.add_custom_button("Take Vitals", () => {
					window.workstationInstance.renderNewForm("Vital Signs", "#content-section", {
						doc_vals: {
							patient: frm.doc.patient,
							emergency: frm.doc.name
						}	
					});
				});
			}
			frm.add_custom_button("Make Encounter", () => {
					window.workstationInstance.renderNewF<PERSON>("Patient Encounter", "#content-section", {
						doc_vals: {
							patient: frm.doc.patient,
							emergency: frm.doc.name
						}	
					});
				});

        }
	}
});

function set_patient_details(frm, patient_id) {
	frappe.db.get_value("Patient", patient_id, ["patient_name", "sex", "dob"])
		.then(({ message }) => {
			if (message) {
				const { patient_name, sex, dob } = message;

				if (patient_name) frm.set_value("patient_name", patient_name);
				if (sex) frm.set_value("sex", sex);
				if (dob) {
					const age = calculate_age(dob);
					frm.set_value("age", age);
				}

				frm.set_df_property("patient_name", "read_only", 1);
				frm.set_df_property("sex", "read_only", 1);
				frm.set_df_property("age", "read_only", 1);
			}
		});
}

function calculate_age(dob) {
	const birthDate = new Date(dob);
	const today = new Date();

	let age = today.getFullYear() - birthDate.getFullYear();
	const m = today.getMonth() - birthDate.getMonth();

	if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
		age--;
	}

	return age;
}
