{"actions": [], "allow_rename": 1, "creation": "2025-03-19 15:51:50.684416", "doctype": "DocType", "engine": "InnoDB", "field_order": ["sms_settings_tab", "send_sms", "success_message", "failure_message", "sms_appointment_details", "appointment_confirmation_message", "auto_fill_outstanding_services", "column_break_kpgu", "max_otp_attempts", "otp_expiration_in_seconds", "sms_new_registration_information", "new_registration_message", "tab_2_tab", "emergency_department", "enable_booking_request", "default_patient_landing_page", "default_prac_img"], "fields": [{"fieldname": "sms_settings_tab", "fieldtype": "Tab Break", "label": "SMS Settings"}, {"default": "True", "fieldname": "send_sms", "fieldtype": "Check", "label": "Send SMS"}, {"fieldname": "success_message", "fieldtype": "Data", "label": "Success Message"}, {"fieldname": "failure_message", "fieldtype": "Data", "label": "Failure Message"}, {"fieldname": "otp_expiration_in_seconds", "fieldtype": "Float", "label": "OTP expiration in seconds", "non_negative": 1, "precision": "0"}, {"fieldname": "column_break_kpgu", "fieldtype": "Column Break"}, {"default": "3", "fieldname": "max_otp_attempts", "fieldtype": "Float", "label": "Max OTP attempts", "non_negative": 1, "precision": "0"}, {"fieldname": "tab_2_tab", "fieldtype": "Tab Break", "label": "De<PERSON>ults"}, {"default": "True", "fieldname": "sms_appointment_details", "fieldtype": "Check", "label": "SMS Appointment Details"}, {"description": "Variable options: \nappointment_name, appointment_date, practioner", "fieldname": "appointment_confirmation_message", "fieldtype": "Data", "label": "Appointment Confirmation Message"}, {"default": "0", "fieldname": "auto_fill_outstanding_services", "fieldtype": "Check", "label": "Auto Fill Outstanding Healthcare Services"}, {"default": "0", "fieldname": "sms_new_registration_information", "fieldtype": "Check", "label": "SMS New Registration Information"}, {"depends_on": "eval:doc.sms_new_registration_information == 1", "fieldname": "new_registration_message", "fieldtype": "Data", "label": "New Registration Message"}, {"fieldname": "emergency_department", "fieldtype": "Link", "label": "Emergency Department", "options": "Medical Department"}, {"default": "0", "fieldname": "enable_booking_request", "fieldtype": "Check", "label": "Enable booking request"}, {"fieldname": "default_patient_landing_page", "fieldtype": "Link", "label": "Default <PERSON> Landing Page", "options": "Web Page"}, {"fieldname": "default_prac_img", "fieldtype": "Attach", "label": "Default Practitioner Image"}], "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-07-02 13:19:51.241786", "modified_by": "Administrator", "module": "Opterp Health", "name": "Opterp Health Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}