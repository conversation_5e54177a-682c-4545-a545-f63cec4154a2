frappe.ui.form.on('Transfer Request', {
    inpatient_records: function(frm) {
        if (frm.doc.inpatient_records) {
            frappe.db.get_doc('Inpatient Record', frm.doc.inpatient_records)
                .then(doc => {
                    if (doc.inpatient_occupancies && doc.inpatient_occupancies.length > 0) {
                        let data = doc.inpatient_occupancies[doc.inpatient_occupancies.length - 1];

                        frm.set_value('source_unit', data.service_unit);
                    } else {
                        frappe.msgprint('No occupancy data found in the selected Inpatient Record.');
                    }
                });
        }
    },
    refresh(frm) {
        if (frm.doc.transfer_status === "Requested") {
            if (!frm.doc.__islocal) {
            frm.add_custom_button("Accept Request", () => {
                frappe.db.get_doc("Inpatient Record", frm.doc.inpatient_records).then(doc => {
                    frappe.call({
                        doc: doc,
                        method: 'transfer',
                        args: {
                            service_unit: frm.doc.destination_unit,
                            check_in: frm.doc.date,
                            leave_from: frm.doc.source_unit
                        },
                        callback: function(data) {
                            if (!data.exc) {
                                frm.set_value("transfer_status", "Accepted");
                                frm.save().then(() => {
                                    frappe.msgprint("Transfer request accepted.");
                                    frm.reload_doc();
                                });
                            }
                        },
                        freeze: true,
                        freeze_message: __('Processing Transfer...')
                    });
                });
            });
    
            frm.add_custom_button("Reject Request", () => {
                frm.set_value("transfer_status", "Rejected");
                frm.save().then(() => {
                    frappe.msgprint("Transfer request rejected.");
                    frm.reload_doc();
                });
            });
        }
    }
    }, 
    onload(frm) {
        if (frm.doc.__islocal) {
            frm.set_value('date', frappe.datetime.now_datetime());
        }
    }, 
    source_unit: function(frm) {
        frm.set_query('destination_unit', function() {
            return {
                filters: [
                    ['name', '!=', frm.doc.source_unit],
                    ['occupancy_status', '=', 'Vacant'],
                    ['is_group', '=', 0] 
                ]
            };
        });
    },

    patient: function(frm) {
    if (frm.doc.patient) {
        frappe.db.get_doc('Patient', frm.doc.patient).then(doc => {
            if (doc.inpatient_record) {
                frm.set_value('inpatient_records', doc.inpatient_record);
            } else {
                frappe.msgprint('No inpatient record found for the selected patient.');
                frm.set_value('inpatient_records', '');
            }
        });
    } else {
        frm.set_value('inpatient_records', '');
    }
}


  
});
