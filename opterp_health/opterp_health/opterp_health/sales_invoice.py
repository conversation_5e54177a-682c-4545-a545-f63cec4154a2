import frappe
from healthcare.healthcare.custom_doctype.sales_invoice import HealthcareSalesInvoice
from opterp_health.opterp_health.utils import get_healthcare_services_to_invoice
from erpnext.stock.get_item_details import get_item_details

class HealthSalesInvoice(HealthcareSalesInvoice):
    @frappe.whitelist()
    def auto_fill_services(self):
        if not self.patient:
            frappe.throw("Please select a Patient first")

        if not self.customer:
            self.customer = frappe.db.get_value("Patient", self.patient, "customer")
            if not self.customer:
                frappe.throw("Please select a Customer first")

        services = get_healthcare_services_to_invoice(
            patient=self.patient,
            customer=self.customer,
            company=self.company,
            link_customer=False
        )

        if not services:
            # frappe.msgprint("No unbilled services found for this patient")
            return

        for service in services:
            item_line = self.append("items", {})
            price_list, price_list_currency = frappe.db.get_values(
                "Price List", {"selling": 1}, ["name", "currency"]
            )[0]
            
            args = {
                "doctype": "Sales Invoice",
                "item_code": service.get("service"),
                "company": self.company,
                "customer": self.customer,
                "selling_price_list": price_list,
                "price_list_currency": price_list_currency,
                "plc_conversion_rate": 1.0,
                "conversion_rate": 1.0,
            }
            
            item_details = get_item_details(args)
            
            item_line.item_code = service.get("service")
            item_line.qty = service.get("qty", 1)
            item_line.rate = item_details.price_list_rate
            item_line.amount = float(item_line.rate) * float(item_line.qty)
            item_line.reference_dt = service.get("reference_type")
            item_line.reference_dn = service.get("reference_name")
            item_line.description = service.get("description")
            
            if service.get("income_account"):
                item_line.income_account = service.get("income_account")
                
            
            if service.get("reference_type") == "Service Request":
                service_request = frappe.get_doc("Service Request", service.get("reference_name"))
                item_line.practitioner = service_request.practitioner

            if service.get("reference_type") == "Clinical Procedure":
                proc = frappe.get_doc("Clinical Procedure", service.get("reference_name"))
                item_line.service_unit = proc.service_unit
                item_line.medical_department = proc.medical_department
                item_line.practitioner = proc.practitioner
                
            elif service.get("reference_type") == "Patient Appointment":
                appt = frappe.get_doc("Patient Appointment", service.get("reference_name"))
                item_line.practitioner = appt.practitioner
                item_line.department = appt.department
                
            elif service.get("reference_type") == "Lab Test":
                lab = frappe.get_doc("Lab Test", service.get("reference_name"))
                item_line.service_unit = lab.service_unit
                item_line.practitioner = lab.practitioner
                item_line.medical_department = lab.department

        self.set_missing_values(for_validate=True)
        # frappe.msgprint("Services have been added to the invoice")

    @frappe.whitelist()
    def set_healthcare_services(self, checked_values):

        for checked_item in checked_values:
            item_line = self.append("items", {})
            price_list, price_list_currency = frappe.db.get_values(
                "Price List", {"selling": 1}, ["name", "currency"]
            )[0]
            args = {
                "doctype": "Sales Invoice",
                "item_code": checked_item["item"],
                "company": self.company,
                "customer": frappe.db.get_value("Patient", self.patient, "customer"),
                "selling_price_list": price_list,
                "price_list_currency": price_list_currency,
                "plc_conversion_rate": 1.0,
                "conversion_rate": 1.0,
            }
            
            item_details = get_item_details(args)
            item_line.item_code = checked_item["item"]
            item_line.qty = 1
            if checked_item["qty"]:
                item_line.qty = checked_item["qty"]
            if checked_item["rate"]:
                item_line.rate = checked_item["rate"]
            else:
                item_line.rate = item_details.price_list_rate
            item_line.amount = float(item_line.rate) * float(item_line.qty)
            if checked_item["income_account"]:
                item_line.income_account = checked_item["income_account"]
            if checked_item["dt"]:
                item_line.reference_dt = checked_item["dt"]
            if checked_item["dn"]:
                item_line.reference_dn = checked_item["dn"]
            if checked_item["description"]:
                item_line.description = checked_item["description"]
                
            if checked_item["dt"] == "Service Request":
                service_request = frappe.get_doc("Service Request", checked_item["dn"])
                item_line.practitioner = service_request.practitioner

            if checked_item["dt"] == "Lab Test":
                lab_test = frappe.get_doc("Lab Test", checked_item["dn"])
                item_line.service_unit = lab_test.service_unit
                item_line.practitioner = lab_test.practitioner
                item_line.medical_department = lab_test.department

        self.set_missing_values(for_validate=True)

    @frappe.whitelist()
    def set_prescription_items(self, checked_values):

        for checked_item in checked_values:
            item_line = self.append("items", {})
            price_list, price_list_currency = frappe.db.get_values(
                "Price List", {"selling": 1}, ["name", "currency"]
            )[0]
            args = {
                "doctype": "Sales Invoice",
                "item_code": checked_item["item"],
                "company": self.company,
                "customer": frappe.db.get_value("Patient", self.patient, "customer"),
                "selling_price_list": price_list,
                "price_list_currency": price_list_currency,
                "plc_conversion_rate": 1.0,
                "conversion_rate": 1.0,
            }
            item_details = get_item_details(args)
            item_line.item_code = checked_item["item"]
            item_line.qty = 1
            if checked_item["qty"]:
                item_line.qty = checked_item["qty"]
            if checked_item["rate"]:
                item_line.rate = checked_item["rate"]
            else:
                item_line.rate = item_details.price_list_rate
            item_line.amount = float(item_line.rate) * float(item_line.qty)
            if checked_item["dt"]:
                item_line.reference_dt = checked_item["dt"]
            if checked_item["dn"]:
                item_line.reference_dn = checked_item["dn"]
            
            if checked_item["dt"] == "Medication Request":
                medication_request = frappe.get_doc("Medication Request", checked_item["dn"])
                item_line.practitioner = medication_request.practitioner

            if checked_item["description"]:
                item_line.description = checked_item["description"]

        self.set_missing_values(for_validate=True)
